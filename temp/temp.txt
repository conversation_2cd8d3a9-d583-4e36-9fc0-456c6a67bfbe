刚刚在主播信息页拨打电话出现闪退
025-08-29 16:00:05.747 13494-13494 com.score....ilActivity pid-13494                            D  status: Online
2025-08-29 16:00:06.270 13494-15295 com.score....nterceptor pid-13494                            W  [CURL] curl -X POST 'https://test-app.callmeso.com/language/translate/v2?key=AIzaSyCY4MncZdTNDX22qyoXBCndAsQALC0gFtU' --data '{"format":"text","q":"1","target":"zh"}'
2025-08-29 16:00:07.297 13494-13494 AndroidRuntime          pid-13494                            E  FATAL EXCEPTION: main
                                                                                                    Process: com.score.callmetest, PID: 13494
                                                                                                    java.lang.IllegalArgumentException: No enum constant com.score.callmetest.entity.RechargeSource.anchor_profile_video_call
                                                                                                            at java.lang.Enum.valueOf(Enum.java:302)
                                                                                                            at com.score.callmetest.entity.RechargeSource.valueOf(Unknown Source:2)
                                                                                                            at com.score.callmetest.ui.widget.InsufficientBalanceDialog.initViews(InsufficientBalanceDialog.kt:72)
                                                                                                            at com.score.callmetest.ui.widget.InsufficientBalanceDialog.onCreateView(InsufficientBalanceDialog.kt:55)
                                                                                                            at androidx.fragment.app.Fragment.performCreateView(Fragment.java:3119)
                                                                                                            at androidx.fragment.app.DialogFragment.performCreateView(DialogFragment.java:770)
                                                                                                            at androidx.fragment.app.FragmentStateManager.createView(FragmentStateManager.java:577)
                                                                                                            at androidx.fragment.app.FragmentStateManager.moveToExpectedState(FragmentStateManager.java:286)
                                                                                                            at androidx.fragment.app.FragmentManager.executeOpsTogether(FragmentManager.java:2214)
                                                                                                            at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(FragmentManager.java:2115)
                                                                                                            at androidx.fragment.app.FragmentManager.execPendingActions(FragmentManager.java:2052)
                                                                                                            at androidx.fragment.app.FragmentManager$5.run(FragmentManager.java:703)
                                                                                                            at android.os.Handler.handleCallback(Handler.java:958)
                                                                                                            at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                            at android.os.Looper.loopOnce(Looper.java:205)
                                                                                                            at android.os.Looper.loop(Looper.java:294)
                                                                                                            at android.app.ActivityThread.main(ActivityThread.java:8177)
                                                                                                            at java.lang.reflect.Method.invoke(Native Method)
                                                                                                            at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:552)
                                                                                                            at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:971)
2025-08-29 16:00:07.299 13494-13494 CrashHandler            pid-13494                            E  捕获到未处理异常，线程: main
                                                                                                    java.lang.IllegalArgumentException: No enum constant com.score.callmetest.entity.RechargeSource.anchor_profile_video_call
                                                                                                            at java.lang.Enum.valueOf(Enum.java:302)
                                                                                                            at com.score.callmetest.entity.RechargeSource.valueOf(Unknown Source:2)
                                                                                                            at com.score.callmetest.ui.widget.InsufficientBalanceDialog.initViews(InsufficientBalanceDialog.kt:72)
                                                                                                            at com.score.callmetest.ui.widget.InsufficientBalanceDialog.onCreateView(InsufficientBalanceDialog.kt:55)
                                                                                                            at androidx.fragment.app.Fragment.performCreateView(Fragment.java:3119)
                                                                                                            at androidx.fragment.app.DialogFragment.performCreateView(DialogFragment.java:770)
                                                                                                            at androidx.fragment.app.FragmentStateManager.createView(FragmentStateManager.java:577)
                                                                                                            at androidx.fragment.app.FragmentStateManager.moveToExpectedState(FragmentStateManager.java:286)
                                                                                                            at androidx.fragment.app.FragmentManager.executeOpsTogether(FragmentManager.java:2214)
                                                                                                            at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(FragmentManager.java:2115)
                                                                                                            at androidx.fragment.app.FragmentManager.execPendingActions(FragmentManager.java:2052)
                                                                                                            at androidx.fragment.app.FragmentManager$5.run(FragmentManager.java:703)
                                                                                                            at android.os.Handler.handleCallback(Handler.java:958)
                                                                                                            at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                            at android.os.Looper.loopOnce(Looper.java:205)
                                                                                                            at android.os.Looper.loop(Looper.java:294)
                                                                                                            at android.app.ActivityThread.main(ActivityThread.java:8177)
                                                                                                            at java.lang.reflect.Method.invoke(Native Method)
                                                                                                            at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:552)
                                                                                                            at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:971)
2025-08-29 16:00:07.300 13494-13494 System.err              pid-13494                            W  java.lang.IllegalArgumentException: No enum constant com.score.callmetest.entity.RechargeSource.anchor_profile_video_call
2025-08-29 16:00:07.300 13494-13494 System.err              pid-13494                            W          at com.score.callmetest.entity.RechargeSource.valueOf(Unknown Source:2)
2025-08-29 16:00:07.300 13494-13494 System.err              pid-13494                            W          at com.score.callmetest.ui.widget.InsufficientBalanceDialog.initViews(InsufficientBalanceDialog.kt:72)
2025-08-29 16:00:07.301 13494-13494 System.err              pid-13494                            W          at com.score.callmetest.ui.widget.InsufficientBalanceDialog.onCreateView(InsufficientBalanceDialog.kt:55)
2025-08-29 16:00:07.303 13494-13494 CrashHandler            pid-13494                            E  java.lang.IllegalArgumentException: No enum constant com.score.callmetest.entity.RechargeSource.anchor_profile_video_call
                                                                                                            at java.lang.Enum.valueOf(Enum.java:302)
                                                                                                            at com.score.callmetest.entity.RechargeSource.valueOf(Unknown Source:2)
                                                                                                            at com.score.callmetest.ui.widget.InsufficientBalanceDialog.initViews(InsufficientBalanceDialog.kt:72)
                                                                                                            at com.score.callmetest.ui.widget.InsufficientBalanceDialog.onCreateView(InsufficientBalanceDialog.kt:55)
                                                                                                            at androidx.fragment.app.Fragment.performCreateView(Fragment.java:3119)
                                                                                                            at androidx.fragment.app.DialogFragment.performCreateView(DialogFragment.java:770)
                                                                                                            at androidx.fragment.app.FragmentStateManager.createView(FragmentStateManager.java:577)
                                                                                                            at androidx.fragment.app.FragmentStateManager.moveToExpectedState(FragmentStateManager.java:286)
                                                                                                            at androidx.fragment.app.FragmentManager.executeOpsTogether(FragmentManager.java:2214)
                                                                                                            at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(FragmentManager.java:2115)
                                                                                                            at androidx.fragment.app.FragmentManager.execPendingActions(FragmentManager.java:2052)
                                                                                                            at androidx.fragment.app.FragmentManager$5.run(FragmentManager.java:703)
                                                                                                            at android.os.Handler.handleCallback(Handler.java:958)
                                                                                                            at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                            at android.os.Looper.loopOnce(Looper.java:205)
                                                                                                            at android.os.Looper.loop(Looper.java:294)
                                                                                                            at android.app.ActivityThread.main(ActivityThread.java:8177)
                                                                                                            at java.lang.reflect.Method.invoke(Native Method)
                                                                                                            at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:552)
                                                                                                            at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:971)
2025-08-29 16:00:07.330 13494-14910 System.out              pid-13494                            I  崩溃日志已保存: /storage/emulated/0/Android/data/com.score.callmetest/cache/logs/crash_2025-08-29.txt
2025-08-29 16:00:07.336 13494-13528 System.out              pid-13494                            I  崩溃日志已保存: /storage/emulated/0/Android/data/com.score.callmetest/cache/logs/crash_2025-08-29.txt
2025-08-29 16:00:08.430  1882-1923  ImeTracker              system_server                        I  com.score.callmetest:1fd2903a: onRequestHide at ORIGIN_SERVER_HIDE_INPUT reason HIDE_REMOVE_CLIENT
2025-08-29 16:00:08.431  1882-1923  ImeTracker              system_server                        I  com.score.callmetest:1fd2903a: onCancelled at PHASE_SERVER_SHOULD_HIDE
2025-08-29 16:00:08.431  1882-4059  ActivityManager         system_server                        I  Process com.score.callmetest (pid 13494) has died: fg  TOP
2025-08-29 16:00:08.433  1882-2488  ConnectivityService     system_server                        D  releasing NetworkRequest [ REQUEST id=18515, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10397 RequestorUid: 10397 RequestorPkg: com.score.callmetest UnderlyingNetworks: Null] ] (release request)
2025-08-29 16:00:08.436  1882-3903  WindowManager           system_server                        I  WIN DEATH: Window{1791a27 u0 com.score.callmetest/com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity}
2025-08-29 16:00:08.436  1882-3903  InputManager-JNI        system_server                        W  Input channel object '1791a27 com.score.callmetest/com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity (client)' was disposed without first being removed with the input manager!
2025-08-29 16:00:08.454  1882-2139  WindowManager           system_server                        W  Failed to deliver inset control state change to w=Window{d708572 u0 com.score.callmetest/com.score.callmetest.ui.main.MainActivity}
                                                                                                    android.os.DeadObjectException
                                                                                                            at android.os.BinderProxy.transactNative(Native Method)
                                                                                                            at android.os.BinderProxy.transact(BinderProxy.java:584)
                                                                                                            at android.view.IWindow$Stub$Proxy.insetsControlChanged(IWindow.java:479)
                                                                                                            at com.android.server.wm.WindowState.notifyInsetsControlChanged(WindowState.java:3802)
                                                                                                            at com.android.server.wm.InsetsStateController.lambda$notifyPendingInsetsControlChanged$3(InsetsStateController.java:343)
                                                                                                            at com.android.server.wm.InsetsStateController.$r8$lambda$8yykPRG1GyNq_J17QvL9d5xANMc(InsetsStateController.java:0)
                                                                                                            at com.android.server.wm.InsetsStateController$$ExternalSyntheticLambda2.run(R8$$SyntheticClass:0)
                                                                                                            at com.android.server.wm.WindowAnimator.executeAfterPrepareSurfacesRunnables(WindowAnimator.java:294)
                                                                                                            at com.android.server.wm.RootWindowContainer.performSurfacePlacementNoTrace(RootWindowContainer.java:811)
                                                                                                            at com.android.server.wm.RootWindowContainer.performSurfacePlacement(RootWindowContainer.java:756)
                                                                                                            at com.android.server.wm.WindowSurfacePlacer.performSurfacePlacementLoop(WindowSurfacePlacer.java:177)
                                                                                                            at com.android.server.wm.WindowSurfacePlacer.performSurfacePlacement(WindowSurfacePlacer.java:126)
                                                                                                            at com.android.server.wm.WindowSurfacePlacer.performSurfacePlacement(WindowSurfacePlacer.java:115)
                                                                                                            at com.android.server.wm.WindowSurfacePlacer$Traverser.run(WindowSurfacePlacer.java:57)
                                                                                                            at android.os.Handler.handleCallback(Handler.java:958)
                                                                                                            at android.os.Handler.dispatchMessage(Handler.java:99)