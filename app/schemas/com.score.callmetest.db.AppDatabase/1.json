{"formatVersion": 1, "database": {"version": 1, "identityHash": "0bc5420e6d3644e9bc216a859cc0c5c2", "entities": [{"tableName": "message_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `currentUserId` TEXT NOT NULL, `userName` TEXT NOT NULL, `gender` INTEGER NOT NULL, `unitPrice` INTEGER NOT NULL, `avatar` TEXT NOT NULL, `avatarThumbUrl` TEXT NOT NULL, `lastMessage` TEXT NOT NULL, `lastMessageType` TEXT NOT NULL, `timestamp` TEXT NOT NULL, `timeInMillis` INTEGER NOT NULL, `unreadCount` INTEGER NOT NULL, `onlineStatus` TEXT NOT NULL, `isPinned` INTEGER NOT NULL, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "currentUserId", "columnName": "currentUserId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userName", "columnName": "userName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "gender", "columnName": "gender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "unitPrice", "columnName": "unitPrice", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": true}, {"fieldPath": "avatarThumbUrl", "columnName": "avatarThumbUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastMessage", "columnName": "lastMessage", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastMessageType", "columnName": "lastMessageType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeInMillis", "columnName": "timeInMillis", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "unreadCount", "columnName": "unreadCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineStatus", "columnName": "onlineStatus", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isPinned", "columnName": "isPinned", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}}, {"tableName": "call_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `userId` TEXT NOT NULL, `currentUserId` TEXT NOT NULL, `userName` TEXT NOT NULL, `avatar` TEXT NOT NULL, `unitPrice` INTEGER NOT NULL, `callType` TEXT NOT NULL, `callDuration` INTEGER NOT NULL, `callEndTime` INTEGER NOT NULL, `hasVideo` INTEGER NOT NULL, `onlineStatus` TEXT NOT NULL, `isPinned` INTEGER NOT NULL, `isBottomView` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "currentUserId", "columnName": "currentUserId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userName", "columnName": "userName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": true}, {"fieldPath": "unitPrice", "columnName": "unitPrice", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "callType", "columnName": "callType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "callDuration", "columnName": "callDuration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "callEndTime", "columnName": "callEndTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hasVideo", "columnName": "hasVideo", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineStatus", "columnName": "onlineStatus", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isPinned", "columnName": "isPinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isBottomView", "columnName": "isBottomView", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '0bc5420e6d3644e9bc216a859cc0c5c2')"]}}