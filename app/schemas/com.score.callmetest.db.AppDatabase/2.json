{"formatVersion": 1, "database": {"version": 2, "identityHash": "bfd485afa7a172d917c9d932b928f147", "entities": [{"tableName": "message_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `currentUserId` TEXT NOT NULL, `userName` TEXT NOT NULL, `gender` INTEGER NOT NULL, `unitPrice` INTEGER NOT NULL, `avatar` TEXT NOT NULL, `avatarThumbUrl` TEXT NOT NULL, `lastMessage` TEXT NOT NULL, `lastMessageType` TEXT NOT NULL, `timestamp` TEXT NOT NULL, `timeInMillis` INTEGER NOT NULL, `unreadCount` INTEGER NOT NULL, `onlineStatus` TEXT NOT NULL, `isPinned` INTEGER NOT NULL, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "currentUserId", "columnName": "currentUserId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userName", "columnName": "userName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "gender", "columnName": "gender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "unitPrice", "columnName": "unitPrice", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": true}, {"fieldPath": "avatarThumbUrl", "columnName": "avatarThumbUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastMessage", "columnName": "lastMessage", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastMessageType", "columnName": "lastMessageType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeInMillis", "columnName": "timeInMillis", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "unreadCount", "columnName": "unreadCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineStatus", "columnName": "onlineStatus", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isPinned", "columnName": "isPinned", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}}, {"tableName": "call_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `userId` TEXT NOT NULL, `currentUserId` TEXT NOT NULL, `userName` TEXT NOT NULL, `avatar` TEXT NOT NULL, `unitPrice` INTEGER NOT NULL, `callType` TEXT NOT NULL, `callDuration` INTEGER NOT NULL, `callEndTime` INTEGER NOT NULL, `hasVideo` INTEGER NOT NULL, `onlineStatus` TEXT NOT NULL, `isPinned` INTEGER NOT NULL, `isBottomView` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "currentUserId", "columnName": "currentUserId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userName", "columnName": "userName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": true}, {"fieldPath": "unitPrice", "columnName": "unitPrice", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "callType", "columnName": "callType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "callDuration", "columnName": "callDuration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "callEndTime", "columnName": "callEndTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hasVideo", "columnName": "hasVideo", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "onlineStatus", "columnName": "onlineStatus", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isPinned", "columnName": "isPinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isBottomView", "columnName": "isBottomView", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "chat_messages", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`messageId` TEXT NOT NULL, `rcMsgId` TEXT, `currentUserId` TEXT NOT NULL, `senderId` TEXT NOT NULL, `senderName` TEXT NOT NULL, `senderAvatar` TEXT NOT NULL, `receiverId` TEXT NOT NULL, `content` TEXT NOT NULL, `contentType` TEXT, `messageType` TEXT NOT NULL, `status` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `progress` INTEGER NOT NULL, `isCurrentUser` INTEGER NOT NULL, `thumbUri` TEXT, `mediaUri` TEXT, `mediaLocalUri` TEXT, `mediaDuration` INTEGER NOT NULL, `giftInfo` TEXT, `isAutoTrans` INTEGER NOT NULL, `extra` TEXT, PRIMARY KEY(`messageId`))", "fields": [{"fieldPath": "messageId", "columnName": "messageId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "rcMsgId", "columnName": "rcMsgId", "affinity": "TEXT"}, {"fieldPath": "currentUserId", "columnName": "currentUserId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "senderId", "columnName": "senderId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sender<PERSON>ame", "columnName": "sender<PERSON>ame", "affinity": "TEXT", "notNull": true}, {"fieldPath": "senderAvatar", "columnName": "senderAvatar", "affinity": "TEXT", "notNull": true}, {"fieldPath": "receiverId", "columnName": "receiverId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "contentType", "columnName": "contentType", "affinity": "TEXT"}, {"fieldPath": "messageType", "columnName": "messageType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "progress", "columnName": "progress", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isCurrentUser", "columnName": "isCurrentUser", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON>", "affinity": "TEXT"}, {"fieldPath": "mediaUri", "columnName": "mediaUri", "affinity": "TEXT"}, {"fieldPath": "mediaLocalUri", "columnName": "mediaLocalUri", "affinity": "TEXT"}, {"fieldPath": "mediaDuration", "columnName": "mediaDuration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "giftInfo", "columnName": "giftInfo", "affinity": "TEXT"}, {"fieldPath": "isAutoTrans", "columnName": "isAutoTrans", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "extra", "columnName": "extra", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["messageId"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'bfd485afa7a172d917c9d932b928f147')"]}}