<resources>
    <!--卡片布局相关-->
    <attr name="cardViewStyle" format="reference" />
    <declare-styleable name="CardView">
        <attr name="cardBackgroundColor" format="color" />
        <attr name="cardCornerRadius" format="dimension" />
        <attr name="cardElevation" format="dimension" />
        <attr name="cardMaxElevation" format="dimension" />
        <attr name="cardUseCompatPadding" format="boolean" />
        <attr name="cardPreventCornerOverlap" format="boolean" />
        <attr name="contentPadding" format="dimension" />
        <attr name="contentPaddingLeft" format="dimension" />
        <attr name="contentPaddingRight" format="dimension" />
        <attr name="contentPaddingTop" format="dimension" />
        <attr name="contentPaddingBottom" format="dimension" />
        <attr name="cardShadowColorStart" format="color" />
        <attr name="cardShadowColorEnd" format="color" />
        <attr name="android:minWidth" />
        <attr name="android:minHeight" />
    </declare-styleable>

    <declare-styleable name="RoundIconButton">
        <attr name="iconSrc" format="reference" />
        <attr name="bgColor" format="color" />
        <!-- 新增的描边属性 -->
        <attr name="strokeWidth" format="dimension" />
        <attr name="strokeColor" format="color" />
    </declare-styleable>

    <declare-styleable name="MineFunctionItemView">
        <attr name="mfi_icon" format="reference" />
        <attr name="mfi_title" format="string" />
        <attr name="mfi_tip" format="string" />
        <attr name="mfi_show_arrow" format="boolean" />
    </declare-styleable>

    <!-- 用于View扩展函数的ID -->
    <item name="click_time_tag" type="id" />
</resources>