<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 弹出菜单动画样式 -->
    <style name="PopupAnimation">
        <item name="android:windowEnterAnimation">@anim/popup_enter</item>
        <item name="android:windowExitAnimation">@anim/popup_exit</item>
    </style>
    <!-- switch禁用 Ripple 效果 -->
    <style name="NoRippleSwitch" parent="Widget.AppCompat.CompoundButton.Switch">
        <item name="android:background">@null</item>
        <item name="android:foreground">@null</item>
    </style>
    <!--卡片布局相关-->
    <style name="Base.CardView" parent="android:Widget">
        <item name="cardCornerRadius">@dimen/cardview_default_radius</item>
        <item name="cardElevation">@dimen/cardview_default_elevation</item>
        <item name="cardMaxElevation">@dimen/cardview_default_elevation</item>
        <item name="cardUseCompatPadding">false</item>
        <item name="cardPreventCornerOverlap">true</item>
    </style>

    <style name="CardView" parent="Base.CardView" />

    <style name="CardView.Dark">
        <item name="cardBackgroundColor">@color/cardview_dark_background</item>
    </style>

    <style name="CardView.Light">
        <item name="cardBackgroundColor">@color/cardview_light_background</item>
    </style>
</resources>