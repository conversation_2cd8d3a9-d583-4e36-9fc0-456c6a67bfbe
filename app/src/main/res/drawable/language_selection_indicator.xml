<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/black" />
            <corners android:radius="2dp" />
            <size android:width="20dp" android:height="20dp" />
        </shape>
    </item>
    <item android:state_selected="false">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <stroke android:width="2dp" android:color="@color/gray_ccc" />
            <corners android:radius="2dp" />
            <size android:width="20dp" android:height="20dp" />
        </shape>
    </item>
</selector>
