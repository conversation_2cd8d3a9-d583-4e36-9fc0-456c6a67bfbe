<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!-- 主要弹窗内容 -->
    <FrameLayout
        android:id="@+id/check_in_content_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="23dp"
        android:background="@drawable/bg_dialog_rounded_check_in"
        app:layout_constrainedHeight="true"
        app:layout_constraintBottom_toTopOf="@id/vip_privilege_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed">

        <!-- 关闭按钮 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/btn_close"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_gravity="top|end"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="13dp"
            android:padding="2dp"
            android:scaleType="fitCenter"
            android:src="@drawable/promotion_dialog_cancel" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="18dp"
            android:paddingTop="30dp"
            android:paddingBottom="24dp">

            <!-- 标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/futura_black_bold"
                android:gravity="center"
                android:text="@string/check_in_for_7_days"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/futura_black_bold"
                android:gravity="center"
                android:text="@string/continuously"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- 签到日期网格 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_check_in_days"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:nestedScrollingEnabled="false"
                android:overScrollMode="never"
                tools:itemCount="7"
                tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                tools:listitem="@layout/item_check_in_day"
                tools:spanCount="4" />

            <!-- 签到按钮 -->
            <com.score.callmetest.ui.widget.AlphaSVGAImageView
                android:id="@+id/btn_check_in"
                android:layout_width="match_parent"
                android:layout_height="51dp"
                android:layout_marginTop="25dp"
                android:visibility="visible" />

            <!-- 已签到按钮 -->
            <TextView
                android:id="@+id/btn_checked_in"
                android:layout_width="match_parent"
                android:layout_height="51dp"
                android:layout_marginTop="25dp"
                android:background="@drawable/bg_checked_in_btn"
                android:fontFamily="@font/roboto_black"
                android:gravity="center"
                android:text="@string/str_see_you_tomorrow"
                android:textColor="@color/checked_in_brown"
                android:textSize="16sp"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>
    </FrameLayout>

    <!-- 背景装饰 -->
    <ImageView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="-10dp"
        android:scaleType="center"
        android:src="@drawable/bg_check_in_top"
        app:layout_constraintBottom_toTopOf="@id/check_in_content_container"
        app:layout_constraintEnd_toEndOf="@id/check_in_content_container"
        app:layout_constraintStart_toStartOf="@id/check_in_content_container" />

    <!-- VIP特权按钮 -->
    <LinearLayout
        android:id="@+id/vip_privilege_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:background="@drawable/bg_check_in_vip"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="12dp"
        app:layout_constraintEnd_toEndOf="@id/check_in_content_container"
        app:layout_constraintStart_toStartOf="@id/check_in_content_container"
        app:layout_constraintTop_toBottomOf="@id/check_in_content_container">

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/icon_check_in_vip" />

        <TextView
            android:id="@+id/tv_vip_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            android:layout_weight="1"
            android:fontFamily="@font/roboto_bold"
            android:text="@string/become_vip_privileges"
            android:textColor="@color/check_in_brown"
            android:textSize="14sp"
            tools:text="123233232323232323232323232323232323232323" />

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:scaleType="center"
            android:src="@drawable/btn_check_in_go" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
