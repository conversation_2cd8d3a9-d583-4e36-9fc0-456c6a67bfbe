<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false">

    <com.score.callmetest.ui.cardview.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="8dp"
        app:cardPreventCornerOverlap="true"
        app:cardShadowColorEnd="#03fd0000"
        app:cardShadowColorStart="#42ff59a7"
        app:cardUseCompatPadding="true"
        app:cardBackgroundColor="@android:color/white">

        <LinearLayout
            android:id="@+id/popup_country_filter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="19dp"
            android:paddingTop="10dp"
            android:paddingBottom="8dp"
            android:paddingEnd="5dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_countries"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never" />

        </LinearLayout>
    </com.score.callmetest.ui.cardview.CardView>
</FrameLayout>