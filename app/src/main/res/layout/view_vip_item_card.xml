<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingTop="14dp"
    android:paddingBottom="8dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:lineHeight="15dp"
        android:text="1 month"
        android:textColor="@android:color/white"
        android:textSize="13sp"
        android:textStyle="bold" />

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginBottom="8dp"
        android:src="@drawable/vip_item_icon_1" />

    <TextView
        android:id="@+id/tv_bonus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:lineHeight="18dp"
        android:text="+30000"
        android:textColor="#FFFFD700"
        android:textSize="15sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:lineHeight="15dp"
        android:text="$9.99"
        android:textColor="@android:color/white"
        android:textSize="13sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_weekly_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:lineHeight="13dp"
        android:text="$2.25/week"
        android:textColor="@android:color/white"
        android:textSize="11sp" />

</LinearLayout>

