<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_broadcaster_gift">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:id="@+id/ll_top_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:paddingVertical="6dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 返回按钮 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/iv_back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/info_btn_back" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/gifts_received"
            android:textColor="#fff"
            android:textSize="18sp"
           android:fontFamily="@font/roboto_medium"
            android:gravity="center" />

        <!-- 占位空间，保持标题居中 -->
        <View
            android:layout_width="32dp"
            android:layout_height="32dp" />

    </LinearLayout>
    <!-- 礼物网格列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_gift_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:overScrollMode="never"
        android:layout_marginTop="230dp"
        android:layout_marginBottom="10dp"
        android:paddingHorizontal="21dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_top_bar"
        tools:listitem="@layout/item_broadcaster_detail_gift"/>

</androidx.constraintlayout.widget.ConstraintLayout> 