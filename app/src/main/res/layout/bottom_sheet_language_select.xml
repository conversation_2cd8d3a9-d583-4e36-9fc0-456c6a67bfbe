<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingBottom="24dp">

    <!-- 顶部标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="18dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:gravity="center_vertical"
            android:text="@string/cancel"
            android:textStyle="bold"
            android:textColor="@color/black_50"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:gravity="center_vertical"
            android:text="@string/confirm"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <!-- 语言列表容器 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_language_list"
        android:layout_width="match_parent"
        android:layout_height="@dimen/language_list_max_height"
        android:overScrollMode="never"
        android:clipToPadding="false" />

</LinearLayout> 