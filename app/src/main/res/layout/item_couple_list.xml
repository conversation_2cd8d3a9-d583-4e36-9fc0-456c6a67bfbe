<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/line_rank_mine"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    android:orientation="horizontal"
    android:paddingStart="24dp">

    <TextView
        android:id="@+id/tv_mine_rank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:fontFamily="@font/roboto_bold"
        android:textColor="#FF8C00"
        android:textSize="18sp"
        android:textStyle="bold"
        tools:text="24" />
    <!-- 头像 -->
    <com.score.callmetest.ui.widget.CircleIconButton
        android:id="@+id/tv_couple_avatar1"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:layout_marginStart="13dp"
        android:scaleType="centerCrop"
        tools:src="@drawable/placeholder" />
    <!-- 头像 -->
    <com.score.callmetest.ui.widget.CircleIconButton
        android:id="@+id/tv_couple_avatar2"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:layout_marginStart="-28dp"
        android:scaleType="centerCrop"
        tools:src="@drawable/placeholder" />

    <TextView
        android:id="@+id/tv_mine_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_gravity="center"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="10dp"
        android:fontFamily="@font/roboto_regular"
        android:textColor="#42424D"
        android:textSize="14sp"
        tools:text="Rosa &amp; Rosemary" />


</LinearLayout>