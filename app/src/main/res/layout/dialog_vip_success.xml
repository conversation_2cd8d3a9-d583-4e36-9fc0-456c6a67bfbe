<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="308dp"
        android:layout_marginHorizontal="25dp"
        android:layout_marginTop="70dp"
        android:background="@drawable/vip_success_bg">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginHorizontal="71dp"
            android:gravity="center"
            android:lineHeight="22dp"
            android:paddingTop="50dp"
            android:text="@string/vip_success"
            android:textColor="#FFECD3"
            android:textSize="18sp"
            android:textStyle="bold"
            android:typeface="sans" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="33dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/clock" />

            <TextView
                android:id="@+id/expire_date_text"
                android:layout_width="wrap_content"
                android:textSize="13sp"
                android:textColor="#FFECD3"
                android:layout_height="wrap_content"/>

        </LinearLayout>

        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/close"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:padding="15dp"
            android:src="@drawable/promotion_dialog_cancel"/>
    </RelativeLayout>
    
    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/icon_svga"
        android:layout_width="375dp"
        android:layout_height="265dp"
        android:layout_gravity="center_horizontal"/>

</FrameLayout>