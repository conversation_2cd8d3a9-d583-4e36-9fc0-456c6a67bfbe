<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_mine_function_item_selector">

    <!-- 头像 -->
    <com.score.callmetest.ui.widget.CircleIconButton
        android:id="@+id/iv_avatar"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:layout_marginVertical="12dp"
        android:layout_marginStart="15dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:iconSrc="@drawable/placeholder" />

    <!-- 用户名 -->
    <TextView
        android:id="@+id/tv_username"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/black"
        android:layout_marginEnd="10dp"
        android:textSize="15sp"
        android:fontFamily="@font/roboto_medium"
        android:textStyle="bold"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintBottom_toTopOf="@id/item_country"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        app:layout_constraintEnd_toStartOf="@id/line_add_follow"
        app:layout_constraintVertical_chainStyle="spread"
        tools:text="Nadine" />

    <!-- 国家 -->
    <LinearLayout
        android:id="@+id/item_country"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:clickable="false"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintStart_toStartOf="@id/tv_username"
        app:layout_constraintTop_toBottomOf="@id/tv_username">

        <ImageView
            android:id="@+id/iv_flag"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_gravity="center_vertical"
            android:scaleType="fitCenter"
            android:src="@drawable/all" />

        <TextView
            android:id="@+id/tv_region"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="13sp"
            android:fontFamily="@font/roboto_regular"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="12dp"
            android:textColor="@color/cuntry_blocklist"
            android:gravity="center"
            android:layout_gravity="center_vertical"
            android:text="All"
            android:maxLines="1" />

    </LinearLayout>
    <LinearLayout
        android:id="@+id/line_add_follow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="18dp"
        android:gravity="center"
        android:paddingHorizontal="15dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" >
        <ImageView
            android:id="@+id/image_add"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/add" />

        <!-- 关注标识 -->
        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/tv_add_follow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textSize="13sp"
            android:textStyle="bold"
            android:textColor="#FF3BB0"
            android:text="@string/follow"
            android:paddingVertical="5dp" />

    </LinearLayout>




</androidx.constraintlayout.widget.ConstraintLayout>