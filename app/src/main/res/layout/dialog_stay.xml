<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="85dp"
        android:background="@drawable/bg_dialog_rounded">

        <ImageView
            android:id="@+id/dialog_bg"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@drawable/login_dialog_bg" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="29dp"
            android:paddingTop="148dp"
            android:paddingBottom="22dp">

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="14dp"
                android:layout_marginHorizontal="20dp"
                android:fontFamily="@font/lte53511"
                android:gravity="center"
                android:text="@string/stay_content"
                android:textColor="#A94A20"
                android:textSize="16sp"
                android:textStyle="bold" />


            <!-- 确认支付按钮 -->
            <FrameLayout
                android:id="@+id/frame_continue"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                android:orientation="horizontal"
                android:gravity="center"
                android:visibility="visible">

                <com.score.callmetest.ui.widget.AlphaSVGAImageView
                    android:id="@+id/svga_continue"
                    android:layout_width="match_parent"
                    android:layout_height="58dp"
                    android:layout_gravity="center_horizontal"
                    android:scaleType="fitCenter" />

                <LinearLayout
                    android:id="@+id/line_continue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:baselineAligned="true">

                    <TextView
                        android:id="@+id/tv_continue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/continue_to_recharge"
                        android:textColor="#9A2525"
                        android:textSize="15sp"
                        android:fontFamily="@font/roboto_bold"
                        android:textStyle="bold" />
                    <TextView
                        android:id="@+id/tv_countdown"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textAlignment="center"
                        android:layout_marginStart="5dp"
                        android:text="(5s)"
                        android:textColor="#FF0000"
                        android:textSize="15sp"
                        android:fontFamily="@font/roboto_bold"
                        android:textStyle="bold" />
                </LinearLayout>
            </FrameLayout>

            <com.score.callmetest.ui.widget.AlphaTextView
                android:id="@+id/tv_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#8A8A8A"
                android:textSize="13sp"
                android:layout_marginTop="15dp"
                android:layout_gravity="center_horizontal"
                android:fontFamily="@font/roboto_regular"
                android:text="@string/leave_with_regret"/>
        </LinearLayout>
    </FrameLayout>

    <ImageView
        android:id="@+id/emoji"
        android:layout_width="wrap_content"
        android:layout_height="222dp"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/pic1" />
</FrameLayout>