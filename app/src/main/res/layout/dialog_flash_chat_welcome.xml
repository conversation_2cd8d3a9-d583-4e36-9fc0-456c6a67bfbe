<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="-30dp"
        android:background="@drawable/flash_welcome_dialog_bg">

        <TextView
            android:id="@+id/welcome"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_marginHorizontal="41dp"
            android:layout_marginTop="106dp"
            android:fontFamily="@font/futura_black_bold"
            android:lineHeight="18dp"
            android:text="@string/welcome_you"
            android:textColor="@android:color/white"
            android:textFontWeight="13"
            android:textSize="21sp"
            android:textStyle="bold"
            android:typeface="sans" />

        <LinearLayout
            android:id="@+id/content_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="42dp"
            android:layout_marginTop="141dp"
            android:layout_marginBottom="17dp"
            android:background="@drawable/bg_dialog_rounded"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:padding="25dp">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/congratulations"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="30dp"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:textColor="#746E6E"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="95dp"
                android:layout_height="95dp"
                android:layout_marginTop="14dp"
                android:src="@drawable/match_card_gold" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="15dp">

                <TextView
                    android:id="@+id/btn_match"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:gravity="center"
                    android:paddingVertical="16dp"
                    android:text="@string/match_now"
                    android:textColor="@android:color/white"
                    android:textSize="17sp"
                    android:textStyle="bold"
                    android:typeface="sans" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="top|end"
                    android:background="@drawable/match_dialog_free_bg"
                    android:paddingHorizontal="11dp"
                    android:paddingTop="3dp"
                    android:paddingBottom="5dp"
                    android:text="@string/free"
                    android:textColor="@android:color/white"
                    android:textSize="12sp"
                    android:textStyle="bold" />
            </FrameLayout>
        </LinearLayout>

    </FrameLayout>
</FrameLayout>