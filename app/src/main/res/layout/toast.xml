<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:paddingVertical="7dp"
    android:layout_height="wrap_content"
    android:background="@color/toast_bg">

    <!-- 图标 -->
    <ImageView
        android:id="@+id/image_view_logo"
        android:layout_width="21dp"
        android:layout_height="21dp"
        android:layout_marginStart="15dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/logo" />

    <!-- toast -->
    <TextView
        android:id="@+id/tv_toast"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="11dp"
        android:layout_marginEnd="20dp"
        android:ellipsize="end"
        android:textColor="@color/black"
        android:textSize="13sp"
        android:textStyle="bold"
        android:maxWidth="300dp"
        android:textAlignment="viewStart"
        android:gravity="start|center_vertical"
        app:layout_constrainedWidth="true"
        app:layout_constraintStart_toEndOf="@id/image_view_logo"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="Delete SuccessDelete SuccessDeletDelete Selete SuccessDelete SuccessDelete Success" />


</androidx.constraintlayout.widget.ConstraintLayout>