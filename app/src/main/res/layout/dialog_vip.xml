<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/icon_view"
        android:layout_width="260dp"
        android:layout_height="85dp"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/vip_dialog_light" />

    <RelativeLayout
        android:id="@+id/dialog_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="85dp"
        android:background="@drawable/vip_dialog_bg"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/card_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="21dp"
            android:layout_marginTop="36dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <!-- 1个月订阅卡片 -->
            <com.score.callmetest.ui.widget.VipItemCardView
                android:id="@+id/vip_card_1month"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Space
                android:layout_width="7dp"
                android:layout_height="2dp" />

            <!-- 3个月订阅卡片 -->
            <com.score.callmetest.ui.widget.VipItemCardView
                android:id="@+id/vip_card_3month"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Space
                android:layout_width="7dp"
                android:layout_height="2dp" />
            <!-- 12个月订阅卡片 -->
            <com.score.callmetest.ui.widget.VipItemCardView
                android:id="@+id/vip_card_12month"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/content_layout_parent"
            android:layout_below="@+id/card_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingHorizontal="25dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/bg_dialog_rounded"
            android:paddingBottom="21dp">

            <LinearLayout
                android:id="@+id/info_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="23dp"
                    android:layout_marginEnd="6dp"
                    android:src="@drawable/vip_dialog_icon" />

                <TextView
                    android:id="@+id/vip_desc_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_medium"
                    android:lineHeight="18dp"
                    android:text="@string/become_vip_privileges"
                    android:textColor="#FF5A0A0A"
                    android:textSize="15sp"
                    android:typeface="sans" />
            </LinearLayout>
            <!--分割线-->
            <ImageView
                android:id="@+id/divide_line"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:paddingHorizontal="6dp"
                android:src="@drawable/dividing_line"
                android:layout_below="@+id/info_layout"/>

            <ScrollView
                android:id="@+id/main_content_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/divide_line"
                android:layout_marginTop="8dp"
                android:fillViewport="true"
                android:overScrollMode="never"
                android:scrollbars="none">

                <LinearLayout
                    android:id="@+id/desc_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    android:paddingBottom="68dp">

                    <!-- VIP特权描述项 -->
                    <com.score.callmetest.ui.widget.VipDescItemView
                        android:id="@+id/vip_desc_coins_bonus"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp" />

                    <com.score.callmetest.ui.widget.VipDescItemView
                        android:id="@+id/vip_desc_recharge_bonus"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="17dp"
                        android:visibility="gone" />

                    <com.score.callmetest.ui.widget.VipDescItemView
                        android:id="@+id/vip_desc_call_discount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="17dp" />

                    <com.score.callmetest.ui.widget.VipDescItemView
                        android:id="@+id/vip_desc_match_call"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="17dp"
                        android:visibility="gone" />

                    <com.score.callmetest.ui.widget.VipDescItemView
                        android:id="@+id/vip_desc_text_chat"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="17dp" />

                    <com.score.callmetest.ui.widget.VipDescItemView
                        android:id="@+id/vip_desc_photo_album"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="17dp" />

                    <com.score.callmetest.ui.widget.VipDescItemView
                        android:id="@+id/vip_desc_visitor_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="17dp" />

                    <com.score.callmetest.ui.widget.VipDescItemView
                        android:id="@+id/vip_desc_status"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="17dp" />
                </LinearLayout>
            </ScrollView>

            <View
                android:id="@+id/bottom_shadow"
                android:layout_width="match_parent"
                android:layout_height="111dp"
                android:layout_alignParentBottom="true" />
        </RelativeLayout>

        <com.score.callmetest.ui.widget.AlphaFrameLayout
            android:id="@+id/vip_button_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginHorizontal="33dp"
            android:paddingBottom="21dp">

            <TextView
                android:id="@+id/vip_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/vip_dialog_button_bg"
                android:gravity="center"
                android:lineHeight="18dp"
                android:paddingTop="15dp"
                android:paddingBottom="19dp"
                android:text="@string/become_vip_now"
                android:textColor="#FF9A2525"
                android:textSize="15sp"
                android:textStyle="bold" />
        </com.score.callmetest.ui.widget.AlphaFrameLayout>
    </RelativeLayout>

    <ImageView
        android:layout_width="88dp"
        android:layout_height="68dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="39dp"
        android:src="@drawable/vip_dialog_icon" />

</FrameLayout>