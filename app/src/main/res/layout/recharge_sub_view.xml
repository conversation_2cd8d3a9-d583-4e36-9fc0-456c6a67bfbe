<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <!-- 充值选项Grid -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_recharge_options"
        android:layout_width="match_parent"
        android:overScrollMode="never"
        android:layout_height="wrap_content" />



    <!-- 确认支付按钮 -->
    <FrameLayout
        android:id="@+id/frame_continue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="26dp"
        android:layout_marginBottom="24dp"
        android:layout_centerHorizontal="true"
        android:layout_below="@+id/rv_recharge_options"
        android:orientation="horizontal"
        android:gravity="center"
        android:visibility="visible">


        <com.score.callmetest.ui.widget.AlphaSVGAImageView
            android:id="@+id/svga_continue"
            android:layout_width="match_parent"
            android:layout_marginHorizontal="42dp"
            android:layout_height="51dp"
            android:layout_gravity="center_horizontal"
            android:scaleType="fitCenter" />

        <LinearLayout
            android:id="@+id/line_continue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_gravity="center"
            android:baselineAligned="true">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/continue_space"
                android:textColor="#9A2525"
                android:textSize="15sp"
                android:textStyle="bold"
                android:fontFamily="@font/roboto_bold"/>
            <TextView
                android:id="@+id/tv_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="($1.99)"
                android:textColor="#9A2525"
                android:textSize="13sp"
                android:fontFamily="@font/roboto_regular"/>

        </LinearLayout>


    </FrameLayout>

    <!-- 更多选项 -->
    <!--<com.score.callmetest.ui.widget.AlphaLinearLayout
        android:id="@+id/more_option_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/rv_recharge_options"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_more_option"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/more_option"
            android:textColor="@android:color/white"
            android:textSize="13sp" />

        <ImageView
            android:id="@+id/tv_more_option_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:layout_marginTop="2dp"
            android:src="@drawable/arrow_down"/>
    </com.score.callmetest.ui.widget.AlphaLinearLayout>-->

</RelativeLayout>