<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_mine_function_item_selector">

    <!-- 头像 -->
    <com.score.callmetest.ui.widget.CircleIconButton
        android:id="@+id/iv_avatar"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:layout_marginVertical="12dp"
        android:layout_marginStart="15dp"
        android:clickable="true"
        android:focusable="true"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/placeholder" />

    <!-- 在线状态指示器 -->
    <View
        android:id="@+id/status_indicator"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_marginEnd="3dp"
        android:layout_marginBottom="3dp"
        android:background="@drawable/shape_online_status"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar"
        tools:visibility="visible" />

    <!-- 用户名 -->
    <TextView
        android:id="@+id/tv_username"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:fontFamily="@font/roboto_bold"
        android:maxLines="1"
        android:textColor="@color/black"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/item_country"
        app:layout_constraintEnd_toStartOf="@+id/iv_video_indicator"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        tools:text="Kathleen" />


    <LinearLayout
        android:id="@+id/item_last_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="false"
        android:focusable="false"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintStart_toStartOf="@id/tv_username"
        app:layout_constraintTop_toBottomOf="@id/tv_username">

        <TextView
            android:id="@+id/last_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#707278"
            android:textSize="13sp" />
    </LinearLayout>


    <!-- 视频通话按钮 -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/iv_video_indicator"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_marginEnd="18dp"
        android:scaleType="fitCenter"
        android:src="@drawable/btn_message"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
