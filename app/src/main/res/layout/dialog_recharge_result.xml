<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="@drawable/bg_dialog_rounded"
    android:padding="20dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!-- 图标 -->
    <ImageView
        android:id="@+id/iv_result_icon"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="16dp"/>

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/recharge_success"
        android:textSize="18sp"
        android:textColor="#000"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="8dp"/>

    <!-- 消息 -->
    <TextView
        android:id="@+id/tv_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Your coins have been added to your account successfully!"
        android:textSize="14sp"
        android:textColor="#666"
        android:gravity="center"
        android:layout_marginBottom="20dp"/>

    <!-- 订单号 -->
    <TextView
        android:id="@+id/tv_order_no"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Order: *********"
        android:textSize="12sp"
        android:textColor="#999"
        android:gravity="center"
        android:layout_marginBottom="20dp"/>

    <!-- 确认按钮 -->
    <Button
        android:id="@+id/btn_ok"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="OK"
        android:textColor="#FFF"
        android:background="@drawable/bg_btn_rounded_blue"
        android:padding="12dp"/>

</LinearLayout> 