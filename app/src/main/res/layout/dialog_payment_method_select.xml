<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/dialog_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_top_bg"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="20dp"
        android:paddingTop="20dp">

        <!-- 标题栏 -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/roboto_medium"
            android:gravity="center"
            android:text="@string/str_payment_method_choose"
            android:textColor="@color/black"
            android:textSize="15sp"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent" />


        <!-- 商品信息卡片 -->
        <LinearLayout
            android:id="@+id/layout_product_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:background="@drawable/bg_gray_rounded"
            android:orientation="vertical"
            android:padding="16dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/str_item"
                    android:textColor="@color/black_50"
                    android:textSize="13sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/str_price"
                    android:textColor="@color/black_50"
                    android:textSize="13sp" />
            </LinearLayout>

            <!--      divide      -->
            <View
                android:id="@+id/divide_line"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/chat_card_divide_line"
                android:layerType="software" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:src="@drawable/coin" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_coin_balance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="3dp"
                    android:fontFamily="@font/roboto_medium"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="30000" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_coin_more"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="3dp"
                    android:fontFamily="@font/roboto_medium"
                    android:textColor="#FF6C00"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="+30" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_price"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_regular"
                    android:gravity="center_vertical|end"
                    android:textColor="@color/black_50"
                    android:textSize="13sp"
                    tools:text="$240" />
            </LinearLayout>
        </LinearLayout>

        <!-- 支付方式列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_payment_methods"
            android:layout_width="match_parent"
            android:layout_height="400dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:nestedScrollingEnabled="false"
            android:overScrollMode="never"
            app:layout_constraintBottom_toTopOf="@id/btn_pay"
            app:layout_constraintTop_toBottomOf="@id/layout_product_info"
            tools:itemCount="4"
            tools:listitem="@layout/item_payment_method" />

        <View
            android:id="@+id/bottom_shadow"
            android:layout_width="match_parent"
            android:layout_height="121dp"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- Pay 按钮 -->
        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/btn_pay"
            android:layout_width="311dp"
            android:layout_height="50dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/black_rounded_button_bg"
            android:fontFamily="@font/roboto_black"
            android:gravity="center"
            android:text="@string/str_pay"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>