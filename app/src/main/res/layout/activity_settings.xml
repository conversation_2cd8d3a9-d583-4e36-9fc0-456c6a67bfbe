<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg"
    android:orientation="vertical">


    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:elevation="0dp">

        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/iv_back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/nav_btn_back" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/settings"
            android:textColor="@android:color/black"
            android:textSize="18sp"
            android:textStyle="bold" />
    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/about_us_bg"
        android:overScrollMode="never">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:orientation="vertical"
            android:padding="0dp">

            <!-- Switch 1: Auto Translate -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/auto_translate"
                    android:textColor="@color/black"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:typeface="sans" />

                <Switch
                    android:id="@+id/switchAutoTranslate"
                    style="@style/NoRippleSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="5dp"
                    android:thumb="@drawable/switch_thumb"
                    android:track="@drawable/switch_track" />
            </LinearLayout>
            <!-- Switch 2: Do not disturb - Call -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/do_not_disturb_call"
                    android:textColor="@color/black"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:typeface="sans" />

                <Switch
                    android:id="@+id/switchDndCall"
                    style="@style/NoRippleSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="5dp"
                    android:thumb="@drawable/switch_thumb"
                    android:track="@drawable/switch_track" />
            </LinearLayout>
            <!-- Switch 3: Do not disturb - Message -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/do_not_disturb_message"
                    android:textColor="@color/black"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:typeface="sans" />

                <Switch
                    android:id="@+id/switchDndMsg"
                    style="@style/NoRippleSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="5dp"
                    android:thumb="@drawable/switch_thumb"
                    android:track="@drawable/switch_track" />
            </LinearLayout>

            <!-- 分割线 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:background="@color/divide_line" />

            <!-- Blocklist -->
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/itemBlocklist"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/blocklist"
                    android:textColor="@color/black"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:typeface="sans" />

                <ImageView
                    android:id="@+id/block_arrow"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_chevron_right" />
            </com.score.callmetest.ui.widget.AlphaLinearLayout>
            <!-- About Us -->
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/itemAboutUs"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/about_us"
                    android:textColor="@color/black"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:typeface="sans" />

                <ImageView
                    android:id="@+id/about_arrow"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_chevron_right" />
            </com.score.callmetest.ui.widget.AlphaLinearLayout>
            <!-- Version -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/version_text"
                    android:textColor="@color/black"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:typeface="sans" />

                <TextView
                    android:id="@+id/tvVersion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#888"
                    android:textSize="14sp" />


            </LinearLayout>
            <!-- delete account -->
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/item_delete_account"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/delete_account_text"
                    android:textColor="@color/black"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:typeface="sans" />

                <ImageView
                    android:id="@+id/delete_arrow"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_chevron_right" />
            </com.score.callmetest.ui.widget.AlphaLinearLayout>
            <!-- 分割线 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:background="@color/divide_line" />

            <!-- Google -->
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/itemGoogle"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/google"
                    android:textColor="@color/black"
                    android:textSize="15sp" />

                <ImageView
                    android:id="@+id/google_arrow"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_chevron_right" />
            </com.score.callmetest.ui.widget.AlphaLinearLayout>

            <!-- Gmail -->
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/itemGmail"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/gmail"
                    android:textColor="@color/black"
                    android:textSize="15sp" />

                <ImageView
                    android:id="@+id/gmail_arrow"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_chevron_right" />
            </com.score.callmetest.ui.widget.AlphaLinearLayout>

            <!-- 分割线 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:background="@color/divide_line" />

            <!-- Log out -->
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/itemLogout"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/log_out"
                    android:textColor="@color/black"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:typeface="sans" />

                <ImageView
                    android:id="@+id/logout_arrow"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_chevron_right" />
            </com.score.callmetest.ui.widget.AlphaLinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>