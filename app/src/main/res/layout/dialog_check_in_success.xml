<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!-- 主要弹窗内容 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="116dp"
        android:layout_marginHorizontal="40dp"
        android:background="@drawable/bg_dialog_rounded"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/dialog_bg"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@drawable/login_dialog_bg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Congratulations 标题 -->
        <TextView
            android:id="@+id/tv_congratulations"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="30dp"
            android:fontFamily="@font/roboto_black"
            android:gravity="center"
            android:text="@string/str_congratulations"
            android:textColor="@color/check_in_brown"
            android:textSize="20sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 奖励内容容器 -->
        <LinearLayout
            android:id="@+id/reward_content_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="38dp"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_congratulations">

            <!-- 金币奖励图标 -->
            <ImageView
                android:id="@+id/iv_reward_icon"
                android:layout_width="58dp"
                android:layout_height="58dp"
                android:layout_marginEnd="8dp"
                android:scaleType="fitCenter"
                tools:src="@drawable/icon_check_in_small_coin" />

            <!-- 奖励数量文本 -->
            <TextView
                android:id="@+id/tv_reward_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_black"
                android:textColor="@color/reward_coin_text"
                android:textSize="52sp"
                tools:text="500" />

        </LinearLayout>

        <!-- OK按钮 -->
        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/btn_ok"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="40dp"
            android:layout_marginBottom="24dp"
            android:fontFamily="@font/roboto_black"
            android:gravity="center"
            android:text="@string/str_ok"
            android:textSize="18sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/reward_content_layout"
            tools:background="@drawable/bg_btn_rounded_blue" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <!--  top-image  -->
    <ImageView
        android:id="@+id/top_image"
        android:layout_width="0dp"
        android:layout_height="116dp"
        android:scaleType="center"
        android:src="@drawable/bg_check_in_ok_top"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="@id/dialog_content"
        app:layout_constraintStart_toStartOf="@id/dialog_content" />

</androidx.constraintlayout.widget.ConstraintLayout>
