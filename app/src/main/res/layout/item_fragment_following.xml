<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_mine_function_item_selector">

    <!-- 头像 -->
    <com.score.callmetest.ui.widget.CircleIconButton
        android:id="@+id/iv_avatar"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:layout_marginVertical="12dp"
        android:layout_marginStart="15dp"
        android:clickable="true"
        android:focusable="true"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:iconSrc="@drawable/placeholder" />

    <!-- 在线状态指示器 -->
    <View
        android:id="@+id/status_indicator"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_marginEnd="3dp"
        android:layout_marginBottom="3dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:background="@drawable/shape_online_status"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_avatar" />

    <!-- 用户名 -->
    <TextView
        android:id="@+id/tv_username"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/black"
        android:textSize="15sp"
        android:textStyle="bold"
        android:fontFamily="@font/roboto_bold"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constrainedWidth="true"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        app:layout_constraintBottom_toTopOf="@+id/item_country"
        app:layout_constraintEnd_toStartOf="@+id/iv_video_indicator"
        tools:text="Kathleen" />

    <!-- 国家信息 -->
    <LinearLayout
        android:id="@+id/item_country"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:clickable="false"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintStart_toStartOf="@id/tv_username"
        app:layout_constraintTop_toBottomOf="@id/tv_username">

        <com.score.callmetest.ui.widget.CountryIconView
            android:id="@+id/iv_flag"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_gravity="center_vertical"
            android:scaleType="fitCenter"
            android:src="@drawable/all" />

        <TextView
            android:id="@+id/tv_region"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="13sp"
            android:fontFamily="@font/roboto_regular"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="12dp"
            android:textColor="@color/cuntry_blocklist"
            android:gravity="center"
            android:layout_gravity="center_vertical"
            android:text="All"
            android:maxLines="1" />

    </LinearLayout>


    <!-- 视频通话按钮 -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/iv_video_indicator"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_marginEnd="18dp"
        android:scaleType="fitCenter"
        android:src="@drawable/btn_message"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
