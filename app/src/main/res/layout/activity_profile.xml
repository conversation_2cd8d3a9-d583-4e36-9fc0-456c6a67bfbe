<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg"
    android:orientation="vertical">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:elevation="0dp">

        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/iv_back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/nav_btn_back" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/profile"
            android:textColor="@android:color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/btnDone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="end|center_vertical"
            android:layout_marginEnd="16dp"
            android:enabled="false"
            android:paddingHorizontal="12dp"
            android:paddingVertical="5dp"
            android:text="@string/done"
            android:textColor="#4D000000"
            android:textSize="14sp"
            android:textStyle="bold" />
    </androidx.appcompat.widget.Toolbar>

    <Space
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="#A2A2A2" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="never">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <com.score.callmetest.ui.widget.AlphaRelativeLayout
                android:id="@+id/avatar_layout"
                android:layout_width="match_parent"
                android:layout_height="55dp">

                <TextView
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/my_avatar" />

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/ivAvatar"
                    android:layout_width="39dp"
                    android:layout_height="39dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:scaleType="centerCrop"
                    app:shapeAppearanceOverlay="@style/CircleImageView" />
            </com.score.callmetest.ui.widget.AlphaRelativeLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#A2A2A2" />

            <!-- ID Field -->
            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="55dp">

                <TextView
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/id" />


                <TextView
                    android:id="@+id/tvId"
                    style="@style/SettingItemTip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toStartOf="@+id/btnCopy"
                    android:gravity="end"
                    android:text="12324374363" />

                <com.score.callmetest.ui.widget.AlphaTextView
                    android:id="@+id/btnCopy"
                    style="@style/SettingItemButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginHorizontal="8dp"
                    android:background="@drawable/bg_btn_copy"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="5dp"
                    android:text="@string/copy" />
            </RelativeLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#A2A2A2" />
            <!-- Nickname Field -->
            <com.score.callmetest.ui.widget.AlphaRelativeLayout
                android:id="@+id/nickname_layout"
                android:layout_width="match_parent"
                android:layout_height="55dp">

                <TextView
                    android:id="@+id/nickname_title"
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/nickname" />

                <EditText
                    android:id="@+id/et_nickname"
                    style="@style/SettingItemTip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="23dp"
                    android:layout_toEndOf="@+id/nickname_title"
                    android:background="@null"
                    android:gravity="end"
                    android:imeOptions="actionDone"
                    android:inputType="text"
                    android:minWidth="20dp"
                    android:selectAllOnFocus="true"
                    android:singleLine="true"
                    android:text="sdfhuyisdfhuyisdfhuyisdfhuyisdfhuyisdfhuyisdfhuyisdfhuyisdfhuyisdfhuyisdfhuyisdfhuyisdfhuyisdfhuyisdfhuyi" />

                <ImageView
                    android:id="@+id/name_arrow"
                    android:layout_width="23dp"
                    android:layout_height="23dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:scaleType="center"
                    android:src="@drawable/ic_chevron_right"
                    android:tintMode="src_in"
                    app:tint="#AEAEAE" />
            </com.score.callmetest.ui.widget.AlphaRelativeLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#A2A2A2" />
            <!-- Gender Field -->
            <RelativeLayout
                android:id="@+id/gender_layout"
                android:layout_width="match_parent"
                android:layout_height="55dp">

                <TextView
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/gender" />

                <TextView
                    android:id="@+id/tv_gender"
                    style="@style/SettingItemTip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="8dp"
                    android:gravity="end"
                    android:text="@string/male" />

            </RelativeLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#A2A2A2" />
            <!-- Age Field -->
            <com.score.callmetest.ui.widget.AlphaRelativeLayout
                android:id="@+id/age_layout"
                android:layout_width="match_parent"
                android:layout_height="55dp"
                android:visibility="visible">

                <TextView
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="@string/age" />

                <TextView
                    android:id="@+id/tv_age"
                    style="@style/SettingItemTip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="23dp"
                    android:gravity="end"
                    android:text="23" />

                <ImageView
                    android:id="@+id/age_arrow"
                    android:layout_width="23dp"
                    android:layout_height="23dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:scaleType="center"
                    android:src="@drawable/ic_chevron_right"
                    android:tintMode="src_in"
                    app:tint="#AEAEAE" />
            </com.score.callmetest.ui.widget.AlphaRelativeLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#A2A2A2"
                android:visibility="gone" />
            <!-- Regions Field -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/region_layout"
                android:layout_width="match_parent"
                android:layout_height="55dp"
                android:visibility="visible">

                <TextView
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/country_region"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.score.callmetest.ui.widget.CountryIconView
                    android:id="@+id/image_region"
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_marginEnd="5dp"
                    android:src="@drawable/in"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/tv_region"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_region"
                    style="@style/SettingItemTip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/india"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/image_select"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/image_select"
                    android:layout_width="23dp"
                    android:layout_height="23dp"
                    android:layout_gravity="center_vertical"
                    android:scaleType="center"
                    android:src="@drawable/ic_chevron_right"
                    android:tintMode="src_in"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="#AEAEAE" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#A2A2A2" />

            <!-- Self-introduction Field -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_introduce"
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:text="@string/self_introduction"
                    android:textColor="@android:color/black" />

                <!-- 直接使用EditText，无需NestedScrollView -->
                <EditText
                    android:id="@+id/etSelfIntroduction"
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    android:layout_below="@+id/tv_introduce"
                    android:layout_marginTop="20dp"
                    android:background="#F3F5FA"
                    android:gravity="top|start"
                    android:hint="@string/signature_hint"
                    android:maxLength="200"
                    android:maxLines="10"
                    android:minLines="3"
                    android:overScrollMode="always"
                    android:padding="8dp"
                    android:scrollbars="vertical"
                    android:textColor="@color/black"
                    android:textColorHint="#666666"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tvCharCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignEnd="@+id/etSelfIntroduction"
                    android:layout_alignBottom="@+id/etSelfIntroduction"
                    android:layout_gravity="end"
                    android:layout_marginEnd="14dp"
                    android:layout_marginBottom="14dp"
                    android:text="0/200"
                    android:textColor="#BBBBBB"
                    android:textSize="10sp" />
            </RelativeLayout>

            <!-- Introduction Photos Section -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp">

                <TextView
                    android:id="@+id/tv_photos_title"
                    style="@style/SettingItemTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/gallery"
                    android:textColor="@android:color/black"
                    android:textSize="16sp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/photo_recyclerview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_photos_title"
                    android:layout_marginTop="13dp"
                    android:overScrollMode="never" />
            </RelativeLayout>
        </LinearLayout>
    </ScrollView>

</LinearLayout>