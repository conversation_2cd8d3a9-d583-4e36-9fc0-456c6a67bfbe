<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg"
    android:orientation="vertical">

    <!--顶部导航栏-->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:elevation="0dp">

        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/iv_back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/nav_btn_back" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/visitor"
            android:textColor="@android:color/black"
            android:textSize="18sp"
            android:textStyle="bold" />
    </androidx.appcompat.widget.Toolbar>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ViewStub
            android:id="@+id/empty_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:inflatedId="@+id/layout_empty_rv_parent"
            android:layout="@layout/layout_empty_rv_bg" />

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipeRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:paddingTop="8dp"
                tools:listitem="@layout/item_visitor" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <FrameLayout
            android:id="@+id/dialog_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="40dp"
                android:background="@drawable/vip_unlock_visitor_bg">

                <LinearLayout
                    android:id="@+id/line_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="26dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:src="@drawable/round_brown" />

                    <TextView
                        android:id="@+id/title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:lineHeight="24dp"
                        android:text="@string/become_vip"
                        android:textColor="#682417"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:typeface="sans" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:src="@drawable/round_brown" />
                </LinearLayout>


                <TextView
                    android:id="@+id/desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/line_title"
                    android:layout_centerHorizontal="true"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="8dp"
                    android:text="@string/view_all_visitors_records"
                    android:textColor="#632121"
                    android:textSize="15sp" />

                <ImageView
                    android:id="@+id/img"
                    android:layout_width="258dp"
                    android:layout_height="113dp"
                    android:layout_below="@+id/desc"
                    android:layout_centerHorizontal="true"
                    android:layout_marginHorizontal="40dp"
                    android:layout_marginTop="26dp"
                    android:src="@drawable/vip_unlock_visitor_icom" />

                <ImageView
                    android:id="@+id/lock"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_alignBottom="@+id/img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="-15dp"
                    android:src="@drawable/vip_lock" />

                <com.score.callmetest.ui.widget.AlphaSVGAImageView
                    android:id="@+id/unlock_btn_svga"
                    android:layout_width="265dp"
                    android:layout_height="50dp"
                    android:layout_below="@+id/lock"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="40dp"
                    android:layout_marginBottom="26dp" />

                <com.score.callmetest.ui.widget.AlphaImageView
                    android:id="@+id/close"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentEnd="true"
                    android:padding="15dp"
                    android:src="@drawable/vip_unlock_dialog_close" />
            </RelativeLayout>
        </FrameLayout>

    </FrameLayout>

</LinearLayout>