<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg"
    tools:context=".ui.mine.blockList.BlockListFragment">

    <!-- 返回按钮 -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/btn_return"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="15dp"
        android:scaleType="fitCenter"
        android:src="@drawable/nav_btn_back"
        app:layout_constraintBottom_toBottomOf="@id/tab_follow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tab_follow" />

    <!--  tab  -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_follow"
        style="@style/CustomTabStyle"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:background="@android:color/transparent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tabGravity="center"
        app:tabIndicatorHeight="0dp"
        app:tabMode="fixed" />

    <!-- 内容区域 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/tab_follow"
        app:layout_constraintBottom_toBottomOf="parent">

        <ViewStub
            android:id="@+id/empty_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:inflatedId="@+id/layout_empty_rv_parent"
            android:layout="@layout/layout_empty_rv_bg" />

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipeRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:overScrollMode="never"
                android:paddingTop="8dp"
                tools:listitem="@layout/item_fragment_blocklist" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
