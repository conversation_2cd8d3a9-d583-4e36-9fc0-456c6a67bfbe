<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="8dp"
    android:paddingTop="4dp"
    android:paddingEnd="60dp"
    android:paddingBottom="4dp">

    <!-- 头像 -->
    <com.score.callmetest.ui.widget.CircleIconButton
        android:id="@+id/ivAvatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="16dp"
        android:contentDescription="@string/avatar"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:iconSrc="@drawable/customer_service" />

    <!-- 图片消息 -->
    <com.score.callmetest.ui.widget.AspectRatioImageView
        android:id="@+id/ivImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginTop="16dp"
        android:contentDescription="@string/image_message"
        android:scaleType="fitCenter"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/image_placeholder"
        tools:visibility="visible" />

    <!-- 消息容器 - -->
    <LinearLayout
        android:id="@+id/messageContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/bg_chat_bubble_left"
        android:orientation="vertical"
        android:padding="11dp"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 消息文本 -->
        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/tvMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|start"
            android:layout_marginBottom="8dp"
            android:fontFamily="@font/roboto_regular"
            android:maxWidth="215dp"
            android:textColor="@color/black"
            android:textColorLink="@color/blue_chat_link"
            android:textSize="14sp"
            tools:text="You can get UP TO 20% more coins.\nValid for 24 hours.You can get UP TO 20% more coins.\nValid for 24 hours.You can get UP TO 20% more coins.\nValid for 24 hours." />

        <View
            android:id="@+id/lineTranslate"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            android:background="@color/gray_chat_translated"
            android:visibility="gone"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTranslateContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:maxWidth="215dp"
            android:textColor="@color/gray_chat_translated"
            android:textSize="14sp"
            android:visibility="gone"
            tools:text="翻译内容翻译内容翻译内容翻译内容翻译内容翻译内容翻译内容"
            tools:visibility="visible" />

    </LinearLayout>

    <!-- 翻译按钮 -->
    <com.score.callmetest.ui.widget.AlphaTextView
        android:id="@+id/tvTranslate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="6dp"
        android:drawableStart="@drawable/translate"
        android:drawablePadding="6dp"
        android:text="@string/click_to_translate"
        android:textColor="@color/gray_chat_translate"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toBottomOf="@id/messageContainer"
        tools:visibility="visible" />

    <!-- 翻译进度 -->
    <ProgressBar
        android:id="@+id/progressTranslate"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="4dp"
        android:indeterminateTint="@color/gray_999"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvTranslate"
        app:layout_constraintStart_toEndOf="@id/tvTranslate"
        app:layout_constraintTop_toTopOf="@id/tvTranslate" />

    <!-- 时间 -->
    <TextView
        android:id="@+id/tvTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="4dp"
        android:textColor="@color/gray_999"
        android:textSize="10sp"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toBottomOf="@id/messageContainer"
        tools:ignore="SmallSp"
        tools:text="2024-07-09 21:22" />

</androidx.constraintlayout.widget.ConstraintLayout>
