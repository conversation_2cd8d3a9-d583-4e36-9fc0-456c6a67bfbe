<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg"
    android:orientation="vertical">


    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:elevation="0dp">

        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/iv_back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/nav_btn_back" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/delete_account"
            android:textColor="@android:color/black"
            android:textSize="18sp"
            android:textStyle="bold" />
    </androidx.appcompat.widget.Toolbar>

    <LinearLayout
        android:id="@+id/line_context"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="29dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="29dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_text1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_regular"
            android:gravity="start"
            android:text="@string/delete_account_text1"
            android:textColor="#282828"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_text2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="22dp"
            android:fontFamily="@font/roboto_regular"
            android:gravity="start"
            android:text="@string/delete_account_text2"
            android:textColor="#282828"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_text3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="22dp"
            android:fontFamily="@font/roboto_regular"
            android:gravity="start"
            android:text="@string/delete_account_text3"
            android:textColor="#282828"
            android:textSize="14sp" />

        <!-- 添加一个占位用的 Space，让下面的按钮可以推到底部 -->
        <Space
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <com.score.callmetest.ui.widget.AlphaLinearLayout
            android:id="@+id/btn_cancel_layout"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginHorizontal="15dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/bg_btn_rounded_blue"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/btn_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/cancel"
                android:textColor="#fff"
                android:textSize="15sp"
                android:textStyle="bold" />
        </com.score.callmetest.ui.widget.AlphaLinearLayout>

        <TextView
            android:id="@+id/btn_delete"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="23dp"
            android:layout_marginBottom="40dp"
            android:fontFamily="@font/roboto_bold"
            android:text="@string/delete_account_text"
            android:textAlignment="center"
            android:textColor="#FF4F4F"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>
</LinearLayout>