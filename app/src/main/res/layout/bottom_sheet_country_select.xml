<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/transparent">
        
        <ImageView
            android:id="@+id/dialog_bg"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@drawable/bg_country_select" />

        <!-- 顶部标题栏 -->
        <RelativeLayout
            android:id="@+id/top_title"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:paddingStart="22dp"
            android:layout_marginTop="60dp"
            android:paddingEnd="16dp">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:text="@string/select_regions"
                android:textStyle="bold"
                android:textColor="#FF000000"
                android:textSize="16sp"/>

            <ImageView
                android:id="@+id/image_cancel"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_alignParentEnd="true"
                android:src="@drawable/cancel_country_select" />
        </RelativeLayout>

        <!-- 国家选择器 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_country"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="90dp"
            android:clipToPadding="false"
            android:overScrollMode="never"
            android:paddingHorizontal="22dp"
            android:background="#fff"/>
    </FrameLayout>
</FrameLayout>