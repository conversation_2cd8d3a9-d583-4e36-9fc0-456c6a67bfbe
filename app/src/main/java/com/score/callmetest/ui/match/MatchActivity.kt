package com.score.callmetest.ui.match

import android.os.Handler
import android.os.Looper
import android.view.WindowManager
import androidx.activity.OnBackPressedCallback
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.media3.ui.TimeBar
import androidx.room.concurrent.AtomicBoolean
import com.score.callmetest.CallmeApplication
import com.score.callmetest.HangupScene
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityMatchBinding
import com.score.callmetest.manager.AppLifecycleManager
import com.score.callmetest.manager.AudioPlayManager
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.FlashChatManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.HangUpReason
import com.score.callmetest.manager.OnPickUpMessage
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.VideoCallManager
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.videocall.CallIncomingManager
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.Random

/**
 * 匹配页面
 */
class MatchActivity : BaseActivity<ActivityMatchBinding, MatchViewModel>() {

    private var channelName: String? = null
    private var oppositeUserId: String? = null
    private var pickUpMsg: OnPickUpMessage? = null
    private var isUserJoined: Boolean = false
    private var gotoCallJob: Job? = null

    private val mClosed = AtomicBoolean(false)
    override fun getViewBinding(): ActivityMatchBinding {
        return ActivityMatchBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = MatchViewModel::class.java

    override fun initView() {
        setKeepScreenOn()
        startRingtone()
        initScrollTip()
    }

    override fun initData() {
        matchingText = getString(R.string.matching)
    }

    override fun initListener() {
        // 拦截系统返回（含手势返回与预测性返回）
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // 消费返回事件，不执行退出
            }
        })

        CustomUtils.playSvga(binding.matchIcon, "matching.svga")
        showText()
        binding.close.click {
            mClosed.set(true)
            gotoCallJob?.cancel()
            FlashChatManager.matchCancel(
                callback = {}
            )
            AgodaUtils.leaveChannel()
            finish()
        }

        FlashChatManager.flashChat(
            scope = lifecycleScope,
            callback = { flashChatResponse ->
                if (flashChatResponse != null &&
                    !flashChatResponse.channelName.isNullOrEmpty() &&
                    !flashChatResponse.rtcToken.isNullOrEmpty()
                ) {
                    startCallTimeouts()
                    channelName = flashChatResponse.channelName
                    oppositeUserId = flashChatResponse.fromUserId

                    // toUserId != "0"说明匹配到了主播，随机延迟后加入频道
                    if (flashChatResponse.toUserId != "0") {
                        binding.close.isEnabled = false
                        binding.close.isVisible = false
                        if(mClosed.get()){
                            return@flashChat
                        }
                        matchingText = getString(R.string.connecting)
                        AgodaUtils.joinChannelWithUserAccount(
                            flashChatResponse.rtcToken,
                            channelName!!,
                            flashChatResponse.fromUserId.toString()
                        )
                        gotoCallJob = ThreadUtils.runOnMainDelayed(4000) {
                            if (mClosed.get()) {
                                return@runOnMainDelayed
                            }
                            VideoCallActivity.startOngoing(
                                context = this@MatchActivity,
                                channelName = flashChatResponse.channelName,
                                fromUserId = flashChatResponse.fromUserId.toString(),
                                toUserId = flashChatResponse.toUserId.toString(),
                                freeCallDuration = flashChatResponse.callFreeSeconds,
                                rtcToken = flashChatResponse.rtcToken,
                            )
                            finish()
                        }
                        return@flashChat
                    }

                    // 收到加入频道成功消息，则监听挂断或接听事件
                    // 等待长链接收 接听 事件（使用双通道去重）
                    DualChannelEventManager.observeOnHangUp(this) { event ->
                        if (channelName == event.channelName) {
                            if (!mClosed.get()) {
                                VideoCallManager.hangUp(
                                    channelName = event.channelName.toString(),
                                    oppositeUserId = event.toUserId.toString(),
                                    hangUpReason = HangUpReason.CL_REMOTE_USER_LEFT,
                                    remark = HangupScene.FLASH_CHAT_HANGUP,
                                )
                            }
                            gotoCallJob?.cancel()
                            AgodaUtils.leaveChannel()
                            finish()
                        }
                    }

                    DualChannelEventManager.observeOnPickUp(this) { pickUpMsg ->
                        // 这里是对方接听后回调。
                        // 断点发现pickUpMsg.fromUserId是对方ID，pickUpMsg.toUserId是自己ID
                        // 所以这里要更换位置
                        this.pickUpMsg = pickUpMsg
                        if (pickUpMsg.rtcToken != null) {
                            AgodaUtils.joinChannelWithUserAccount(
                                pickUpMsg.rtcToken,
                                channelName!!,
                                pickUpMsg.toUserId.toString()
                            )
                        }
                        matchingText = getString(R.string.connecting)
                        binding.close.isEnabled = false
                        binding.close.isVisible = false
                        gotoOnGoing()
                    }

                    AgodaUtils.observeUserJoined(owner = this, onEvent = { event ->
                        isUserJoined = true
                        gotoOnGoing()
                    })
                } else {
                    AgodaUtils.leaveChannel()
                    finish()
                }
            }
        )
    }

    fun gotoOnGoing() {
        if (mClosed.get()) {
            return
        }
        if (pickUpMsg != null && isUserJoined) {
            ThreadUtils.runOnMainDelayed(1000) {
                VideoCallActivity.startOngoing(
                    context = this@MatchActivity,
                    channelName = pickUpMsg!!.channelName.toString(),
                    fromUserId = pickUpMsg!!.toUserId.toString(),
                    toUserId = pickUpMsg!!.fromUserId.toString(),
                    freeCallDuration = pickUpMsg!!.callFreeSeconds,
                    rtcToken = pickUpMsg!!.rtcToken,
                )
                finish()
            }
        }
    }

    // Keep a reference to the Job to cancel it when the Activity is destroyed
    private var matchingTextJob: Job? = null
    private var matchingText: String? = ""

    private fun showText() {
        // Cancel any existing job to avoid multiple coroutines updating the text
        matchingTextJob?.cancel()
        matchingTextJob = lifecycleScope.launch {
            var dots = 1
            while (true) { // Loop indefinitely, or until the Job is cancelled
                binding.matchText.text = matchingText + ".".repeat(dots)
                dots++
                if (dots > 3) {
                    dots = 1
                }
                delay(500) // Adjust the delay as needed (e.g., 500ms)
            }
        }
    }

    private var callHandler: Handler? = null
    private var call30sRunnable: Runnable? = null
    
    // 轮播相关
    private var scrollTipJob: Job? = null
    private var scrollTipList: List<String>? = null
    private var currentScrollTipIndex = 0

    private fun startCallTimeouts() {
        callHandler = Handler(Looper.getMainLooper())
        call30sRunnable = Runnable {
            ToastUtils.showToast(getString(R.string.user_not_available_try_again))
            binding.close.performClick()
        }
        callHandler?.postDelayed(call30sRunnable!!, 30_000)
    }

    private fun clearCallTimeouts() {
        call30sRunnable?.let { callHandler?.removeCallbacks(it) }
    }

    private fun startRingtone() {
        if (AppLifecycleManager.isAppInBackground()) {
            return
        }
        AudioPlayManager.playVideoRingtone(this,lifecycleScope)
    }

    private fun stopRingtone() {
        AudioPlayManager.stopPlay()
    }

    override fun onDestroy() {
        matchingTextJob?.cancel()
        scrollTipJob?.cancel()
        UserInfoManager.refreshMyUserInfo()
        clearCallTimeouts()
        stopRingtone()
        clearKeepScreenOn()
        super.onDestroy()
    }


    /**
     * 设置防息屏
     */
    private fun setKeepScreenOn() {
        window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    /**
     * 取消防息屏
     */
    private fun clearKeepScreenOn() {
        window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }
    
    /**
     * 初始化轮播提示
     */
    private fun initScrollTip() {
        GlobalManager.getScrollIntroduce { scrollTipData ->
            scrollTipList = scrollTipData
            if (!scrollTipData.isNullOrEmpty()) {
                startScrollTip()
            }
        }
    }
    
    /**
     * 开始轮播提示
     */
    private fun startScrollTip() {
        scrollTipJob?.cancel()
        scrollTipJob = lifecycleScope.launch {
            while (true) {
                scrollTipList?.let { list ->
                    if (list.isNotEmpty()) {
                        binding.scrollTip.text = list[currentScrollTipIndex]
                        currentScrollTipIndex = (currentScrollTipIndex + 1) % list.size
                    }
                }
                delay(3000) // 2秒切换一次
            }
        }
    }
}
