package com.score.callmetest.ui.main

import android.graphics.drawable.GradientDrawable
import android.view.View
import androidx.core.graphics.toColorInt
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityCoinStoreBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.BannerManager
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.manager.PaymentMethodManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.VipManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.widget.GridSpacingItemDecoration
import com.score.callmetest.ui.widget.Helper.PagerHelper
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EmojiUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 金币充值页面
 */
class CoinStoreActivity : BaseActivity<ActivityCoinStoreBinding, CoinStoreViewModel>() {

    private var rechargeAdapter: RechargeOptionAdapter? = null
    private var selectedOption: RechargeOption? = null

    override fun getViewBinding() = ActivityCoinStoreBinding.inflate(layoutInflater)
    override fun getViewModelClass() = CoinStoreViewModel::class.java

    private val PAGER_KEY = this::class.java.simpleName

    override fun initView() {
        GlobalManager.addViewStatusBarTopMargin(this, binding.llTopBar)
        CustomUtils.flipViewIfRTL(binding.ivBack)

        binding.tvTitle.text = getString(R.string.coinStore)
        // 返回按钮点击关闭页面
        binding.ivBack.click {
            finish()
        }
        
        setupBanner()
        setupRechargeOptions()
        updateCoinBalance()
        setupSvgaAnimation()

        binding.frameContinueContainer.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#00FFFCF5".toColorInt(), "#FFFFFCF5".toColorInt()),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM,
        )
    }

    override fun initData() {
        super.initData()
        // 预先加载支付渠道列表
        lifecycleScope.launch {
            PaymentMethodManager.ensurePayChannelListLoaded()
        }
        loadRechargeOptions()
    }

    override fun onResume() {
        super.onResume()
        loadRechargeOptions()
        PagerHelper.resumeAutoScroll(PAGER_KEY)
        // 恢复SVGA动画播放
        startSvgaAnimation()
    }

    override fun initListener() {
        super.initListener()
        setupObservers()
        setupContinueButton()
    }

    /**
     * 设置 Banner 轮播图
     */
    private fun setupBanner() {
        BannerManager.getBannerInfo(
            scope = lifecycleScope,
            callback = { bannerList ->
                Timber.d("CoinStoreActivity banner data received: ${bannerList?.size} items")
                if (!bannerList.isNullOrEmpty()) {
                    Timber.d("CoinStoreActivity setting up banner with ${bannerList.size} items")
                    // 使用 PagerHelper 设置轮播图
                    PagerHelper.setupBannerPager(
                        viewPager = binding.bannerViewPager,
                        bannerList = bannerList,
                        simpleName = PAGER_KEY,
                        enableLoop = true,
                        enableAutoScroll = true,
                        autoScrollInterval = 3000L,
                        onItemClick = { realIndex ->
                            if (realIndex < bannerList.size) {
                                BannerManager.handleBannerClick(
                                    context = this,
                                    bannerInfoResponse = bannerList[realIndex]
                                )
                            }
                        },
                        onPageSelected = { realIndex, totalCount, isBlurred ->
                            Timber.d("CoinStoreActivity onPageSelected: realIndex=$realIndex")
                            updateDots(bannerList.size, realIndex)
                        }
                    )
                    // 延迟初始化小圆点，确保 ViewPager 已经设置完成
                    binding.bannerViewPager.post {
                        updateDots(bannerList.size, 0)
                    }
                } else {
                    Timber.d("CoinStoreActivity banner list is empty")
                    binding.lineBanner.visibility = View.GONE
                }
            }
        )
    }

    /**
     * 更新轮播图小圆点
     */
    private fun updateDots(totalCount: Int, currentIndex: Int) {
        Timber.d("CoinStoreActivity updateDots: totalCount=$totalCount, currentIndex=$currentIndex")

        try {
            binding.dotLayout.removeAllViews()

            // 确保参数有效
            if (totalCount <= 0 || currentIndex < 0) {
                Timber.w("CoinStoreActivity invalid parameters: totalCount=$totalCount, currentIndex=$currentIndex")
                binding.dotLayout.visibility = View.GONE
                return
            }

            // 只有多于1张图片时才显示小圆点
            if (totalCount <= 1) {
                binding.dotLayout.visibility = View.GONE
                Timber.d("CoinStoreActivity dots hidden: totalCount <= 1")
                return
            }

            binding.dotLayout.visibility = View.VISIBLE
            Timber.d("CoinStoreActivity dots visible: creating $totalCount dots")

            for (i in 0 until totalCount) {
                val dotSize = DisplayUtils.dp2px(5f)
                val dot = android.widget.ImageView(this)

                val params = android.view.ViewGroup.MarginLayoutParams(dotSize, dotSize).apply {
                    if (i > 0) {
                        marginStart = DisplayUtils.dp2px(3f)
                    }
                }
                dot.layoutParams = params

                val isSelected = i == currentIndex
                val color = if (isSelected) android.graphics.Color.WHITE else "#88FFFFFF".toColorInt()

                dot.setImageDrawable(
                    com.score.callmetest.util.DrawableUtils.createRoundRectDrawable(
                        radius = dotSize / 2f,
                        color = color
                    )
                )

                binding.dotLayout.addView(dot)
                Timber.d("CoinStoreActivity added dot $i, selected=$isSelected, color=${Integer.toHexString(color)}")
            }

            Timber.d("CoinStoreActivity dots update completed: ${binding.dotLayout.childCount} dots added")
        } catch (e: Exception) {
            Timber.e(e, "CoinStoreActivity error updating dots")
            binding.dotLayout.visibility = View.GONE
        }
    }

    /**
     * 设置充值套餐列表
     */
    private fun setupRechargeOptions() {
        binding.rvRechargeOptions.layoutManager = GridLayoutManager(this, 2)
        // 添加卡片间距，13dp
        val spacing = DisplayUtils.dp2px(13f)
        binding.rvRechargeOptions.addItemDecoration(GridSpacingItemDecoration(2, spacing, false))
    }

    /**
     * 加载充值套餐数据
     */
    private fun loadRechargeOptions() {
        val cachedGoods = GoodsManager.getCachedAllGoods()
        if (cachedGoods.isNotEmpty()) {
            updateRechargeOptionsUI(cachedGoods)
        } else {
            lifecycleScope.launch {
                try {
                    val goodsList = GoodsManager.getAllGoods()
                    updateRechargeOptionsUI(goodsList)
                } catch (e: Exception) {
                    ToastUtils.showShortToast(getString(R.string.net_error_and_try_again))
                }
            }
        }
    }

    /**
     * 更新充值套餐UI
     */
    private fun updateRechargeOptionsUI(goodsList: List<GoodsInfo>) {
        lifecycleScope.launch {
            val payChannelList = PaymentMethodManager.ensurePayChannelListLoaded()
            val rechargeOptions = goodsList
                .map { goods ->
                    // 使用本地化价格逻辑，与其他地方保持一致
                    val (priceSymbol, price) = GoodsManager.getLocaleGoodsPrice(goods)
                    val (oldPriceSymbol, oldPrice) = GoodsManager.getLocaleGoodsPrice(goods, true)
                    var discount = 0
                    if (!payChannelList.isNullOrEmpty()) {
                        payChannelList.forEachIndexed { index, item ->
                            val calculatedDiscount = CustomUtils.calculateChannelDiscount(goods.type, goods.thirdpartyCoinPercent, item)
                            if (calculatedDiscount > discount) {
                                discount = calculatedDiscount
                            }
                        }
                    }
                    RechargeOption(
                        iconRes = GoodsManager.getIconByGoodsId(goods.code),
                        coinAmount = (goods.exchangeCoin ?: 0),
                        price = price?.let { "$priceSymbol$it" } ?: "",
                        oldPrice = oldPrice?.let { "$oldPriceSymbol$it" } ?: "",
                        priceSymbol = priceSymbol,
                        name = goods.name,
                        tags = goods.tags,
                        extraCoinPercent = goods.extraCoinPercent,
                        discount = discount,
                        goodsCode = goods.code,
                        type = goods.type,
                        remainMilliseconds = goods.remainMilliseconds ?: goods.surplusMillisecond,
                        invitationId = goods.invitationId
                    )
                }

            val distinctOptions = rechargeOptions
                .filter { it.type == "0" || ((it.remainMilliseconds ?: 0L) > 0L) }

            rechargeAdapter = RechargeOptionAdapter.createWithoutLimit(
                options = distinctOptions,
                onClick = { option -> 
                    selectedOption = option
                    updatePriceDisplay(option)
                    if (GoodsManager.isVipGoods(option.goodsCode)) {
                        VipManager.showVipDialog()
                    }
                },
                onCountdownEnd = { loadRechargeOptions() }
            )
            binding.rvRechargeOptions.adapter = rechargeAdapter
            
            // 自动选择第一个选项
            if (distinctOptions.isNotEmpty()) {
                selectedOption = distinctOptions[0]
                updatePriceDisplay(distinctOptions[0])
            }
        }
    }

    /**
     * 更新价格显示
     */
    private fun updatePriceDisplay(option: RechargeOption) {
        try {
            val priceText = "(${option.price})"
            binding.tvPrice.text = priceText
            Timber.d("CoinStoreActivity 更新价格显示: $priceText")
        } catch (e: Exception) {
            Timber.e(e, "CoinStoreActivity 更新价格显示失败")
        }
    }

    /**
     * 处理充值套餐点击
     */
    private fun handleRechargeOptionClick(option: RechargeOption) {
        option.goodsCode?.let { goodsCode ->
            if (GoodsManager.isVipGoods(goodsCode)) {
                VipManager.showVipDialog()
            } else {
                RechargeManager.startRecharge(
                    activity = this@CoinStoreActivity,
                    goodsCode = goodsCode,
                    goodsName = option.price + option.priceSymbol + "-" + option.coinAmount,
                    entry = RechargeSource.SUBSCRIBE_DETAIL
                )
            }
        }
    }

    /**
     * 更新金币余额显示
     */
    private fun updateCoinBalance() {
        val coinCount = UserInfoManager.myUserInfo?.availableCoins ?: 0
        binding.tvCoinBalance.text = coinCount.toString()
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 监听金币余额变化
        DualChannelEventManager.observeAvailableCoins(this) { availableCoinsMessage ->
            val availableCoins = availableCoinsMessage.coins ?: 0
            UserInfoManager.myUserInfo?.availableCoins = availableCoins
            binding.tvCoinBalance.text = availableCoins.toString()
        }
    }

    /**
     * 设置继续按钮点击事件
     */
    private fun setupContinueButton() {
        binding.svgaContinue.setOnClickListener {
            Timber.d("CoinStoreActivity 继续按钮点击")
            // 获取当前选中的充值选项
            selectedOption?.let { option ->
                // 重置StayDialog显示次数
                EmojiUtils.resetStayDialogCount()
                handleRechargeOptionClick(option)
            } ?: run {
                Timber.w("CoinStoreActivity 没有选中的充值选项")
            }
        }
    }

    /**
     * 设置SVGA动画
     */
    private fun setupSvgaAnimation() {
        // 初始化SVGA动画
        CustomUtils.playSvga(
            svgaView = binding.svgaContinue,
            assetName = "btn_stay_dialog.svga",
            loops = 0, // 无限循环
        )
    }

    /**
     * 开始播放SVGA动画
     */
    private fun startSvgaAnimation() {
        try {
            Timber.d("CoinStoreActivity 开始播放SVGA动画")
            binding.svgaContinue.startAnimation()
        } catch (e: Exception) {
            Timber.e(e, "CoinStoreActivity 播放SVGA动画异常")
        }
    }

    /**
     * 停止SVGA动画
     */
    private fun stopSvgaAnimation() {
        try {
            Timber.d("CoinStoreActivity 停止SVGA动画")
            binding.svgaContinue.stopAnimation()
        } catch (e: Exception) {
            Timber.e(e, "CoinStoreActivity 停止SVGA动画异常")
        }
    }

    override fun onDestroy() {
        // 清理适配器监听器
        rechargeAdapter?.clearAllListeners()
        rechargeAdapter = null
        PagerHelper.cleanup(PAGER_KEY)
        
        // 强制清理RecyclerView
        binding.rvRechargeOptions.adapter = null
        
        super.onDestroy()
        binding.svgaContinue.clearAnimation()
    }

    override fun onPause() {
        super.onPause()
        PagerHelper.pauseAutoScroll(PAGER_KEY)
        // 暂停SVGA动画
        stopSvgaAnimation()
    }
}