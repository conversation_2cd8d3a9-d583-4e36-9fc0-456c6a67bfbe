package com.score.callmetest.ui.mine.about

import android.os.Build
import android.os.Bundle
import com.score.callmetest.BuildConfig
import com.score.callmetest.CallmeApplication
import com.score.callmetest.databinding.ActivityAboutUsBinding
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.RatingManager
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.base.EmptyViewModel
import com.score.callmetest.ui.rating.RatingDialog
import com.score.callmetest.ui.web.WebViewActivity
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.click
import com.score.callmetest.Constant
import com.score.callmetest.R

class AboutUsActivity : BaseActivity<ActivityAboutUsBinding, EmptyViewModel>() {
    override fun getViewBinding(): ActivityAboutUsBinding {
        return ActivityAboutUsBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = EmptyViewModel::class.java

    override fun initView() {
        GlobalManager.addViewStatusBarTopMargin(this, binding.toolbar)
        CustomUtils.flipViewIfRTL(binding.ivBack)
        CustomUtils.flipViewIfRTL(binding.userArrow)
        CustomUtils.flipViewIfRTL(binding.privateArrow)


        // 可在此初始化视图
        binding.tvVer.text = BuildConfig.VERSION_NAME
    }

    override fun initListener() {
        if (BuildConfig.DEBUG) {
            binding.logo.click {
                RatingDialog(this).show()
            }
        }
        // 返回按钮
        binding.ivBack.click {
            finish()
        }
        // 隐私政策
        binding.itemPrivacyPolicy.click {
            // 跳转到隐私政策页面
            ActivityUtils.startActivity(this@AboutUsActivity, WebViewActivity::class.java,
                Bundle().apply {
                    putString("url", Constant.PRIVACY_POLICY_URL)
                    putString("title", getString(R.string.privacy_policy))
                }
                ,false)

        }
        // 用户协议
        binding.itemUserAgreement.click {
            // 跳转到用户协议页面
            ActivityUtils.startActivity(this@AboutUsActivity, WebViewActivity::class.java,
                Bundle().apply {
                    putString("url", Constant.USER_AGREEMENT_URL)
                    putString("title", getString(R.string.user_agreement))
                }
                ,false)
        }
    }
} 