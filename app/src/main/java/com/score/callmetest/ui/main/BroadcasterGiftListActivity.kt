package com.score.callmetest.ui.main

import android.view.View
import androidx.core.content.IntentCompat
import androidx.recyclerview.widget.GridLayoutManager
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityGiftListBinding
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.network.GetGiftCountItem
import com.score.callmetest.network.GetGiftCountResponse
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.widget.GridSpacingItemDecoration
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.CustomUtils

class BroadcasterGiftListActivity : BaseActivity<ActivityGiftListBinding, GiftListViewModel>() {
    private val allGifts = mutableListOf<GetGiftCountItem>()
    private lateinit var adapter: BroadcasterGiftListAdapter

    override fun getViewBinding(): ActivityGiftListBinding = ActivityGiftListBinding.inflate(layoutInflater)
    override fun getViewModelClass(): Class<GiftListViewModel> = GiftListViewModel::class.java

    override fun initView() {
        GlobalManager.addViewStatusBarTopMargin(this, binding.llTopBar)
        CustomUtils.flipViewIfRTL(binding.ivBack)

        binding.rvGiftList.layoutManager = GridLayoutManager(this, 4)
        val spacing = resources.getDimensionPixelSize(R.dimen.gift_item_spacing)
        binding.rvGiftList.addItemDecoration(GridSpacingItemDecoration(4, spacing, false))
        adapter = BroadcasterGiftListAdapter(allGifts)
        binding.rvGiftList.adapter = adapter
        // 返回按钮
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivBack) {
            finish()
        }
    }

    override fun initData() {
        val giftResponse = IntentCompat.getParcelableExtra<GetGiftCountResponse>(intent,"gift_response", GetGiftCountResponse::class.java)
        allGifts.clear()
        giftResponse?.activityGiftNum?.let { allGifts.addAll(it) }
        giftResponse?.normalGiftNum?.let { allGifts.addAll(it) }
        giftResponse?.specialEffectsGiftNum?.let { allGifts.addAll(it) }
        
        // 按num从大到小排序
        allGifts.sortByDescending { it.num ?: 0 }
        adapter.notifyDataSetChanged()
        // 空数据占位
        if (allGifts.isEmpty()) {
            binding.rvGiftList.visibility = View.GONE
            // 可自定义空数据UI
            binding.tvTitle.text = getString(R.string.no_gift_received)
        } else {
            binding.rvGiftList.visibility = View.VISIBLE
            binding.tvTitle.text = getString(R.string.gifts_received)
        }
    }
} 