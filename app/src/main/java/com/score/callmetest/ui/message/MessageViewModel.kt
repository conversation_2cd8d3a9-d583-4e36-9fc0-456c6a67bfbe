package com.score.callmetest.ui.message

import androidx.lifecycle.viewModelScope
import com.score.callmetest.Constant
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.base.BaseViewModel
import kotlinx.coroutines.launch

class MessageViewModel : BaseViewModel() {

    // <editor-folder desc="更新在线状态">

    /**
     * 查询在线状态---添加到队列
     */
    fun checkOnlineStatus(providedList: List<String>){
        if(providedList.isEmpty()) return
        viewModelScope.launch {
            // 过滤空字符串并去重
            val userIds = providedList.filter {
                // 非空 && 非官方号 && 非机器人
                it.isNotBlank() && !StrategyManager.isTopOfficialUser(it)
                        && Constant.ROBOt_ID != it
            }.distinct()

            UserInfoManager.addUserToRefreshQueue(*userIds.toTypedArray())
        }
    }

    // </editor-folder>

} 