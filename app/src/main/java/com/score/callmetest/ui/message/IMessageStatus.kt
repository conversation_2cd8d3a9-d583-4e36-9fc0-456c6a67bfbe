package com.score.callmetest.ui.message

/**
 *  供[MessageFragment]统一更新状态并下发通知所有fragment--按需继承
 *
 * <AUTHOR>
 * @date 2025/07/25
 * @constructor 创建[IMessageStatus]
 */
interface IMessageStatus {

    /**
     * 提供用户ids，不需要判null or “”，使用时会统一判断
     * @return [List<String>]
     */
    fun provideVisibleUserIds(): List<String> = emptyList()

    fun notifyStatusChanged(statusMap: Map<String, String>){}

}