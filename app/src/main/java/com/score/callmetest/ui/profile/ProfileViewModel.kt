package com.score.callmetest.ui.profile

import android.app.Application
import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.network.OssPolicyResponse
import com.score.callmetest.network.UserInfo
import com.score.callmetest.manager.OssUploadManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.suspendCancellableCoroutine
import timber.log.Timber
import kotlin.coroutines.resume
import androidx.core.net.toUri
import com.score.callmetest.util.ActivityUtils

class ProfileViewModel(app: Application) : AndroidViewModel(app) {
    // 原始资料
    private var originalUserInfo: UserInfo? = null
    private var originalMediaUris: List<Uri> = emptyList()

    // 当前编辑资料
    var selectedAvatarUri: Uri? = null
    var selectedNickname: String? = null
    var selectedGender: Int = 0
    var selectedBirthday: String? = null
    var selectedCountry: String? = null
    var selectedAbout: String? = null
    var selectedMediaUris: List<Uri> = emptyList()

    fun recordOriginalProfile(user: UserInfo?) {
        originalUserInfo = user
        originalMediaUris =
            user?.mediaList?.mapNotNull { it.mediaUrl?.toUri() }
                ?: emptyList()
        selectedAvatarUri = null
        selectedNickname = user?.nickname
        selectedGender = user?.gender ?: 0
        selectedBirthday = user?.birthday
        selectedCountry = user?.country
        selectedAbout = user?.about
        selectedMediaUris = originalMediaUris.toList()
    }

    fun isAvatarChanged(): Boolean = selectedAvatarUri != null
    fun isNicknameChanged(): Boolean = selectedNickname != (originalUserInfo?.nickname ?: "")
    fun isGenderChanged(): Boolean = selectedGender != (originalUserInfo?.gender ?: 0)
    fun isBirthdayChanged(): Boolean = selectedBirthday != (originalUserInfo?.birthday ?: "")
    fun isCountryChanged(): Boolean = selectedCountry != (originalUserInfo?.country ?: "")
    fun isAboutChanged(): Boolean = selectedAbout != (originalUserInfo?.about ?: "")
    fun isMediaChanged(): Boolean {
        val cur = selectedMediaUris
        val ori = originalMediaUris
        if (cur.size != ori.size) return true
        for (i in cur.indices) {
            if (i >= ori.size || cur[i] != ori[i]) return true
        }
        return false
    }

    fun isBaseInfoChanged(): Boolean =
        isNicknameChanged() || isGenderChanged() || isBirthdayChanged() || isCountryChanged() || isAboutChanged()

    fun isProfileChanged(): Boolean = isAvatarChanged() || isMediaChanged() || isBaseInfoChanged()

    // 上传头像，返回ossPath或null
    private suspend fun updateAvatarIfNeeded(
        context: Context, showToast: (String) -> Unit
    ): String? {
        if (!isAvatarChanged() || selectedAvatarUri == null) return null
        // 获取OSS policy
        val policy = suspendCancellableCoroutine<OssPolicyResponse?> { cont ->
            OssUploadManager.getOssPolicy(<EMAIL>) {
                cont.resume(it)
            }
        }
        if (policy == null) {
            showToast(context.getString(R.string.failed_to_get_oss_policy))
            return null
        }
        // 压缩并上传
        val compressedPath = OssUploadManager.compressImage(context, selectedAvatarUri!!)
        if (compressedPath == null) {
            showToast(context.getString(R.string.failed_to_compress_avatar))
            return null
        }
        val ossPath = suspendCancellableCoroutine<String?> { cont ->
            OssUploadManager.uploadImageToOss(
                policy,
                compressedPath,
                object : OssUploadManager.UploadCallback {
                    override fun onSuccess(ossRelativePath: String) {
                        cont.resume(ossRelativePath)
                    }
                    override fun onFailure(error: String) {
                        Timber.tag("ProfileViewModel").e("Failed to upload avatar to OSS:  $error")
                        cont.resume(null)
                    }
                })
        }
        if (ossPath.isNullOrEmpty()) return null
        // 上传后端
        val avatarResp = UserInfoManager.updateAvatar(ossPath)
        if (!avatarResp) {
            showToast(context.getString(R.string.failed_to_update_avatar))
            return null
        }
        return ossPath
    }

    // 上传媒体，返回ossPath列表或null
    private suspend fun updateMediaIfNeeded(
        context: Context, showToast: (String) -> Unit
    ): List<String>? {
        if (!isMediaChanged()) return null
        // 获取OSS policy
        val policy = suspendCancellableCoroutine<OssPolicyResponse?> { cont ->
            OssUploadManager.getOssPolicy(<EMAIL>) {
                cont.resume(it)
            }
        }
        if (policy == null) {
            showToast(context.getString(R.string.failed_to_get_oss_policy))
            return null
        }
        val oriUrls = originalMediaUris.map { it.toString() }
        val curUrls = selectedMediaUris.map { it.toString() }
        val same = oriUrls.intersect(curUrls.toSet())
        val oldDiff = oriUrls.filter { it !in same }
        val newDiff = curUrls.filter { it !in same }
        val oriMediaList = originalUserInfo?.mediaList ?: emptyList()
        // 只上传newDiff部分
        val newDiffOssPaths = mutableListOf<String>()
        for (uri in selectedMediaUris) {
            if (uri.toString() in newDiff) {
                val compressedPath = OssUploadManager.compressImage(context, uri)
                if (compressedPath == null) {
                    showToast(context.getString(R.string.failed_to_compress_image))
                    return null
                }
                val ossPath = suspendCancellableCoroutine<String?> { cont ->
                    OssUploadManager.uploadImageToOss(
                        policy,
                        compressedPath,
                        object : OssUploadManager.UploadCallback {
                            override fun onSuccess(ossRelativePath: String) {
                                cont.resume(ossRelativePath)
                            }
                            override fun onFailure(error: String) {
                                Timber.e(error)
//                                showToast(error)
                                cont.resume(null)
                            }
                        })
                }
                if (ossPath.isNullOrEmpty()) return null
                newDiffOssPaths.add(ossPath)
            }
        }
        // 替换
        val n = minOf(oldDiff.size, newDiff.size)
        for (i in 0 until n) {
            val media = oriMediaList.find { it.mediaUrl == oldDiff[i] }
            val ossPath = newDiffOssPaths.getOrNull(i) ?: continue
            val mediaResp = UserInfoManager.updateMedia(
                actionType = 2,
                mediaPath = ossPath,
                mediaType = "photo",
                replaceMediaId = media?.mediaId
            )
            if (!mediaResp) {
                showToast(context.getString(R.string.failed_to_replace_media))
                return null
            }
        }
        // 新增
        for (i in n until newDiff.size) {
            val ossPath = newDiffOssPaths.getOrNull(i) ?: continue
            val mediaResp = UserInfoManager.updateMedia(
                actionType = 1,
                mediaPath = ossPath,
                mediaType = "photo"
            )
            if (!mediaResp) {
                showToast(context.getString(R.string.failed_to_add_media))
                return null
            }
        }
        // 删除
        for (i in n until oldDiff.size) {
            val media = oriMediaList.find { it.mediaUrl == oldDiff[i] }
            if (media?.mediaId != null) {
                val mediaResp = UserInfoManager.updateMedia(
                    actionType = 3,
                    deleteMediaId = media.mediaId,
                    mediaType = "photo"
                )
                if (!mediaResp) {
                    showToast(context.getString(R.string.failed_to_delete_media))
                    return null
                }
            }
        }
        return newDiffOssPaths
    }

    private suspend fun updateBaseInfoIfNeeded(showToast: (String) -> Unit): Boolean {
        if (isBaseInfoChanged()) {
            val saveResp = UserInfoManager.updateBaseUserInfo(
                nickname = selectedNickname,
                birthday = selectedBirthday,
                country = selectedCountry,
                about = selectedAbout
            )
            if (!saveResp) {
                ActivityUtils.getTopActivity().let {
                    it?.resources?.getString(R.string.failed_to_save_user_info).let { str ->
                        if (str != null) {
                            showToast(str)
                        }
                    }
                }
                return false
            }
            return true
        }
        return false
    }

    suspend fun refreshUserInfoIfNeeded(
        showToast: (String) -> Unit,
        onSuccess: () -> Unit
    ) {
        val userId = originalUserInfo?.userId ?: ""
        val userInfo = UserInfoManager.fetchUserInfo(userId)
        if (userInfo != null) {
            UserInfoManager.updateMyUserInfo(userInfo)
            onSuccess()
        } else {
            ActivityUtils.getTopActivity().let {
                it?.resources?.getString(R.string.failed_to_get_user_info).let { str ->
                    if (str != null) {
                        showToast(str)
                    }
                }
            }
        }
    }

    suspend fun uploadAndSaveAll(
        context: Context,
        showToast: (String) -> Unit,
        onSuccess: () -> Unit
    ) = coroutineScope {
        if (!isProfileChanged()) {
            showToast(context.getString(R.string.no_content_to_update))
            return@coroutineScope
        }
        try {
            val avatarDeferred = async { updateAvatarIfNeeded(context, showToast) }
            val mediaDeferred = async { updateMediaIfNeeded(context, showToast) }
            val baseDeferred = async { updateBaseInfoIfNeeded(showToast) }
            val avatarChanged = avatarDeferred.await()
            val mediaChanged = mediaDeferred.await()
            val baseChanged = baseDeferred.await()
            val needRefreshUserInfo = avatarChanged != null || mediaChanged != null || baseChanged
            if (needRefreshUserInfo) {
                refreshUserInfoIfNeeded(showToast, onSuccess)
            } else {
                onSuccess()
            }
        } catch (e: Exception) {
            Timber.tag("ProfileViewModel").e("网络异常: $e")
        }
    }
} 