package com.score.callmetest.ui.widget

import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.graphics.toColorInt
import com.score.callmetest.databinding.ViewVipDescItemBinding
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils.createGradientDrawable

/**
 * VIP特权描述项自定义控件
 * 显示特权图标、标题和描述信息
 */
class VipDescItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val binding: ViewVipDescItemBinding

    init {
        binding = ViewVipDescItemBinding.inflate(LayoutInflater.from(context), this, true)
    }

    /**
     * 设置背景颜色
     */
    fun setBackgroundColor() {
        binding.lineItem.background = createGradientDrawable(
            colors = intArrayOf(
                "#fffff6db".toColorInt(),
                "#fffffcf7".toColorInt()
            ),
            radius = DisplayUtils.dp2pxInternalFloat(12f),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM,

        )
    }

    /**
     * 设置特权数据
     */
    fun setData(iconRes: Int, title: String, description: String) {
        binding.ivIcon.setBackgroundResource(iconRes)
        binding.tvTitle.text = title
        binding.tvDesc.text = description
    }

    /**
     * 设置特权数据（支持高亮数字）
     */
    fun setDataWithHighlight(iconRes: Int, title: String, description: String, highlightText: String, backupHighlightText: String? = null) {
        binding.ivIcon.setBackgroundResource(iconRes)
        binding.tvTitle.text = title

        // 高亮显示指定文本
        val spannableString = android.text.SpannableString(description)
        val highlightColor = "#FFFF7E00".toColorInt()
        var startIndex = description.indexOf(highlightText)
        if (startIndex == -1 && backupHighlightText != null) {
            startIndex = description.indexOf(backupHighlightText)
        }
        if (startIndex != -1) {
            spannableString.setSpan(
                android.text.style.ForegroundColorSpan(highlightColor),
                startIndex,
                startIndex + highlightText.length,
                android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        binding.tvDesc.text = spannableString
    }

}
