package com.score.callmetest.ui.widget

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogVipBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.manager.VipManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.ui.widget.VipItemCardView.OnCardClickListener
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.DialogUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EmojiUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.click

// agree 按钮在下面，cancel 在上面

class VipDialog(
    context: Context,
) : Dialog(context, androidx.appcompat.R.style.Theme_AppCompat_Dialog) {
    private var binding: DialogVipBinding = DialogVipBinding.inflate(LayoutInflater.from(context))
    private var selectedItem: VipItemCardView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        setCancelable(true)

        // 设置全屏/居中
        window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        // 隐藏状态栏
        window?.decorView?.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
        window?.setBackgroundDrawableResource(R.color.transparent)
        // 点击空白区域可取消
        setCanceledOnTouchOutside(true)

        // 设置内容左右37dp边距
        val paddingPx = DisplayUtils.dp2pxInternal(15f)
        binding.root.setPadding(
            paddingPx,
            DisplayUtils.dp2pxInternal(50f),
            paddingPx,
            DisplayUtils.dp2pxInternal(99f)
        )
        binding.iconView.click {
           dismiss()
        }
        binding.dialogContent.click {
            // 拦截点击事件，不关闭弹窗
        }
        
        // 设置背景点击关闭Dialog
        binding.root.click {
            try {
                DialogUtils.safeDismiss(this)
            } catch (e: Exception) {
                // 如果safeDismiss失败，尝试直接dismiss
                try {
                    dismiss()
                } catch (e2: Exception) {
                    // 记录错误但不崩溃
                    timber.log.Timber.tag("VipDialog").e(e2, "Failed to dismiss dialog")
                }
            }
        }

        setupVipCards()

        // 高亮显示指定文本
        val num = VipManager.VIP_DESC_NUM
        val spannableString = SpannableString(context.getString(R.string.become_vip_privileges, num))
        val highlightText = "$num"
        val highlightColor = "#FFFF4444".toColorInt()
        val startIndex = spannableString.indexOf(highlightText)
        if (startIndex != -1) {
            spannableString.setSpan(
                android.text.style.ForegroundColorSpan(highlightColor),
                startIndex,
                startIndex + highlightText.length,
                android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        binding.vipDescInfo.text = spannableString

        binding.bottomShadow.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#00FFFFFF".toColorInt(), Color.WHITE, Color.WHITE),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM
        )


        binding.vipButtonLayout.click {
            if (selectedItem != null) {
                ActivityUtils.getTopActivity()?.let {
                    // 重置StayDialog显示次数
                    EmojiUtils.resetStayDialogCount()
                    RechargeManager.startRecharge(
                        activity = it,
                        goodsCode = selectedItem!!.getData()!!.code.toString(),
                        goodsName = selectedItem!!.getGoodsName(),
                        entry = RechargeSource.SUBSCRIBE_DIALOG,
                    )
                    try {
                        DialogUtils.safeDismiss(this)
                    } catch (e: Exception) {
                        // 如果safeDismiss失败，尝试直接dismiss
                        try {
                            dismiss()
                        } catch (e2: Exception) {
                            // 记录错误但不崩溃
                            timber.log.Timber.tag("VipDialog").e(e2, "Failed to dismiss dialog in VIP button")
                        }
                    }
                }
            }
        }
        refreshData()
    }

    private fun setupVipCards() {
        val list = GoodsManager.getCachedVipGoods()
        selectedItem = binding.vipCard1month
        if (list.size > 0) {
            binding.vipCard1month.setData(
                GoodsManager.getCachedVipGoods()[0],
                R.drawable.vip_item_icon_1,
                style = VipItemCardView.Style.DARK
            ).setOnCardClickListener(object : OnCardClickListener {
                override fun onCardClick(data: GoodsInfo?) {
                    selectedItem = binding.vipCard1month
                    refreshData()
                }
            })
        } else {
            binding.vipCard1month.visibility = View.GONE
            binding.vipCard3month.visibility = View.GONE
            binding.vipCard12month.visibility = View.GONE
        }

        if (list.size > 1) {
            binding.vipCard3month.setData(
                GoodsManager.getCachedVipGoods()[1],
                R.drawable.vip_item_icon_2
            ).setOnCardClickListener(object : OnCardClickListener {
                override fun onCardClick(data: GoodsInfo?) {
                    selectedItem = binding.vipCard3month
                    refreshData()
                }
            })
        } else {
            binding.vipCard3month.visibility = View.GONE
            binding.vipCard12month.visibility = View.GONE
        }

        if (list.size > 2) {
            binding.vipCard12month.setData(
                GoodsManager.getCachedVipGoods()[2],
                R.drawable.vip_item_icon_3
            ).setOnCardClickListener(object : OnCardClickListener {
                override fun onCardClick(data: GoodsInfo?) {
                    selectedItem = binding.vipCard12month
                    refreshData()
                }
            })
        } else {
            binding.vipCard12month.visibility = View.GONE
        }
    }

    private fun refreshData() {
        binding.vipCard1month.updateUI(VipItemCardView.Style.LIGHT)
        binding.vipCard3month.updateUI(VipItemCardView.Style.LIGHT)
        binding.vipCard12month.updateUI(VipItemCardView.Style.LIGHT)
        selectedItem?.updateUI(VipItemCardView.Style.DARK)
        // 设置VIP特权描述项数据
        setupVipPrivileges()
    }

    /**
     * 设置VIP特权描述项数据
     */
    private fun setupVipPrivileges() {
        binding.vipDescCoinsBonus.setBackgroundColor()
        // 1. 金币奖励
        binding.vipDescCoinsBonus.setDataWithHighlight(
            R.drawable.vip_item_1,
            context.getString(R.string.coins_bonus),
            context.getString(R.string.get_coins_after_activating, "${selectedItem?.getData()?.exchangeCoin ?: 0}"),
            "${selectedItem?.getData()?.exchangeCoin}"
        )
        binding.vipDescRechargeBonus.setBackgroundColor()
        // 2. 充值奖励
        binding.vipDescRechargeBonus.setDataWithHighlight(
            R.drawable.vip_item_2,
            context.getString(R.string.recharge_bonus),
            context.getString(R.string.up_to_20_percent_extra),
            "20%",
            "%20"
        )
        binding.vipDescCallDiscount.setBackgroundColor()
        // 3. 通话折扣
        binding.vipDescCallDiscount.setDataWithHighlight(
            R.drawable.vip_item_3,
            context.getString(R.string.call_discount),
            context.getString(R.string.vip_video_call_discount),
            "10%",
            "%10"
        )
        binding.vipDescMatchCall.setBackgroundColor()
        // 4. 匹配通话优惠
        binding.vipDescMatchCall.setDataWithHighlight(
            R.drawable.vip_item_4,
            context.getString(R.string.match_call_offer),
            context.getString(R.string.vip_match_call_discount),
            "10%",
            "%10"
        )
        binding.vipDescTextChat.setBackgroundColor()
        // 5. 免费文字聊天
        binding.vipDescTextChat.setData(
            R.drawable.vip_item_5,
            context.getString(R.string.free_text_chat),
            context.getString(R.string.text_chat_all_free)
        )
        binding.vipDescPhotoAlbum.setBackgroundColor()
        // 6. 查看照片相册
        binding.vipDescPhotoAlbum.setData(
            R.drawable.vip_item_6,
            context.getString(R.string.view_photo_album),
            context.getString(R.string.check_all_photos_free)
        )
        binding.vipDescVisitorList.setBackgroundColor()
        // 7. 查看访客列表
        binding.vipDescVisitorList.setData(
            R.drawable.vip_item_7,
            context.getString(R.string.view_visitor_list),
            context.getString(R.string.unlock_visitor_list)
        )
        binding.vipDescStatus.setBackgroundColor()
        // 8. 精致VIP状态
        binding.vipDescStatus.setData(
            R.drawable.vip_item_8,
            context.getString(R.string.exquisite_vip_status),
            context.getString(R.string.distinguished_status_unique)
        )

    }
    
    /**
     * 安全地关闭Dialog
     * 避免"not attach to window"崩溃
     */
    fun safeDismiss() {
        try {
            DialogUtils.safeDismiss(this)
        } catch (e: Exception) {
            // 如果safeDismiss失败，尝试直接dismiss
            try {
                dismiss()
            } catch (e2: Exception) {
                timber.log.Timber.tag("VipDialog").e(e2, "Failed to dismiss dialog in safeDismiss")
            }
        }
    }

}