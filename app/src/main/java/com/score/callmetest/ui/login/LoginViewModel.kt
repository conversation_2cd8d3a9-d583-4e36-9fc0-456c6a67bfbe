package com.score.callmetest.ui.login

import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.network.LoginData
import com.score.callmetest.ui.base.BaseViewModel
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.SharePreferenceUtil
import timber.log.Timber

class LoginViewModel : BaseViewModel() {
    // 首次/非首次注册逻辑
    val isFirstLogin = SharePreferenceUtil.getBoolean(Constant.KEY_IS_FIRST_LOGIN, true)
    val lastLoginType = SharePreferenceUtil.getString(Constant.KEY_LAST_LOGIN_TYPE, null)

    fun saveLoginData(token: String, isFirstLogin: Boolean, loginType: String) {
        SharePreferenceUtil.putString(Constant.TOKEN_KEY, token)
        SharePreferenceUtil.putString(Constant.KEY_LAST_LOGIN_TYPE, loginType)
    }

    fun login(
        oauthType: Int = 4,
        token: String,
        onSuccess: (LoginData) -> Unit,
        onError: (String) -> Unit
    ) {
        // 先获取配置
        AppConfigManager.getAppConfig(
            onSuccess = { configData ->
                AppConfigManager.saveAppConfig(configData)
                doLogin(oauthType, token, onSuccess, onError)
            },
            onError = { errorMsg ->
                Timber.e(errorMsg)
                val errorMsg = if(errorMsg == "-1") CustomUtils.getString(R.string.login_error)
                else errorMsg
                onError(errorMsg)
            }
        )
    }

} 