package com.score.callmetest.ui.widget

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.os.CountDownTimer
import android.text.Spanned
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogStayBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DialogUtils
import com.score.callmetest.util.click

class StayDialog(
    context: Context,
    @DrawableRes private val emojiResId: Int? = null,
    private val content: Any? = null, // String 或 Spanned
    private val continueText: String = "",
    private val cancelText: String = "",
    private val countdownSeconds: Int = 6,
    // 支付相关参数
    private val activity: Activity? = null,
    private val goodsCode: String? = null,
    private val goodsName: String? = null,
    private val entry: RechargeSource? = null,
    private val invitationId: String? = null,
    private val bcInvitationId: String? = null,
    private val onContinue: (() -> Unit)? = null,
    private val onCancel: (() -> Unit)? = null
) : Dialog(context, androidx.appcompat.R.style.Theme_AppCompat_Dialog) {
    private var binding: DialogStayBinding = DialogStayBinding.inflate(LayoutInflater.from(context))
    private var countDownTimer: CountDownTimer? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        
        // 设置全屏/居中显示
        window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        window?.setBackgroundDrawableResource(R.color.transparent)
        
        // 设置内容左右29dp边距
        val paddingPx = (29 * context.resources.displayMetrics.density).toInt()
        binding.root.setPadding(
            paddingPx,
            binding.root.paddingTop,
            paddingPx,
            binding.root.paddingBottom
        )

        // 设置emoji图片
        emojiResId?.let {
            binding.emoji.visibility = View.VISIBLE
            binding.emoji.setImageResource(it)
        } ?: run {
            binding.emoji.visibility = View.GONE
        }

        // 设置内容文本 - 传入参数时使用传入的，否则使用XML默认值
        when (content) {
            is Spanned -> binding.tvContent.text = content
            is String -> binding.tvContent.text = content
            else -> {
                // 使用XML中定义的默认文本，不需要额外设置
            }
        }

        // 设置继续充值按钮文本 - 传入参数时使用传入的，否则使用XML默认值
        if (continueText.isNotEmpty()) {
            binding.tvContinue.text = continueText
        }
        // 如果continueText为空，则使用XML中定义的默认文本

        // 设置取消按钮文本 - 传入参数时使用传入的，否则使用XML默认值
        if (cancelText.isNotEmpty()) {
            binding.tvCancel.text = cancelText
        }
        // 如果cancelText为空，则使用XML中定义的默认文本

        // 设置倒计时
        startCountdown()

        // 播放SVGA动画
        playSvgaAnimation()

        // 设置点击事件
        binding.svgaContinue.click {
            // 执行支付逻辑
            startPayment()
            onContinue?.invoke()
            dismiss()
        }

        binding.tvCancel.click {
            onCancel?.invoke()
            dismiss()
        }
    }

    private fun startCountdown() {
        countDownTimer?.cancel()
        countDownTimer = object : CountDownTimer((countdownSeconds * 1000).toLong(), 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val secondsLeft = (millisUntilFinished / 1000).toInt()
                binding.tvCountdown.text = "(${secondsLeft}s)"
            }

            override fun onFinish() {

                onContinue?.invoke()
                // 检查对话框是否仍然显示且窗口有效
                if (isShowing && window?.isActive == true) {
                    binding.tvCountdown.text = "0s"
                    // 倒计时结束后自动执行支付逻辑
                    startPayment()
                    dismiss()
                }

            }
        }
        countDownTimer?.start()
    }

    /**
     * 启动支付流程
     */
    private fun startPayment() {
        // 检查支付参数是否完整
        if (activity == null || goodsCode == null || entry == null) {
            return
        }
        
        // 使用传入的Activity或获取当前顶层Activity
        val targetActivity = activity ?: ActivityUtils.getTopActivity()
        targetActivity?.let {
            RechargeManager.startRecharge(
                activity = it,
                goodsCode = goodsCode,
                goodsName = goodsName,
                entry = entry,
                invitationId = invitationId,
                bcInvitationId = bcInvitationId
            )
        }
    }

    /**
     * 播放SVGA动画
     */
    private fun playSvgaAnimation() {
        CustomUtils.playSvga(
            svgaView = binding.svgaContinue,
            assetName = "btn_stay_dialog.svga",
            loops = 0, // 无限循环
        )
    }

    override fun dismiss() {
        countDownTimer?.cancel()
        super.dismiss()
    }
    
    /**
     * 安全地关闭Dialog
     * 避免"not attach to window"崩溃
     */
    fun safeDismiss() {
        countDownTimer?.cancel()
        DialogUtils.safeDismiss(this)
    }

}