package com.score.callmetest.ui.videocall.ongoing.manager

import android.util.Log
import androidx.lifecycle.LifecycleOwner
import com.score.callmetest.R
import com.score.callmetest.entity.VideoCallMessageEntity
import com.score.callmetest.im.callback.ImOnReceiveMessageListener
import com.score.callmetest.im.callback.ImSendMsgCallback
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.manager.OnChatMessage
import com.score.callmetest.constants.RongCloudConstants
import com.score.callmetest.constants.SocketCommands
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.SocketManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.ToastUtils
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import org.json.JSONObject
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap

/**
 * 双通道消息管理器
 * 同时使用 Socket 和融云 IM 发送和接收消息
 *
 * 功能特点：
 * 1. 双通道发送：同时通过 Socket 和融云 IM 发送消息
 * 2. 双通道接收：监听两个通道的消息，过滤重复消息
 * 3. 时间戳去重：基于时间戳过滤相同消息
 * 4. 可靠性保证：一个通道失败时另一个通道仍可工作
 *
 */
class DualChannelMessageManager(
    private val currentUserId: String,
    private val targetUserId: String,
    var targetUserName: String,
    var targetUserAvatar: String
) {
    
    companion object {
        private const val TAG = "DualChannelMessage"
        private const val DUPLICATE_TIME_THRESHOLD = 1000L // 1秒内的消息认为是重复的
    }
    
    /**
     * 消息回调接口
     */
    interface MessageCallback {
        /**
         * 消息发送成功
         */
        fun onMessageSent(message: VideoCallMessageEntity)
        
        /**
         * 消息发送失败
         */
        fun onMessageSendFailed(message: VideoCallMessageEntity, error: String)
        
        /**
         * 接收到新消息
         */
        fun onMessageReceived(message: VideoCallMessageEntity)
    }
    
    private var messageCallback: MessageCallback? = null

    // 用于去重的消息时间戳缓存
    private val receivedMessageTimestamps = ConcurrentHashMap<String, Long>()

    // 清理过期时间戳的间隔（5分钟）
    private val cleanupInterval = 5 * 60 * 1000L
    private var lastCleanupTime = System.currentTimeMillis()
    
    /**
     * 设置消息回调
     */
    fun setMessageCallback(callback: MessageCallback) {
        this.messageCallback = callback
    }
    
    /**
     * 开始监听消息
     * 需要在 Fragment 或 Activity 中调用
     */
    fun startListening(lifecycleOwner: LifecycleOwner) {
        // 监听 message 消息
        DualChannelEventManager.observeOnChat(lifecycleOwner) { socketMessage ->
            handleMessage(socketMessage)
        }
    }
    
    /**
     * 发送消息（双通道）
     */
    fun sendMessage(content: String) {
        val timestamp = System.currentTimeMillis()
        val messageId = generateMessageId()
        
        // 创建消息实体
        val message = VideoCallMessageEntity.createSentMessage(
            messageId = messageId,
            senderId = currentUserId,
            senderName = UserInfoManager.myUserInfo?.nickname ?: CustomUtils.getString(R.string.me),
            senderAvatar = UserInfoManager.myUserInfo?.avatarUrl ?: "",
            receiverId = targetUserId,
            content = content
        ).copy(timestamp = timestamp)
        
        // 验证消息内容
        val (isValid, errorMsg) = validateMessageContent(content)
        if (!isValid) {
            messageCallback?.onMessageSendFailed(message, errorMsg ?: "消息内容无效")
            return
        }
        
        // 记录发送的消息时间戳，避免接收到自己发送的消息
        addMessageTimestamp(generateTimestampKey(currentUserId, timestamp), timestamp)
        
        var socketSuccess = false
        var rongCloudSuccess = false
        val errorMessages = mutableListOf<String>()
        
        // 通道1：Socket 发送
        try {
            sendViaSocket(message)
            socketSuccess = true
            Timber.tag(TAG).d("Socket 发送成功")
        } catch (e: Exception) {
            val error = "Socket发送失败: ${e.message}"
            errorMessages.add(error)
            Timber.tag(TAG).e(e, error)
        }
        
        // 通道2：融云 IM 发送
        try {
            sendViaRongCloud(message) { success, error ->
                rongCloudSuccess = success
                if (!success) {
                    errorMessages.add("融云发送失败: $error")
                    Timber.tag(TAG).e("融云发送失败: $error")
                }
                
                // 当两个通道都尝试完成后，判断结果
                if (socketSuccess || rongCloudSuccess) {
                    messageCallback?.onMessageSent(message)
                    if (errorMessages.isNotEmpty()) {
                        Timber.tag(TAG).w("部分通道发送失败: ${errorMessages.joinToString(", ")}")
                    }
                } else {
                    val allErrors = errorMessages.joinToString("; ")
                    messageCallback?.onMessageSendFailed(message, "所有通道发送失败: $allErrors")
                }
            }
        } catch (e: Exception) {
            val error = "融云发送异常: ${e.message}"
            errorMessages.add(error)
            Timber.tag(TAG).e(e, error)
            
            // 如果 Socket 也失败了，则整体失败
            if (!socketSuccess) {
                val allErrors = errorMessages.joinToString("; ")
                messageCallback?.onMessageSendFailed(message, "所有通道发送失败: $allErrors")
            } else {
                // Socket 成功，融云失败，仍然算成功但记录警告
                messageCallback?.onMessageSent(message)
                Timber.tag(TAG).w("融云通道失败但Socket成功: $error")
            }
        }
    }
    
    /**
     * 通过 Socket 发送消息
     */
    private fun sendViaSocket(message: VideoCallMessageEntity) {
        val data = JSONObject().apply {
            put("fromUserId", message.senderId)
            put("toUserId", message.receiverId)
            put("content", message.content)
            put("timestamp", message.timestamp)
            put("command", SocketCommands.Message.ON_CHAT)
        }
        SocketManager.instance.sendMessage(RongCloudConstants.MESSAGE_EVENT, data)
    }
    
    /**
     * 通过融云 IM 发送消息
     */
    private fun sendViaRongCloud(message: VideoCallMessageEntity, callback: (Boolean, String?) -> Unit) {
        RongCloudManager.sendCommandMessage(
            targetId = message.receiverId,
            fromUserId = message.senderId,
            content = message.content,
            timestamp = message.timestamp,
            callback = object : ImSendMsgCallback() {
                override fun success(rongMessage: Message?) {
                    callback(true, null)
                }

                override fun failed(code: Int?, errorMsg: String?) {
                    callback(false, errorMsg)
                }
            }
        )
    }
    
    /**
     * 处理 Socket 消息
     */
    private fun handleMessage(socketMessage: OnChatMessage) {
        if (!isTargetMessage(socketMessage.fromUserId, socketMessage.toUserId)) {
            return
        }
        
        val content = socketMessage.content ?: return
        val timestamp = socketMessage.timestamp ?: System.currentTimeMillis()
        val timestampKey = generateTimestampKey(socketMessage.fromUserId ?: "", timestamp)
        
        // 检查是否重复消息
        if (isDuplicateMessage(timestampKey, timestamp)) {
            Timber.tag(TAG).d("过滤重复的Socket消息: $timestampKey")
            return
        }
        
        // 记录消息时间戳
        addMessageTimestamp(timestampKey, timestamp)
        
        // 创建消息实体
        val message = createReceivedMessage(socketMessage.fromUserId, content, timestamp)
        if (message != null) {
            Timber.tag(TAG).d("接收到Socket消息: ${message.content}")
            messageCallback?.onMessageReceived(message)
        }
    }
    
    /**
     * 创建接收到的消息实体
     */
    private fun createReceivedMessage(fromUserId: String?, content: String, timestamp: Long): VideoCallMessageEntity? {
        if (fromUserId.isNullOrBlank() || content.isBlank()) {
            return null
        }
        
        return VideoCallMessageEntity.createReceivedMessage(
            messageId = generateMessageId(),
            senderId = fromUserId,
            senderName = targetUserName,
            senderAvatar = targetUserAvatar,
            receiverId = currentUserId,
            content = content
        ).copy(timestamp = timestamp)
    }
    
    /**
     * 检查是否是目标消息
     */
    private fun isTargetMessage(fromUserId: String?, toUserId: String?): Boolean {
        return (fromUserId == targetUserId && toUserId == currentUserId)
    }
    
    /**
     * 生成时间戳键
     */
    private fun generateTimestampKey(fromUserId: String, timestamp: Long): String {
        return "${fromUserId}_$timestamp"
    }
    
    /**
     * 检查是否是重复消息（线程安全）
     */
    private fun isDuplicateMessage(timestampKey: String, timestamp: Long): Boolean {
        synchronized(receivedMessageTimestamps) {
            val existingTimestamp = receivedMessageTimestamps[timestampKey]
            return existingTimestamp != null &&
                   Math.abs(timestamp - existingTimestamp) < DUPLICATE_TIME_THRESHOLD
        }
    }
    
    /**
     * 添加消息时间戳（线程安全）
     */
    private fun addMessageTimestamp(timestampKey: String, timestamp: Long) {
        synchronized(receivedMessageTimestamps) {
            receivedMessageTimestamps[timestampKey] = timestamp

            // 定期清理过期的时间戳
            val now = System.currentTimeMillis()
            if (now - lastCleanupTime > cleanupInterval) {
                cleanupExpiredTimestamps(now)
                lastCleanupTime = now
            }
        }
    }
    
    /**
     * 清理过期的时间戳（线程安全）
     */
    private fun cleanupExpiredTimestamps(currentTime: Long) {
        synchronized(receivedMessageTimestamps) {
            val expireTime = currentTime - cleanupInterval
            val iterator = receivedMessageTimestamps.entries.iterator()
            var cleanedCount = 0

            while (iterator.hasNext()) {
                val entry = iterator.next()
                if (entry.value < expireTime) {
                    iterator.remove()
                    cleanedCount++
                }
            }

            if (cleanedCount > 0) {
                Timber.tag(TAG).d("清理了 $cleanedCount 个过期时间戳")
            }
        }
    }
    
    /**
     * 验证消息内容
     */
    private fun validateMessageContent(content: String): Pair<Boolean, String?> {
        return when {
            content.isBlank() -> false to "消息内容不能为空"
            content.length > 500 -> false to "消息内容过长，最多500个字符"
            else -> true to null
        }
    }
    
    /**
     * 生成消息ID
     */
    private fun generateMessageId(): String {
        return "msg_${System.currentTimeMillis()}_${(Math.random() * 1000).toInt()}"
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        receivedMessageTimestamps.clear()
        messageCallback = null

        Timber.tag(TAG).d("双通道消息管理器已清理")
    }
}
