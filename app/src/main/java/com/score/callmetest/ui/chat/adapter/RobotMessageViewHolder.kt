package com.score.callmetest.ui.chat.adapter

import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.UnderlineSpan
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemChatMessageRechargeCardBinding
import com.score.callmetest.databinding.ItemChatMessageRobotBinding
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.ImCardEntity
import com.score.callmetest.entity.isRobot
import com.score.callmetest.manager.TranslateManager
import com.score.callmetest.network.FAQInfoList
import com.score.callmetest.network.FaqInfo
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.LanguageUtils
import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.CoroutineScope
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 机器人客服消息
 */
internal class RobotMessageViewHolder(
    val scope: CoroutineScope,
    private val binding: ItemChatMessageRobotBinding,
    private val mChatMessageListeners: ChatAdapterListeners
) : RecyclerView.ViewHolder(binding.root), MessageHolder {

    private var mCurrentMessage: ChatMessageEntity? = null

    /**
     * 是否正在翻译。。。
     */
    private val mIsTransIng = AtomicBoolean(false)

    init {

        // 图片点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivImage) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnImageClickListener?.invoke(message,binding.ivImage)
            }
        }

        // 翻译按钮点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.tvTranslate) {
            mCurrentMessage?.let { message ->
                if (mIsTransIng.get()) {
                    // 正在翻译---点击无效
                    Timber.d("正在翻译---点击无效")
                    return@let
                }
                // 通知外部已经开始翻译
                mChatMessageListeners.mOnTranslateClickListener?.invoke(message)
                translateMessage()
            }
        }

    }

    /**
     * 绑定消息数据
     * @param message 消息实体
     */
    override fun bind(message: ChatMessageEntity) {
        mCurrentMessage = message

        try {
            // 客服账号
            /*GlideUtils.load(
                view = binding.ivAvatar,
                url = R.drawable.customer_service,
                placeholder = R.drawable.customer_service,
                isCircle = true
            )*/

            // 优先判断是否是图片显示
            if(message.mediaUri != null && message.content.isEmpty()){
                binding.messageContainer.visibility = View.GONE
                binding.ivImage.visibility = View.VISIBLE

                showTranslateButton(false)

                // 加载图片
                val uri = message.mediaUri
                GlideUtils.load(
                    view =binding.ivImage,
                    url = uri,
                    placeholder = R.drawable.image_placeholder,
                    error = R.drawable.image_placeholder,
                    radius = DisplayUtils.dp2px(12f)
                )

                return
            }

            // 非图片
            binding.messageContainer.visibility = View.VISIBLE
            binding.ivImage.visibility = View.GONE

            // 根据消息内容判断是否显示翻译按钮
            if (message.content.isNotEmpty()) {
                // FAQ消息：构建带超链接的文本
                val faqSpannableString = createFaqSpannableString(message)
                binding.tvMessage.isTouchable = false
                binding.tvMessage.text = faqSpannableString
                binding.tvMessage.movementMethod = LinkMovementMethod.getInstance()
                binding.tvMessage.setOnClickListener(null)
                binding.tvMessage.setOnLongClickListener(null)
                binding.tvMessage.isClickable = false
                binding.tvMessage.isLongClickable = false

                binding.tvMessage.visibility = View.VISIBLE
                binding.ivImage.visibility = View.GONE
                showTranslateButton(true)
                // 检查是否开启了自动翻译
                if(message.isAutoTrans){
                    binding.tvMessage.post { translateMessage() }
                }

            } else {
                showTranslateButton(false)
            }

        } catch (e: Exception) {
            Timber.e(e, "Error binding recharge card message")
        }
    }

    /**
     * 创建FAQ消息的SpannableString，将问题设置为超链接样式
     * @param message FAQ消息实体
     * @return 带有超链接的SpannableString
     */
    private fun createFaqSpannableString(message: ChatMessageEntity): SpannableString {
        val content = message.content
        val spannableString = SpannableString(content)

        try {
            // 解析FAQ信息
            val extraInfo = if (!message.extra.isNullOrEmpty()) {
                // 解析extra字段
                val gson = Gson()
                try {
                    if(message.contentType.equals(FAQInfoList::class.java.simpleName)){
                        gson.fromJson(message.extra, FAQInfoList::class.java)
                    }else if(message.contentType.equals(FaqInfo::class.java.simpleName)){
                        gson.fromJson(message.extra, FaqInfo::class.java)
                    }else{
                        null
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Failed to parse FAQ info from extra")
                    null
                }

            } else null

            if(extraInfo == null){
                return spannableString
            }

            // 改content的样式
            if (extraInfo is FAQInfoList) {
                // 创建FAQ首条消息
                createFaqFirstMessage(spannableString, extraInfo, message)
            }else if(extraInfo is FaqInfo){
                // 创建FAQ后续消息
                createFaqSecondMessage(spannableString, extraInfo, message)
            }

        } catch (e: Exception) {
            Timber.e(e, "Failed to create FAQ spannable string")
        }

        return spannableString
    }

    /**
     * 创建FAQ首条消息
     * @param [spannableString] spannable字符串
     * @param [faqInfoList] 常见问题信息列表
     * @param [message] 消息
     */
    private fun createFaqFirstMessage(spannableString: SpannableString, faqInfoList: FAQInfoList, message: ChatMessageEntity){
        val content = message.content
        // 为每个问题设置超链接样式和点击事件
        faqInfoList.faqInfoList?.forEach { faqInfo ->
            val question = faqInfo.question
            val startIndex = content.indexOf(question)

            if (startIndex != -1) {
                val endIndex = startIndex + question.length

                // 创建点击事件
                val clickableSpan = object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        // 触发FAQ问题点击事件
                        mChatMessageListeners.mOnFaqQuestionClickListener?.invoke(faqInfo.code, message)
                    }

                    override fun updateDrawState(ds: android.text.TextPaint) {
                        super.updateDrawState(ds)
                        // 设置超链接样式：颜色、下划线
                        ds.color = ContextCompat.getColor(binding.root.context, R.color.link_color)
                        ds.isUnderlineText = false
                    }
                }

                // 应用点击事件和样式
                spannableString.setSpan(
                    clickableSpan,
                    startIndex,
                    endIndex,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )

                // 添加下划线样式（备用）
                // 暂时不需要下划线
                /*   spannableString.setSpan(
                       UnderlineSpan(),
                       startIndex,
                       endIndex,
                       Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                   )*/
            }
        }
    }

    /**
     * 创建FAQ具体消息
     * @param [spannableString] spannable字符串
     * @param [faqInfo] 具体问题信息
     * @param [message] 消息
     */
    private fun createFaqSecondMessage(spannableString: SpannableString, faqInfo: FaqInfo, message: ChatMessageEntity){
        val content = message.content
        // 为问题里面指定消息内容设置超链接样式和点击事件
        faqInfo.messageAnswer?.answerEventHandleList?.forEach { answerEventHandle ->
            val matchStr = answerEventHandle.matchStr
            val startIndex = content.indexOf(matchStr)

            if (startIndex != -1) {
                val endIndex = startIndex + matchStr.length

                // 创建点击事件
                val clickableSpan = object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        // 触发点击事件
                        mChatMessageListeners.mOnFaqContentClickListener?.invoke(answerEventHandle, message)
                    }

                    override fun updateDrawState(ds: android.text.TextPaint) {
                        super.updateDrawState(ds)
                        // 设置超链接样式：颜色、下划线
                        ds.color = ContextCompat.getColor(binding.root.context, R.color.link_color)
                        ds.isUnderlineText = false
                    }
                }

                // 应用点击事件和样式
                spannableString.setSpan(
                    clickableSpan,
                    startIndex,
                    endIndex,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )

                // 添加下划线样式（备用）
                spannableString.setSpan(
                    UnderlineSpan(),
                    startIndex,
                    endIndex,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }
    }

    // region translate翻译相关
    /**
     * 翻译消息
     */
    private fun translateMessage() {
        showTranslateLoading(true)
        // 翻译前期准备
        val needTranslate = mCurrentMessage?.content
        if(needTranslate.isNullOrEmpty()) {
            showTranslateButton(true)
            return
        }
        LanguageUtils.getAppLanguage(binding.root.context, scope) { targetLang ->
            // 内部有分配线程执行翻译
            TranslateManager.translate(scope, needTranslate, targetLang) { newTxt ->
                ThreadUtils.runOnMain {
                    showTranslateLoading(false)
                    if (newTxt == null) {
                        // 翻译失败
                        Timber.d("翻译失败")
                        showTranslateButton(true)
                        return@runOnMain
                    }
                    // 翻译成功
                    Timber.d("翻译成功--$needTranslate-->$newTxt")
                    showTranslateResult(newTxt)
                }
            }
        }

    }
    private fun showTranslateButton(show: Boolean) {
        binding.tvTranslate.visibility = if (show) View.VISIBLE else View.GONE
        binding.progressTranslate.visibility = View.GONE
    }

    private fun showTranslateLoading(show: Boolean) {
        binding.tvTranslate.visibility = if (show) View.VISIBLE else View.GONE
        binding.progressTranslate.visibility = if (show) View.VISIBLE else View.GONE
        mIsTransIng.set(show)
    }

    private fun showTranslateResult(text: String) {
        binding.tvTranslate.visibility = View.GONE
        binding.progressTranslate.visibility = View.GONE
        binding.tvTranslateContent.text = text
        binding.lineTranslate.visibility = View.VISIBLE
        binding.tvTranslateContent.visibility = View.VISIBLE
    }
    // endregion

}