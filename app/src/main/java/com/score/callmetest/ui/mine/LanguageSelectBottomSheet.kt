package com.score.callmetest.ui.mine

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.View.OVER_SCROLL_NEVER
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.R
import com.score.callmetest.util.AppLanguageManager
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.LanguageUtils
import com.score.callmetest.util.click
import timber.log.Timber

class LanguageSelectBottomSheet(
    private val onLanguageSelected: (String) -> Unit
) : BottomSheetDialogFragment() {

    companion object {
        private const val TAG = "LanguageSelectBottomSheet"
    }

    private lateinit var languageAdapter: LanguageSelectAdapter

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        // 禁用默认背景和拖动功能
        dialog.setOnShowListener {
            try {
                val bottomSheet =
                    dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
                bottomSheet?.setBackgroundResource(android.R.color.transparent)

                // 禁用拖动功能
                val behavior =
                    com.google.android.material.bottomsheet.BottomSheetBehavior.from(bottomSheet)
                behavior.isDraggable = false
                behavior.isHideable = false
            } catch (e: Exception) {

            }
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.bottom_sheet_language_select, container, false)
        
        // 设置底部弹窗内容区域圆角背景（内容区域）
        DrawableUtils.createRoundRectDrawable(
            Color.WHITE,
            floatArrayOf(
                DisplayUtils.dp2pxInternalFloat(20f), DisplayUtils.dp2pxInternalFloat(20f), // 左上
                DisplayUtils.dp2pxInternalFloat(20f), DisplayUtils.dp2pxInternalFloat(20f), // 右上
                0f, 0f, // 右下
                0f, 0f  // 左下
            )
        ).also { view.background = it }
        
        val recyclerView = view.findViewById<RecyclerView>(R.id.rv_language_list)
        val cancelButton = view.findViewById<TextView>(R.id.tv_cancel)
        val confirmButton = view.findViewById<TextView>(R.id.tv_confirm)
        
        // 初始化适配器
        languageAdapter = LanguageSelectAdapter { languageCode ->
            languageAdapter.updateSelectedLanguage(languageCode)
        }
        recyclerView.overScrollMode = OVER_SCROLL_NEVER
        // 设置RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        recyclerView.adapter = languageAdapter
        
        // 设置初始选中的语言
        val currentLanguage = AppLanguageManager.getCurrentAppLanguage(requireContext())
        languageAdapter.updateSelectedLanguage(currentLanguage)

        // 设置取消和确认按钮
        cancelButton.click {
            dismiss()
        }
        
        confirmButton.click {
            val selectedLanguage = languageAdapter.getSelectedLanguage()
            val currentLanguage = AppLanguageManager.getCurrentAppLanguage(requireContext())
            
            // 如果选择的语言与当前语言相同，直接关闭弹窗
            if (selectedLanguage == currentLanguage) {
                dismiss()
                return@click
            }
            
            // 保存用户选择的语言
            AppLanguageManager.saveUserSelectedLanguage(requireContext(), selectedLanguage)
            
            // 应用语言设置
            AppLanguageManager.applyLanguage(requireContext(), selectedLanguage)
            
            // 通知回调
            onLanguageSelected(selectedLanguage)
            
            dismiss()
        }
        
        return view
    }
} 