package com.score.callmetest.ui.videocall

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.score.callmetest.Constant
import com.score.callmetest.manager.OnCallMessage
import com.score.callmetest.manager.OnHangUpMessage
import com.score.callmetest.util.EventBus

class CallIncomingDialogFragment : DialogFragment() {
    companion object {
        fun newInstance(callInfo: OnCallMessage): CallIncomingDialogFragment {
            val fragment = CallIncomingDialogFragment()
            val args = Bundle()
            args.putString(Constant.AVATAR_URL, callInfo.avatarThumbUrl ?: callInfo.avatar)
            args.putString(Constant.NICKNAME, callInfo.nickname)
            args.putString(Constant.AGE, callInfo.age?.toString())
            args.putString(Constant.COUNTRY, callInfo.country)
            args.putString(Constant.UNIT_PRICE, callInfo.unitPrice.toString())
            fragment.arguments = args
            return fragment
        }
    }
    interface OnActionListener {
        fun onAccept()
        fun onReject()
        fun onContentClick()
    }
    private var actionListener: OnActionListener? = null
    fun setOnActionListener(listener: OnActionListener) {
        this.actionListener = listener
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, android.R.style.Theme_Translucent_NoTitleBar)
    }
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        isCancelable = false
        val view = CallIncomingPopupView(requireContext())
        view.bind(
            avatarUrl = arguments?.getString(Constant.AVATAR_URL),
            nicknameStr = arguments?.getString(Constant.NICKNAME),
            ageStr = arguments?.getString(Constant.AGE),
            countryStr = arguments?.getString(Constant.COUNTRY),
            price = arguments?.getString(Constant.UNIT_PRICE).toString(),
            onAccept = { actionListener?.onAccept() },
            onReject = { actionListener?.onReject() },
            onContentClick = { actionListener?.onContentClick() }
        )
        return view
    }
    override fun onStart() {
        super.onStart()
        dialog?.window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        val window = dialog?.window
        window?.setGravity(android.view.Gravity.TOP)
        window?.clearFlags(android.view.WindowManager.LayoutParams.FLAG_DIM_BEHIND)
        window?.addFlags(android.view.WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL)
        // 隐藏状态栏，实现真正的全屏
        window?.decorView?.systemUiVisibility =
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
            View.SYSTEM_UI_FLAG_FULLSCREEN
        // 挂断事件监听，收到后自动关闭弹窗（用EventBus.observe方式）
        EventBus.observe(
            owner = viewLifecycleOwner,
            eventType = OnHangUpMessage::class.java
        ) {
            dismissAllowingStateLoss()
        }
    }

    
} 