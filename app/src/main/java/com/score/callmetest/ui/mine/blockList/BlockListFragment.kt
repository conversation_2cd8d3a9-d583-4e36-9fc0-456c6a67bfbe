package com.score.callmetest.ui.mine.blockList

import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentBlocklistBinding
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.ui.mine.follow.BottomState
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.ToastUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

class BlockListFragment : BaseFragment<FragmentBlocklistBinding, BlockListViewModel>() {

    private lateinit var adapter: BlockListAdapter
    private val mLayoutManager by lazy { LinearLayoutManager(context) }
    private var mNeedScrollToTop = false
    private var mEmptyViewInflated = false

    override fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragmentBlocklistBinding {
        return FragmentBlocklistBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = BlockListViewModel::class.java

    override fun initView() {
        super.initView()
        // 设置状态栏边距
        GlobalManager.addViewStatusBarTopMargin(requireActivity(), binding.tabFollow)
        CustomUtils.flipViewIfRTL(binding.btnReturn)

        setupTabLayout()
        setupRecyclerView()
        setupSwipeRefresh()
    }

    override fun initListener() {
        super.initListener()
        // 返回按钮点击事件
        ClickUtils.setOnIsolatedClickListener(binding.btnReturn) {
            activity?.onBackPressedDispatcher?.onBackPressed()
        }
    }

    private fun setupTabLayout() {
        binding.tabFollow.addTab(binding.tabFollow.newTab().apply {
            customView = createCustomTabView(CustomUtils.getString(R.string.blocklist), selected = true)
        })
        binding.tabFollow.tabRippleColor = null
    }

    private fun createCustomTabView(text: String, selected: Boolean): View {
        val view = layoutInflater.inflate(R.layout.tab_custom, null)
        val textView = view.findViewById<android.widget.TextView>(R.id.tab_text)
        val indicator = view.findViewById<View>(R.id.tab_indicator)
        textView.text = text
        textView.textSize = 18f
        textView?.setTypeface(Typeface.create("sans-serif", Typeface.BOLD))
        indicator.visibility = View.GONE
        return view
    }

    private fun setupRecyclerView() {
        adapter = BlockListAdapter { item ->
            item.broadcasterId?.let { broadcasterId ->
                viewModel.unblock(broadcasterId) { success,userId ->
                    if (success) {
                        ToastUtils.showShortToast(getString(com.score.callmetest.R.string.unblock_successfully))
                        loadBlockList(isRefresh = true)
                        // 更新缓存中用户信息，（实际数据其他以及更新）。
                        val userInfo = UserInfoManager.getCachedUserInfo(userId)
                        userInfo?.isBlock = false
                        UserInfoManager.putCachedUserInfo(userId, userInfo)

                    } else {
                        ToastUtils.showShortToast(getString(com.score.callmetest.R.string.unblock_failed))
                    }
                }
            }
        }

        binding.recyclerView.apply {
            adapter = <EMAIL>
            layoutManager = mLayoutManager

            // 添加滚动监听，实现加载更多
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    // 只有向下滚动时才检查
                    if (dy > 0) {
                        val visibleItemCount = mLayoutManager.childCount
                        val totalItemCount = mLayoutManager.itemCount
                        val firstVisibleItemPosition = mLayoutManager.findFirstVisibleItemPosition()

                        // 当滚动到倒数第3个item时开始加载更多
                        // 只有在底部状态为HIDDEN时才允许触发加载更多
                        if (!viewModel.isLoadingMore() &&
                            viewModel.hasMoreData() &&
                            viewModel.bottomState.value == BottomState.HIDDEN &&
                            (visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 3) {
                            loadMoreBlockList()
                        }
                    }
                }
            })
        }
    }



    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.apply {
            setColorSchemeResources(R.color.refresh_loading)
            setOnRefreshListener {
                isRefreshing = true
                mNeedScrollToTop = true
                // 重新加载 block 列表
                loadBlockList(isRefresh = true)
            }
        }
    }

    private fun loadBlockList(isRefresh: Boolean = true) {
        // 调用ViewModel加载屏蔽列表
        viewModel.fetchBlockList(isRefresh)
    }

    private fun loadMoreBlockList() {
        // 加载更多屏蔽数据
        viewModel.fetchBlockList(isRefresh = false)
    }

    override fun initData() {
        super.initData()
        setupObservers()
        // 不再预先显示空页面，而是显示下拉刷新状态
        // binding.emptyView.inflate() // 移除这行，改为在数据加载完成后根据结果决定是否显示

        // 显示下拉刷新状态并加载数据
        binding.swipeRefreshLayout.isRefreshing = true
        mNeedScrollToTop = true
        loadBlockList(isRefresh = true)
    }

    private fun setupObservers() {
        // 监听 blockList 数据
        viewModel.blockList.observe(viewLifecycleOwner) { list ->
            // 显示/隐藏空视图
            emptyView(list.isNullOrEmpty())

            // 提交新列表前保存当前滚动位置
            val firstVisiblePosition = mLayoutManager.findFirstVisibleItemPosition()

            // 更新适配器数据
            adapter.setData(list ?: emptyList())

            // 数据加载完成后，停止刷新动画
            binding.swipeRefreshLayout.isRefreshing = false

            // 只有在需要滚动到顶部时才滚动（下拉刷新时）
            if (mNeedScrollToTop) {
                binding.recyclerView.scrollToPosition(0)
                mNeedScrollToTop = false
            } else if (firstVisiblePosition == 0) {
                // 如果原本就在顶部，确保仍然在顶部
                binding.recyclerView.scrollToPosition(0)
            }

        }

        // 观察加载状态 - 只有在刷新时才显示顶部加载动画
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // 只有在不是加载更多的情况下才显示顶部加载动画
            if (!viewModel.isLoadingMore()) {
                binding.swipeRefreshLayout.isRefreshing = isLoading
                if (isLoading) {
                    mNeedScrollToTop = true
                }
            }
        }

        // 监听错误信息
        viewModel.error.observe(viewLifecycleOwner) { msg ->
            if (!msg.isNullOrEmpty()) {
                // 错误发生时，停止刷新动画
                binding.swipeRefreshLayout.isRefreshing = false
            }
            emptyView(viewModel.blockList.value.isNullOrEmpty())
        }

        // 观察底部状态变化
        viewModel.bottomState.observe(viewLifecycleOwner) { bottomState ->
            adapter.setBottomState(bottomState)
        }

        // 观察Toast消息
        viewModel.toastMessage.observe(viewLifecycleOwner) { messageResId ->
            if (messageResId != null && messageResId != 0) {
                ToastUtils.showShortToast(getString(messageResId))
            }
        }
    }

    /**
     * 显示/隐藏EmptyView
     */
    private fun emptyView(isEmpty: Boolean) {
        if (isEmpty) {
            // 只有在需要显示空页面时才inflate（延迟加载）
            if (!mEmptyViewInflated) {
                try {
                    binding.emptyView.inflate()
                    mEmptyViewInflated = true
                } catch (e: IllegalStateException) {
                    // ViewStub 已经被 inflate 过了，忽略异常
                    mEmptyViewInflated = true
                }
            }
            binding.emptyView.visibility = View.VISIBLE
            binding.recyclerView.visibility = View.GONE
        } else {
            // 有数据时隐藏空页面，显示列表
            if (mEmptyViewInflated && binding.emptyView.visibility == View.VISIBLE) {
                binding.emptyView.visibility = View.GONE
            }
            binding.recyclerView.visibility = View.VISIBLE
        }
    }

    /**
     * 取消拉黑后更新用户数据缓存
     * @param broadcasterId 被取消拉黑的主播ID
     */
    private fun updateUserInfoAfterUnblock(broadcasterId: String) {
        // 使用协程在后台线程更新用户数据
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 使用fetchUserInfo方法，只进行网络请求，不返回缓存数据
                val userInfo = UserInfoManager.fetchUserInfo(broadcasterId)
                // 用户信息已更新到缓存中
                Timber.d("用户数据已更新: userId=$broadcasterId, isBlock=${userInfo?.isBlock}")
            } catch (e: Exception) {
                Timber.e(e, "更新用户数据失败: userId=$broadcasterId")
            }
        }
    }

}