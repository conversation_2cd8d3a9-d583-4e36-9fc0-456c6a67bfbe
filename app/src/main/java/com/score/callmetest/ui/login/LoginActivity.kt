package com.score.callmetest.ui.login

import android.content.Intent
import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import android.widget.Toast
import com.score.callmetest.databinding.ActivityLoginBinding
import com.score.callmetest.ui.base.BaseActivity
import android.app.AlertDialog
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.style.UnderlineSpan
import com.score.callmetest.CallmeApplication
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.ui.main.MainActivity
import com.score.callmetest.R
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.ui.web.WebViewActivity
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.util.LoadingUtils
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.util.click
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.ToastUtils
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonPrimitive
import com.score.callmetest.ui.widget.BaseCustomDialog
import android.text.Html
import android.widget.TextView
import java.io.File
import java.io.FileOutputStream
import android.util.Log
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.common.MediaItem
import androidx.media3.ui.PlayerView
import android.net.Uri
import android.provider.Settings
import android.view.KeyEvent
import androidx.activity.OnBackPressedCallback
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import androidx.core.view.isVisible
import androidx.credentials.pendingGetCredentialRequest
import com.score.callmetest.BuildConfig
import com.score.callmetest.Constant
import com.score.callmetest.manager.GoogleSignInManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.ui.widget.DebugDeviceIdDialog
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.NetworkUtils
import timber.log.Timber

class LoginActivity : BaseActivity<ActivityLoginBinding, LoginViewModel>() {
    
    
    override fun getViewBinding(): ActivityLoginBinding {
        return ActivityLoginBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = LoginViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        if (AppPermissionManager.shouldShowFirstLaunchPermissionDialog(this)) {
            AppPermissionManager.showFirstLaunchPermissionDialog(
                this,
                onGranted = { /* 可选：权限授权后操作 */ },
                onDenied = { /* 可选：权限拒绝后操作 */ }
            )
        }
        super.onCreate(savedInstanceState)
    }

    override fun initView() {
        // Splash逻辑已移至SplashActivity

        binding.loginButton.doOnPreDraw {
            binding.loginButton.background = DrawableUtils.createGradientDrawable(
                colors = GlobalManager.getMainButtonBgGradientColors(),
                radius = binding.loginButton.height / 2f
            )
        }

        binding.loginButton.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));

        // 播放视频背景
        playLoginBgVideo()

        // 设置条款文本
        setupTermsText()
    }

    private var exoPlayer: ExoPlayer? = null

    private fun playLoginBgVideo() {
        val playerView = binding.gifBackground
        val player = ExoPlayer.Builder(this).build()
        exoPlayer = player
        playerView.player = player
        val mediaItem = MediaItem.fromUri("asset:///login_bg.mp4")
        player.setMediaItem(mediaItem)
        player.repeatMode = ExoPlayer.REPEAT_MODE_ALL
        player.volume = 0f
        player.playWhenReady = true
        player.prepare()
    }

    override fun onDestroy() {
        super.onDestroy()
        exoPlayer?.release()
        exoPlayer = null
    }

    private fun setupTermsText() {
        val fullText = getString(R.string.terms_privacy_policy)
        val spannableString = SpannableString(fullText)
        val userAgreementText = getString(R.string.user_agreement)
        val privacyPolicyText = getString(R.string.privacy_policy)

        // User Agreement 点击事件
        val termsClickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                // 跳转到用户协议页面
                ActivityUtils.startActivity(this@LoginActivity, WebViewActivity::class.java,
                    Bundle().apply {
                        putString("url", Constant.USER_AGREEMENT_URL)
                        putString("title", getString(R.string.user_agreement))
                    }
                    ,false)
            }
            override fun updateDrawState(ds: android.text.TextPaint) {
                super.updateDrawState(ds)
                ds.color = Color.WHITE
                ds.isUnderlineText = true
            }
        }

        // Privacy Policy 点击事件
        val privacyClickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                // 跳转到隐私政策页面
                ActivityUtils.startActivity(this@LoginActivity, WebViewActivity::class.java,
                    Bundle().apply {
                        putString("url", Constant.PRIVACY_POLICY_URL)
                        putString("title", getString(R.string.privacy_policy))
                    }
                    ,false)
            }
            override fun updateDrawState(ds: android.text.TextPaint) {
                super.updateDrawState(ds)
                ds.color = Color.WHITE
                ds.isUnderlineText = true
            }
        }

        // 设置点击区域
        val termsStart = fullText.indexOf(userAgreementText)
        val termsEnd = termsStart + userAgreementText.length
        val privacyStart = fullText.indexOf(privacyPolicyText)
        val privacyEnd = privacyStart + privacyPolicyText.length

        // 安全检查，只有在找到匹配文本时才设置点击事件
        val underlineSpan = UnderlineSpan()
        try {
            if (termsStart >= 0 && termsEnd <= fullText.length) {
                spannableString.setSpan(
                    termsClickableSpan,
                    termsStart,
                    termsEnd,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                spannableString.setSpan(
                    underlineSpan,
                    termsStart,
                    termsEnd,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            } else {
                Timber.w("setupTermsText - User Agreement text not found in fullText")
            }
            
            if (privacyStart >= 0 && privacyEnd <= fullText.length) {
                spannableString.setSpan(
                    privacyClickableSpan,
                    privacyStart,
                    privacyEnd,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                spannableString.setSpan(
                    underlineSpan,
                    privacyStart,
                    privacyEnd,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            } else {
                Timber.w("setupTermsText - Privacy Policy text not found in fullText")
            }
        } catch (e: Exception) {
            Timber.e(e, "setupTermsText - Error setting spans")
        }

        binding.termsText.text = spannableString
        binding.termsText.movementMethod = LinkMovementMethod.getInstance()
    }


    override fun initListener() {
        // 拦截系统返回（含手势返回与预测性返回）
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // 消费返回事件，不执行退出
                backToDesktop()
            }
        })
        // 登录按钮
        binding.loginButton.click {
            if (binding.termsCheckBox.isChecked) {
                doLogin(DeviceUtils.getAndroidId())
            } else {
                showTermsDialog {
                    doLogin(DeviceUtils.getAndroidId())
                }
            }
        }

        binding.btnMore.click {
            binding.btnMore.isVisible = false
            binding.googleLoginButton.isVisible = true
        }

        // Google登录按钮
        binding.googleLoginButton.click {
            if (binding.termsCheckBox.isChecked) {
                if(NetworkUtils.isConnected()) {
                // 显示loading遮罩
                LoadingUtils.showLoading(this)
                // Google登录过程
                // 不管是否已经有登录过，记录过凭证
                // 1. 静默登录
                // 2. 有凭证，自动登录
                // 3. 没有凭证，报错
                // 4. 普通登录
                // 5. 有多个账号，选择一个，自动登录

                    loginByGoogle(true)
                }else {
                    ToastUtils.showShortToast(getString(R.string.net_error_and_try_again))
                }

            } else {
                ToastUtils.showShortToast(getString(R.string.check_terms_and_conditions))
            }
        }

        //Todo： 双击后取随机android  id登陆，方便研发调试和后期测试
        if (BuildConfig.DEBUG) {
            binding.callmeTitle.apply {
                var lastClickTime = 0L
                var clickCount = 0
                setOnClickListener {
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastClickTime < 500) { // 双击间隔500ms
                        clickCount++
                        if (clickCount >= 2) {
                            showDebugDeviceIdDialog()
                            clickCount = 0
                        }
                    } else {
                        clickCount = 1
                    }
                    lastClickTime = currentTime
                }
            }
        }
    }

    /**
     * 通过谷歌登录
     * @param [isSilent] 是否静默登录
     *
     *  ps. 当
     *
     */
    private fun loginByGoogle(isSilent: Boolean = false) {
        GoogleSignInManager.signIn(
            this@LoginActivity, isSilent,
            onSuccess = {
                LoadingUtils.dismissLoading()
            }, onError = {
                if (it == GoogleSignInManager.TYPE_NO_CREDENTIAL && isSilent) {
                    // 没有凭证，尝试普通登录
                    loginByGoogle(false)
                    return@signIn
                }
                LoadingUtils.dismissLoading()
                if(it == GoogleSignInManager.TYPE_NO_CREDENTIAL){
                    // 没有凭证，且不是静默登录，引导用户登录
                    BaseCustomDialog(
                        context = this,
                        emojiResId = R.drawable.emoji_cry,
                        title = getString(R.string.google_account_required),
                        content = getString(R.string.google_account_required_hint),
                        agreeText = getString(R.string.remind_me_later),
                        cancelText = getString(R.string.go_to_settings),
                        onAgree = {},
                        onCancel = {
                            // 直接打开账号添加界面
                            val intent = Intent(Settings.ACTION_ADD_ACCOUNT)
                                .putExtra(Settings.EXTRA_ACCOUNT_TYPES, arrayOf("com.google"))
                            startActivity(intent)
                        }
                    ).show()

                }else {
                    // 其他错误
                    ToastUtils.showShortToast(getString(R.string.login_error))
                }
            }, onCancel = {
                LoadingUtils.dismissLoading()
            })
    }

    //todo 双击logo显示调试弹窗
    /**
     * 显示调试设备ID设置弹窗
     */
    private fun showDebugDeviceIdDialog() {
        val dialog = DebugDeviceIdDialog(
            context = this,
            onConfirm = {
                // 弹窗确认后的回调，可以在这里做一些额外的处理
                // 比如刷新UI显示当前使用的设备ID等
            }
        )
        dialog.show()
    }

    private fun showTermsDialog(onAgree: () -> Unit) {
        val fullText = getString(R.string.hope_great_experience)
        val spannableString = SpannableString(fullText)
        val userAgreementText = getString(R.string.user_agreement)
        val privacyPolicyText = getString(R.string.privacy_policy)

        val termsStart = fullText.indexOf(userAgreementText)
        val termsEnd = termsStart + userAgreementText.length
        val privacyStart = fullText.indexOf(privacyPolicyText)
        val privacyEnd = privacyStart + privacyPolicyText.length

        val termsClickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                ActivityUtils.startActivity(
                    this@LoginActivity, WebViewActivity::class.java,
                    Bundle().apply {
                        putString("url", Constant.USER_AGREEMENT_URL)
                        putString("title", getString(R.string.user_agreement))
                    }, false)

            }

            override fun updateDrawState(ds: android.text.TextPaint) {
                super.updateDrawState(ds)
                ds.color = 0xFF808080.toInt() // #808080
                ds.isUnderlineText = true
            }
        }
        val privacyClickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {

                ActivityUtils.startActivity(
                    this@LoginActivity, WebViewActivity::class.java,
                    Bundle().apply {
                        putString("url", Constant.PRIVACY_POLICY_URL)
                        putString("title", getString(R.string.privacy_policy))
                    }, false)
            }
            override fun updateDrawState(ds: android.text.TextPaint) {
                super.updateDrawState(ds)
                ds.color = 0xFF808080.toInt() // #808080
                ds.isUnderlineText = true
            }
        }
        val underlineSpan = UnderlineSpan()
        
        // 安全检查，只有在找到匹配文本时才设置点击事件
        try {
            if (termsStart >= 0 && termsEnd <= fullText.length) {
                spannableString.setSpan(
                    termsClickableSpan,
                    termsStart,
                    termsEnd,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                spannableString.setSpan(
                    underlineSpan,
                    termsStart,
                    termsEnd,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            } else {
                Timber.w("showTermsDialog - User Agreement text not found in fullText")
            }
            
            if (privacyStart >= 0 && privacyEnd <= fullText.length) {
                spannableString.setSpan(
                    privacyClickableSpan,
                    privacyStart,
                    privacyEnd,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                spannableString.setSpan(
                    underlineSpan,
                    privacyStart,
                    privacyEnd,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            } else {
                Timber.w("showTermsDialog - Privacy Policy text not found in fullText")
            }
        } catch (e: Exception) {
            Timber.e(e, "showTermsDialog - Error setting spans")
        }

        val dialog = BaseCustomDialog(
            context = this,
            emojiResId = R.drawable.emoji_laugh,
            title = getString(R.string.thank_you_review_terms),
            content = spannableString,
            // 这里为了适配，所以同意和拒绝按钮的点击事件反着写
            agreeText = getString(R.string.cancel),
            cancelText = getString(R.string.agree),
            onAgree = {

            },
            onCancel = {
                binding.termsCheckBox.isChecked = true
                onAgree()
            }
        )

        dialog.show()

        // 支持富文本点击
        dialog.findViewById<TextView>(R.id.content)?.movementMethod = android.text.method.LinkMovementMethod.getInstance()
    }

    private fun doLogin(deviceId: String) {
        // 显示loading遮罩
        LoadingUtils.showLoading(this)

        viewModel.login(
            token = deviceId,
            onSuccess = { data ->
                if (data.token != null) {
                    if (data.userInfo != null) {
                        UserInfoManager.updateMyUserInfo(data.userInfo)
                    }
                    viewModel.saveLoginData(data.token, false, "guest")

                    viewModel.getStrategy(
                        onSuccess = {
                            LoadingUtils.dismissLoading()
                            // 检查是否需要显示首次启动权限弹窗
                            if (AppPermissionManager.shouldShowFirstLaunchPermissionDialog(this)) {
                                AppPermissionManager.showFirstLaunchPermissionDialog(
                                    this,
                                    onGranted = {
                                        // 权限授权成功，跳转到首页
                                        startActivity(Intent(this, MainActivity::class.java))
                                        finish()
                                    },
                                    onDenied = {
                                        // 权限被拒绝，仍然跳转到首页
                                        startActivity(Intent(this, MainActivity::class.java))
                                        finish()
                                    }
                                )
                            } else {
                                // 跳转到首页
                                startActivity(Intent(this, MainActivity::class.java))
                                finish()
                            }
                        },
                        onError = {
                            // 隐藏loading遮罩
                            LoadingUtils.dismissLoading()
                            ToastUtils.showShortToast( getString(R.string.login_error))
                        }
                    )
                } else {
                    // 隐藏loading遮罩
                    LoadingUtils.dismissLoading()
//                    ToastUtils.showToast("Login failed", Toast.LENGTH_SHORT)
                    ToastUtils.showShortToast( getString(R.string.login_error))
                }
            },
            onError = { msg ->
                // 隐藏loading遮罩
                LoadingUtils.dismissLoading()
                Timber.e(msg)
                // todo 登录失败
                ToastUtils.showShortToast( getString(R.string.login_error))
            }
        )
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        AppPermissionManager.handleRequestPermissionsResult(this, requestCode, permissions, grantResults)
    }


//    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
//        if (keyCode == KeyEvent.KEYCODE_BACK) {
//            val fm = supportFragmentManager
//            // 如果有Fragment在back stack，先让FragmentManager处理
//            if (fm.backStackEntryCount > 0) {
//                fm.popBackStack()
//            } else {
//                backToDesktop()
//            }
//            backToDesktop()
//            return true
//        }
//        return super.onKeyDown(keyCode, event)
//    }

    fun backToDesktop() {
        // 直接返回桌面，不退出应用
        val homeIntent = Intent(Intent.ACTION_MAIN)
        homeIntent.addCategory(Intent.CATEGORY_HOME)
        homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        startActivity(homeIntent)
    }
} 