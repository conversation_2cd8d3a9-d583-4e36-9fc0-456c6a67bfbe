package com.score.callmetest.ui.mine

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.util.AppLanguageManager
import com.score.callmetest.util.click

/**
 * 语言选择适配器
 */
class LanguageSelectAdapter(
    private val onLanguageSelected: (String) -> Unit
) : RecyclerView.Adapter<LanguageSelectAdapter.LanguageViewHolder>() {

    private val languageCodes = AppLanguageManager.getSupportedLanguageCodes()
    private var selectedLanguageCode: String = "en"

    fun updateSelectedLanguage(languageCode: String) {
        val oldIndex = languageCodes.indexOf(selectedLanguageCode)
        val newIndex = languageCodes.indexOf(languageCode)
        
        selectedLanguageCode = languageCode
        
        if (oldIndex != -1) {
            notifyItemChanged(oldIndex)
        }
        if (newIndex != -1) {
            notifyItemChanged(newIndex)
        }
    }

    fun getSelectedLanguage(): String = selectedLanguageCode

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LanguageViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_language_select, parent, false)
        return LanguageViewHolder(view)
    }

    override fun onBindViewHolder(holder: LanguageViewHolder, position: Int) {
        val languageCode = languageCodes[position]
        val languageName = AppLanguageManager.getLanguageDisplayName(languageCode)
        val isSelected = languageCode == selectedLanguageCode
        
        holder.bind(languageName, isSelected) {
            onLanguageSelected(languageCode)
        }
    }

    override fun getItemCount(): Int = languageCodes.size

    class LanguageViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val languageNameView: TextView = itemView.findViewById(R.id.tv_language_name)

        fun bind(languageName: String, isSelected: Boolean, onClick: () -> Unit) {
            languageNameView.text = languageName
            itemView.isSelected = isSelected
            
            // 设置背景（无分割线）
            itemView.background = ContextCompat.getDrawable(itemView.context, R.drawable.language_item_background)
            
            itemView.click { onClick() }
        }
    }
}
