package com.score.callmetest.ui.widget.checkin

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemCheckInDayBinding
import com.score.callmetest.manager.CheckInManager
import com.score.callmetest.network.CheckinRewardResp
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.GlideUtils
import timber.log.Timber

/**
 * 签到日期的RecyclerView适配器 - DataBinding版本
 */
class CheckInDayAdapter : RecyclerView.Adapter<CheckInDayAdapter.CheckInDayViewHolder>() {

    private var rewards: List<CheckinRewardResp> = emptyList()
    
    init {
        // 初始化时获取奖励数据
        updateRewards()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CheckInDayViewHolder {
        val binding = ItemCheckInDayBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CheckInDayViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CheckInDayViewHolder, position: Int) {
        if (position >= rewards.size) return
        
        val reward = rewards[position]
        val context = holder.binding.root.context

        // 设置基本信息
        holder.binding.tvDay.text = context.getString(R.string.str_day_num, reward.day)

        // 图标、数量
        GlideUtils.load(context, reward.iconUrl, holder.binding.ivReward)
        holder.binding.tvNum.text = "+${reward.amount}"

        if(position == rewards.size -1){
            // 最后一个--变大
            val layoutParams = holder.binding.ivReward.layoutParams
            layoutParams.width = DisplayUtils.dp2px(50f)
            holder.binding.ivReward.layoutParams = layoutParams
        }

        // 设置签到状态
        val rewardStatus = reward.status
        if (rewardStatus == "received") {
            // 已签到状态-received
            holder.binding.checkedLayout.visibility = View.VISIBLE
            holder.binding.dayLayout.alpha = 0.5f
        } else if(rewardStatus == "available"){
            // 未签到状态-available
            holder.binding.checkedLayout.visibility = View.GONE
            holder.binding.dayLayout.setBackgroundResource(R.drawable.bg_check_in_selected)
            holder.binding.tvDay.setTextColor(ContextCompat.getColor(context, R.color.black))
        }else {
            // unavailable
            holder.binding.checkedLayout.visibility = View.GONE
        }
    }

    override fun getItemCount(): Int = rewards.size

    /**
     * 更新奖励数据
     */
    private fun updateRewards() {
        val cachedRewards = CheckInManager.getCheckInRewards()
        if (cachedRewards != null) {
            this.rewards = cachedRewards
        } else {
            // 如果没有缓存数据，使用空列表
            this.rewards = emptyList()
            Timber.w("CheckInDayAdapter: 没有缓存的奖励数据")
        }
    }
    
    /**
     * 更新签到状态
     */
    fun updateCheckInStatus() {
        // 同时更新奖励数据
        updateRewards()
        notifyDataSetChanged()
    }

    class CheckInDayViewHolder(val binding: ItemCheckInDayBinding) : RecyclerView.ViewHolder(binding.root)
}
