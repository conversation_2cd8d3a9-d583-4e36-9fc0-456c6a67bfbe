package com.score.callmetest.ui.widget

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import com.score.callmetest.databinding.DialogRechargeTimeoutBinding
import com.score.callmetest.util.DialogUtils
import com.score.callmetest.util.click

class RechargeTimeoutDialog(
    context: Context,
    private val orderNo: String,
    private val onAction: (Boolean) -> Unit
) : Dialog(context) {

    private lateinit var binding: DialogRechargeTimeoutBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DialogRechargeTimeoutBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)

        setupButtons()
    }

    private fun setupButtons() {
        binding.btnCustomerService.click {
            onAction(true) // 咨询客服
            DialogUtils.safeDismiss(this)
        }

        binding.btnOk.click {
            onAction(false) // 确认
            DialogUtils.safeDismiss(this)
        }
    }
} 