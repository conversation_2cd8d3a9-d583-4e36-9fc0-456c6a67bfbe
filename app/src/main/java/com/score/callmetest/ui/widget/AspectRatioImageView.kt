package com.score.callmetest.ui.widget

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.util.Log
import android.view.WindowManager
import com.score.callmetest.util.DeviceUtils
import kotlin.math.min

/**
 * 自适应宽高比的ImageView
 * 最大宽度为屏幕宽度的0.6，最大高度为屏幕高度的0.38
 * 根据图片实际宽高比进行等比缩放
 */
class AspectRatioImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AlphaImageView(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "dsc--AspectRatioImageView"
        private const val MAX_WIDTH_RATIO = 0.6f  // 最大宽度为屏幕宽度的60%
        private const val MAX_HEIGHT_RATIO = 0.38f // 最大高度为屏幕高度的38%
        private const val MIN_WIDTH_DP = 120  // 最小宽度120dp
        private const val MIN_HEIGHT_DP = 80  // 最小高度80dp
    }

    private val screenWidth: Int
    private val screenHeight: Int
    private val density: Float

    init {
        screenWidth = DeviceUtils.getScreenWidth()
        screenHeight = DeviceUtils.getScreenHeight()
        density = DeviceUtils.getScreenDensity()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        try {
            val drawable = drawable
            if (drawable == null) {
                // 如果没有图片，使用默认尺寸
                val defaultWidth = (MIN_WIDTH_DP * density).toInt()
                val defaultHeight = (MIN_HEIGHT_DP * density).toInt()
                setMeasuredDimension(defaultWidth, defaultHeight)
                return
            }

            // 获取图片的原始宽高
            val imageWidth = drawable.intrinsicWidth
            val imageHeight = drawable.intrinsicHeight


            // 如果无法获取图片尺寸或尺寸无效，使用默认尺寸
            if (imageWidth <= 0 || imageHeight <= 0) {
                val defaultWidth = (MIN_WIDTH_DP * density).toInt()
                val defaultHeight = (MIN_HEIGHT_DP * density).toInt()
                setMeasuredDimension(defaultWidth, defaultHeight)
                return
            }

            // 计算最大允许的宽高
            val maxWidth = (screenWidth * MAX_WIDTH_RATIO).toInt()
            val maxHeight = (screenHeight * MAX_HEIGHT_RATIO).toInt()

            // 计算最小宽高
            val minWidth = (MIN_WIDTH_DP * density).toInt()
            val minHeight = (MIN_HEIGHT_DP * density).toInt()

            // 计算图片的宽高比
            val imageRatio = imageWidth.toFloat() / imageHeight.toFloat()

            var finalWidth: Int
            var finalHeight: Int

            // 根据宽高比计算最终尺寸
            if (imageRatio > 1) {
                // 横图：以宽度为准
                finalWidth = min(imageWidth, maxWidth)
                finalHeight = (finalWidth / imageRatio).toInt()

                // 如果计算出的高度超过最大高度，以高度为准重新计算
                if (finalHeight > maxHeight) {
                    finalHeight = maxHeight
                    finalWidth = (finalHeight * imageRatio).toInt()
                }
            } else {
                // 竖图或正方形：以高度为准
                finalHeight = min(imageHeight, maxHeight)
                finalWidth = (finalHeight * imageRatio).toInt()

                // 如果计算出的宽度超过最大宽度，以宽度为准重新计算
                if (finalWidth > maxWidth) {
                    finalWidth = maxWidth
                    finalHeight = (finalWidth / imageRatio).toInt()
                }
            }

            // 确保不小于最小尺寸
            finalWidth = maxOf(finalWidth, minWidth)
            finalHeight = maxOf(finalHeight, minHeight)


            setMeasuredDimension(finalWidth, finalHeight)
        } catch (e: Exception) {
            // 发生异常时使用默认尺寸
            val defaultWidth = (MIN_WIDTH_DP * density).toInt()
            val defaultHeight = (MIN_HEIGHT_DP * density).toInt()
            setMeasuredDimension(defaultWidth, defaultHeight)
        }
    }

    override fun setImageDrawable(drawable: Drawable?) {
        super.setImageDrawable(drawable)
        // 当设置新图片时，重新测量
        requestLayout()
    }

    override fun setImageBitmap(bm: Bitmap?) {
        super.setImageBitmap(bm)
        // 当设置新图片时，重新测量
        requestLayout()
    }
}
