package com.score.callmetest.ui.home.ranklist

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.score.callmetest.network.BroadcasterRankResponse
import com.score.callmetest.network.BroadcasterRankSearchRequest
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.UserCoupleRankResponse
import com.score.callmetest.network.UserCoupleRankSearchRequest
import com.score.callmetest.network.UserRankResponse
import com.score.callmetest.network.UserRankSearchRequest
import com.score.callmetest.ui.base.BaseViewModel
import kotlinx.coroutines.launch

/**
 * 排行榜ViewModel
 * 负责处理排行榜数据获取和状态管理
 */
class RankListViewModel : BaseViewModel() {

    // 用户排行榜数据
    private val _userRankData = MutableLiveData<UserRankResponse>()
    val userRankData: LiveData<UserRankResponse> = _userRankData

    // cp排行榜数据
    private val _userCoupleRankData = MutableLiveData<UserCoupleRankResponse>()
    val userCoupleRankData: LiveData<UserCoupleRankResponse> = _userCoupleRankData

    // 主播排行榜数据
    private val _broadcasterRankData = MutableLiveData<BroadcasterRankResponse>()
    val broadcasterRankData: LiveData<BroadcasterRankResponse> = _broadcasterRankData

    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // 错误信息 type 0 用户榜；；type 1 主播排行榜；type 2 cp排行榜
    private val _errorMessage = MutableLiveData<Pair<String?, Int>>()
    val errorMessage: LiveData<Pair<String?, Int>> = _errorMessage

    /**
     * 获取用户排行榜数据
     * @param count 排行榜数量，默认50
     */
    fun loadUserRankData(count: Int = 50) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = Pair(null, 0)
                
                val request = UserRankSearchRequest(count = count)
                val result = RetrofitUtils.dataRepository.searchUserRank(request)
                
                when (result) {
                    is NetworkResult.Success -> {
                        result.data?.let { data ->
                            _userRankData.value = data
                        } ?: run {
                            val error = "排行榜数据为空"
                            _errorMessage.value = Pair(error, 0)
                        }
                    }
                    is NetworkResult.Error -> {
                        _errorMessage.value = Pair(result.message,0)
                    }

                }
            } catch (e: Exception) {
                _errorMessage.value = Pair(e.message,0)
            } finally {
                _isLoading.value = false
            }
        }
    }


    /**
     * 获取主播排行榜数据
     * @param count 排行榜数量，默认50
     */
    fun loadBroadcasterRankData(count: Int = 50) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = Pair(null, 1)
                
                val request = BroadcasterRankSearchRequest(count = count)
                val result = RetrofitUtils.dataRepository.searchBroadcasterRank(request)
                
                when (result) {
                    is NetworkResult.Success -> {
                        result.data?.let { data ->
                            _broadcasterRankData.value = data
                        } ?: run {
                            val error = "主播排行榜数据为空"
                            _errorMessage.value = Pair(error, 1)

                        }
                    }
                    is NetworkResult.Error -> {
                        _errorMessage.value = Pair(result.message, 1)

                    }
                }
            } catch (e: Exception) {
                _errorMessage.value = Pair(e.message, 1)
            } finally {
                _isLoading.value = false
            }
        }
    }


    /**
     * 获取情侣排行榜数据
     * @param count 排行榜数量，默认50
     */
    fun loadUserCoupleRankData(count: Int = 50) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = Pair(null, 2)

                val request = UserCoupleRankSearchRequest(count = count)
                val result = RetrofitUtils.dataRepository.searchUserCoupleRank(request)

                when (result) {
                    is NetworkResult.Success -> {
                        result.data?.let { data ->
                            _userCoupleRankData.value = data
                        } ?: run {
                            val error = "情侣排行榜数据为空"
                            _errorMessage.value = Pair(error, 2)
                        }
                    }
                    is NetworkResult.Error -> {
                        _errorMessage.value = Pair(result.message, 2)
                    }

                }
            } catch (e: Exception) {
                _errorMessage.value = Pair(e.message, 2)
            } finally {
                _isLoading.value = false
            }
        }
    }
    /**
     * 刷新排行榜数据
     */
    fun refreshRankData() {
        loadUserRankData()
    }

    /**
     * 刷新情侣排行榜数据
     */
    fun refreshCoupleRankData() {
        loadUserCoupleRankData()
    }

    /**
     * 刷新主播排行榜数据
     */
    fun refreshBroadcasterRankData() {
        loadBroadcasterRankData()
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = Pair(null, 0)
    }
}