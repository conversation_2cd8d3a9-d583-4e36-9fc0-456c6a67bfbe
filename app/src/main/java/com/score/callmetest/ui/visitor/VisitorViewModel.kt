package com.score.callmetest.ui.visitor

import androidx.lifecycle.viewModelScope
import com.score.callmetest.R
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.VisitorManager
import com.score.callmetest.network.UserInfo
import com.score.callmetest.network.VisitorRecord
import com.score.callmetest.ui.base.BaseViewModel
import com.score.callmetest.ui.mine.follow.BottomState
import timber.log.Timber

class VisitorViewModel : BaseViewModel() {
    
    private val TAG = "VisitorViewModel"

    // 分页状态管理
    private var visitorCurrentPage = 1
    private var isVisitorLoadingMore = false
    private var hasMoreVisitor = true
    private val pageSize = 15

    // 当前列表数据
    private val currentVisitorList = mutableListOf<VisitorRecord>()

    /**
     * 加载来访者列表
     * @param isRefresh 是否是刷新操作，true表示刷新，false表示加载更多
     * @param onVisitorListUpdated 来访者列表更新回调
     * @param onLoadingStateChanged 加载状态变化回调
     * @param onError 错误回调
     * @param onBottomStateChanged 底部状态变化回调
     * @param onToastMessage Toast消息回调
     */
    fun loadVisitorList(
        isRefresh: Boolean = true,
        onVisitorListUpdated: (List<VisitorRecord>) -> Unit = {},
        onLoadingStateChanged: (Boolean) -> Unit = {},
        onError: (String) -> Unit = {},
        onBottomStateChanged: (BottomState) -> Unit = {},
        onToastMessage: (Int) -> Unit = {}
    ) {
        Timber.tag(TAG).d("加载来访者列表: isRefresh=$isRefresh")
        
        if (isRefresh) {
            // 刷新操作，重置分页状态和底部状态
            visitorCurrentPage = 1
            hasMoreVisitor = true
            currentVisitorList.clear()
            onBottomStateChanged(BottomState.HIDDEN)
        } else {
            // 加载更多操作，检查是否正在加载或没有更多数据
            if (isVisitorLoadingMore || !hasMoreVisitor) {
                Timber.tag(TAG).d("跳过加载更多: isVisitorLoadingMore=$isVisitorLoadingMore, hasMoreVisitor=$hasMoreVisitor")
                return
            }
            visitorCurrentPage++
            // 开始加载更多时显示加载动画
            onBottomStateChanged(BottomState.LOADING)
        }

        isVisitorLoadingMore = true
        VisitorManager.loadVisitorList(pageSize, visitorCurrentPage, object : VisitorManager.VisitorPageCallback {
            override fun onSuccess(list: List<VisitorRecord>) {
                isVisitorLoadingMore = false

                if (isRefresh) {
                    currentVisitorList.clear()
                }
                currentVisitorList.addAll(list)

                // 判断是否还有更多数据
                hasMoreVisitor = list.size >= pageSize

                onVisitorListUpdated(currentVisitorList.toList())

                // 根据数据情况设置底部状态
                updateBottomState(isRefresh, list.size, currentVisitorList, onBottomStateChanged, onToastMessage)
                
                Timber.tag(TAG).d("来访者列表加载成功: 当前页=${visitorCurrentPage}, 获取${list.size}条, 总计${currentVisitorList.size}条")
            }

            override fun onError(errorMsg: String) {
                isVisitorLoadingMore = false
                if (!isRefresh) {
                    // 加载更多失败时，回退页码
                    visitorCurrentPage--

                    // 显示Toast并设置为完成状态
                    onToastMessage(R.string.there_is_no_more_data)
                    onBottomStateChanged(BottomState.FINISHED)
                } else {
                    onBottomStateChanged(BottomState.HIDDEN)
                }
                onError(errorMsg)
                
                Timber.tag(TAG).e("来访者列表加载失败: $errorMsg")
            }

            override fun onLoading(isLoading: Boolean) {
                onLoadingStateChanged(isLoading)
            }
        })
    }

    /**
     * 通用的底部状态更新方法
     */
    private fun updateBottomState(
        isRefresh: Boolean,
        dataSize: Int,
        currentList: List<VisitorRecord>,
        onBottomStateChanged: (BottomState) -> Unit,
        onToastMessage: (Int) -> Unit
    ) {
        if (isRefresh) {
            // 第一次请求
            when {
                currentList.isEmpty() -> {
                    // 数据为空，隐藏底部
                    onBottomStateChanged(BottomState.HIDDEN)
                }
                dataSize < pageSize -> {
                    // 数据少于15条，显示静态底部
                    onBottomStateChanged(BottomState.FINISHED)
                }
                dataSize == pageSize -> {
                    // 数据等于15条，隐藏底部等待用户滚动
                    onBottomStateChanged(BottomState.HIDDEN)
                }
            }
        } else {
            // 后续请求
            when {
                dataSize == 0 -> {
                    // 没有更多数据，显示Toast和静态底部
                    onToastMessage(R.string.there_is_no_more_data)
                    onBottomStateChanged(BottomState.FINISHED)
                }
                dataSize < pageSize -> {
                    // 数据少于15条，显示静态底部
                    onBottomStateChanged(BottomState.FINISHED)
                }
                dataSize == pageSize -> {
                    // 数据等于15条，隐藏底部继续等待
                    onBottomStateChanged(BottomState.HIDDEN)
                }
            }
        }
    }

    /**
     * 点击item查询userinfo后跳转到聊天
     */
    fun gotoChat(userId: String, onGoChatUser: (UserInfo) -> Unit = {}) {
        Timber.tag(TAG).d("跳转到聊天: userId=$userId")
        // 先查缓存，再网络查询
        UserInfoManager.getUserInfo(userId, scope = viewModelScope) { getUserInfo ->
            getUserInfo?.let { nonNullUser ->
                onGoChatUser(nonNullUser)
            }
        }
    }

    /**
     * 检查来访者列表是否还有更多数据
     */
    fun hasMoreVisitorData(): Boolean = hasMoreVisitor

    /**
     * 检查来访者列表是否正在加载更多
     */
    fun isVisitorLoadingMore(): Boolean = isVisitorLoadingMore
}