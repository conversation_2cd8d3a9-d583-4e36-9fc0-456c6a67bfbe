package com.score.callmetest.ui.message

import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentMessageBinding
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.manager.CheckInManager
import com.score.callmetest.manager.FlashChatManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.RechargeManager.adjustPollingStrategyOnPaymentReturn
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.main.MainActivity
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.EventBus
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 消息主页面，包含Messages和Call两个Tab
 */
class MessageFragment : BaseFragment<FragmentMessageBinding, MessageViewModel>() {

    private lateinit var mPagerAdapter: MessagePagerAdapter

    // 标记是否是首次onResume，用于控制delay逻辑
    private var isFirstResume = true

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentMessageBinding {
        return FragmentMessageBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = MessageViewModel::class.java

    override fun initView() {
        GlobalManager.addViewStatusBarTopMargin(requireActivity(), binding.tabMessage)

        setupTabLayout()
        setupViewPager()
        setupCheckIn()

    }

    override fun initData() {
        super.initData()
        setupObservers()
    }

    private fun setupObservers() {
        // 监听状态变化
        EventBus.observe(
            scope = lifecycleScope,
            eventType = CustomEvents.NewStatusEvent::class.java
        ) { event ->
            // 遍历子fragment更新在线状态
            childFragmentManager.fragments.forEach { fragment ->
                if (fragment is IMessageStatus) {
                    fragment.notifyStatusChanged(event.statusMap)
                }
            }
        }

        // 监听消息列表滚动停止事件
        EventBus.observe(
            scope = lifecycleScope,
            eventType = CustomEvents.MessageListScrollEvent::class.java
        ) { event ->
            handleScrollStopEvent(event)
        }
    }

    /**
     * 签到dialog
     */
    private fun setupCheckIn() {
        if (StrategyManager.isReviewPkg()) {
            binding.btnCheckIn.visibility = View.GONE
            return
        }

        // 签到按钮点击事件
        ClickUtils.setOnIsolatedClickListener(binding.btnCheckIn) {
            CheckInManager.showCheckInDialog(requireActivity() as AppCompatActivity)
        }

        // 监听签到是否可用
        EventBus.observe(
            scope = lifecycleScope,
            eventType = CustomEvents.CheckInAvailable::class.java
        ) { event ->
            try {
                if (event.isAvailable) {
                    binding.btnCheckIn.loops = 0
                    binding.btnCheckIn.startAnimation()
                } else {
                    binding.btnCheckIn.stopAnimation()
                }
            } catch (e: Exception) {
                Timber.e(e)
            }
        }

        if (CheckInManager.isCheckedInToday()) {
            // 当天签到过，动画一次
            CustomUtils.playSvgaOnce(binding.btnCheckIn, "icon_check_in_enter.svga")
        } else {
            CustomUtils.playSvga(binding.btnCheckIn, "icon_check_in_enter.svga", 0)
        }
    }

    /**
     * 处理滚动停止事件
     * 判断当前Fragment是否可见，可见则触发状态更新
     */
    private fun handleScrollStopEvent(event: CustomEvents.MessageListScrollEvent) {
        try {
            // 检查当前Fragment是否可见
            if (!isVisible || isHidden || !isResumed) {
                Timber.d("Fragment不可见，忽略滚动停止事件: ${event.fragmentTag}")
                return
            }

            // 检查事件来源是否是当前显示的Fragment
            val currentFragment = childFragmentManager.fragments.getOrNull(binding.viewPager.currentItem)
            val expectedTag = when (currentFragment) {
                is MsgListFragment -> MsgListFragment::class.java.simpleName
                is MessageFollowingFragment -> MessageFollowingFragment::class.java.simpleName
                is CallHistoryFragment -> CallHistoryFragment::class.java.simpleName
                else -> null
            }

            if (expectedTag == event.fragmentTag) {
                Timber.d("收到滚动停止事件，触发状态更新: ${event.fragmentTag}")
                triggerStatusUpdate()
            } else {
                Timber.d("事件来源不匹配当前Fragment，忽略: expected=$expectedTag, actual=${event.fragmentTag}")
            }
        } catch (e: Exception) {
            Timber.e(e, "处理滚动停止事件失败")
        }
    }

    private fun setupTabLayout() {
        with(binding.tabMessage) {
            tabRippleColor = null
            // 添加Messages标签
            addTab(newTab().apply {
                customView = createCustomTabView(getString(R.string.messages), selected = true)
            })

            // 添加Following标签
            addTab(newTab().apply {
                customView = createCustomTabView(getString(R.string.followed), selected = false)
            })

            // 添加Call标签
            addTab(newTab().apply {
                customView = createCustomTabView(getString(R.string.calls), selected = false)
            })


        }
    }

    private fun setupViewPager() {
        // 初始化ViewPager适配器
        mPagerAdapter = MessagePagerAdapter(this)

        // 配置ViewPager
        binding.viewPager.apply {
            adapter = mPagerAdapter
            offscreenPageLimit = 3 // 预加载页面数量，现在有3个Tab
        }
        GlobalManager.setNeverOverScroll(binding.viewPager)
    }


    override fun initListener() {
        super.initListener()
        // click-clean-一键已读
        ClickUtils.setOnIsolatedClickListener(binding.btnClear) {
            if (binding.viewPager.currentItem == 0) {
                (childFragmentManager.fragments[0] as? MsgListFragment)?.clearMsg()
            }
        }

        // 监听Tab切换
        binding.tabMessage.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                updateTabStyle(tab.customView, true)
                binding.viewPager.currentItem = tab.position
                binding.btnClear.visibility =
                    if (tab.position == 0) View.VISIBLE else View.GONE

                // Tab切换时触发状态更新
                triggerStatusUpdate()
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                updateTabStyle(tab.customView, false)
            }

            override fun onTabReselected(tab: TabLayout.Tab) {
                // 不需要处理
            }
        })

        // 监听ViewPager页面切换
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                binding.tabMessage.getTabAt(position)?.select()
            }
        })
    }

    /**
     * 创建自定义Tab视图
     * @param text Tab文本
     * @param customView 现有的自定义视图（如果有）
     * @param selected 是否选中
     * @return 自定义Tab视图
     */
    private fun createCustomTabView(
        text: String? = null,
        customView: View? = null,
        selected: Boolean = false
    ): View {
        val view = customView ?: layoutInflater.inflate(R.layout.tab_custom, null)
        updateTabStyle(view, selected, text)
        return view
    }

    /**
     * 更新Tab样式
     * @param view Tab视图
     * @param selected 是否选中
     * @param text Tab文本（可选）
     */
    private fun updateTabStyle(view: View?, selected: Boolean, text: String? = null) {
        if (view == null) return

        val textView = view.findViewById<TextView>(R.id.tab_text)
        val indicator = view.findViewById<View>(R.id.tab_indicator)

        textView.setTypeface(
            if (selected)  ResourcesCompat.getFont(requireContext(),  R.font.roboto_bold)
            else Typeface.create("sans-serif", Typeface.NORMAL)
        )

        // 设置文本（如果提供）
        if (!text.isNullOrEmpty()) {
            textView.text = text
        }

        // 设置样式
        textView.textSize = if (selected) 22f else 15f
        textView.paint.isFakeBoldText = selected

        // 设置指示器
        indicator.visibility = if (selected) View.VISIBLE else View.GONE

        // 如果选中，调整指示器宽度
        if (selected) {
            textView.post {
                indicator.layoutParams.width = textView.width / 2
                indicator.layoutParams.height = textView.height / 2
                indicator.background = com.score.callmetest.util.DrawableUtils.createRoundRectDrawable(
                    resources.getColor(R.color.tab_indicator_color),
                    textView.height / 4f
                )
                indicator.requestLayout()
            }
        }
    }

    //状态更新方法
    private fun triggerStatusUpdate() {
        try {
            val providedList = arrayListOf<String>()
            // 只添加当前显示的fragment
            val currentSelectedFragment = childFragmentManager.fragments.getOrNull(binding.viewPager.currentItem)

            if (currentSelectedFragment != null && currentSelectedFragment is IMessageStatus) {
                val visibleUserIds = currentSelectedFragment.provideVisibleUserIds()
                providedList.addAll(visibleUserIds)
            }

            viewModel.checkOnlineStatus(providedList)
        } catch (e: Exception) {
        }
    }

    /**
     * ViewPager适配器
     */
    private inner class MessagePagerAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {
        override fun getItemCount(): Int = binding.tabMessage.tabCount

        override fun createFragment(position: Int): Fragment {
            return when (position) {
                0 -> MsgListFragment()
                1 -> MessageFollowingFragment()
                2 -> CallHistoryFragment()
                else -> throw IllegalArgumentException("Invalid position: $position")
            }
        }
    }

    private fun isCurrentTabOfMain(): Boolean {
        if (activity !is MainActivity) {
            return true
        }
        val currentTabFragmentName = (activity as MainActivity).getCurrentTabFragmentName()
        return currentTabFragmentName == this::class.java.simpleName
    }

    override fun onResume() {
        super.onResume()
        if (!isCurrentTabOfMain()) {
            return
        }
        FlashChatManager.startShowDialogTimer()

        lifecycleScope.launch {
            if (isFirstResume) {
                // 首次onResume需要延迟，等待数据初始化完成
                delay(500L)
                isFirstResume = false
            } else {
                // 非首次onResume，直接触发状态更新
            }
            triggerStatusUpdate()
        }
    }

    override fun onPause() {
        super.onPause()
        FlashChatManager.stopShowDialogTimer()
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        Timber.d("onHiddenChanged: hidden=$hidden")
        if (hidden) {
            FlashChatManager.stopShowDialogTimer()
        } else {
            FlashChatManager.startShowDialogTimer()
            triggerStatusUpdate()
        }
    }
}