package com.score.callmetest.ui.widget

import DateUtils
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogVipSuccessBinding
import com.score.callmetest.manager.VipManager
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DialogUtils
import com.score.callmetest.util.click

// agree 按钮在下面，cancel 在上面

class VipSuccessDialog(
    context: Context,
) : Dialog(context, androidx.appcompat.R.style.Theme_AppCompat_Dialog) {
    private var binding: DialogVipSuccessBinding = DialogVipSuccessBinding.inflate(LayoutInflater.from(context))

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        setCancelable(true)

        // 设置全屏/居中
        window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        // 隐藏状态栏
        window?.decorView?.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
        window?.setBackgroundDrawableResource(R.color.transparent)
        // 点击空白区域可取消
        setCanceledOnTouchOutside(true)


        val dateString = DateUtils.getDate(VipManager.getExpiredTime()!!.toLong())
        val fullText = context.getString(R.string.expiry_date_text, dateString)
        val spannableString = SpannableString(fullText)
        
        // 找到日期部分的位置并设置为粗体
        val dateStartIndex = fullText.indexOf(dateString)
        if (dateStartIndex != -1) {
            val dateEndIndex = dateStartIndex + dateString.length
            spannableString.setSpan(
                StyleSpan(android.graphics.Typeface.BOLD),
                dateStartIndex,
                dateEndIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        
        binding.expireDateText.text = spannableString

        CustomUtils.playSvgaOnce(binding.iconSvga, "vip_success.svga")

        binding.close.click {
            DialogUtils.safeDismiss(this)
        }
    }
}