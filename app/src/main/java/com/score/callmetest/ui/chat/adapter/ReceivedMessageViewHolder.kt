package com.score.callmetest.ui.chat.adapter

import android.graphics.drawable.Drawable
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.UnderlineSpan
import android.text.method.LinkMovementMethod
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.google.gson.Gson
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemChatMessageLeftBinding
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageType
import com.score.callmetest.entity.isRobot
import com.score.callmetest.manager.TranslateManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.FAQInfoList
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.LanguageUtils
import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.CoroutineScope
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 接收消息ViewHolder
 */
internal class ReceivedMessageViewHolder(
    val scope: CoroutineScope,
    private val binding: ItemChatMessageLeftBinding,
    private val mChatMessageListeners: ChatAdapterListeners
) :
    RecyclerView.ViewHolder(binding.root), MessageHolder {

    private var mCurrentMessage: ChatMessageEntity? = null

    /**
     * 是否正在翻译。。。
     */
    private val mIsTransIng = AtomicBoolean(false)

    // 事件
    init {
        // 消息点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.tvMessage) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnMessageClickListener?.invoke(message, binding.tvMessage)
            }
        }

        // image点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivImage) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnImageClickListener?.invoke(message, binding.ivImage)
            }
        }

        // 消息长按
        binding.tvMessage.setOnLongClickListener {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnMessageLongClickListener?.invoke(message, binding.tvMessage)
                    ?: false
            } ?: false
        }

        // image长按
        binding.ivImage.setOnLongClickListener {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnImageLongClickListener?.invoke(message, binding.ivImage)
                    ?: false
            } ?: false
        }

        // 头像
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivAvatar) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnAvatarClickListener?.invoke(message,binding.ivAvatar)
            }
        }

        // 翻译按钮点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.tvTranslate) {
            mCurrentMessage?.let { message ->
                if (mIsTransIng.get()) {
                    // 正在翻译---点击无效
                    Timber.d("正在翻译---点击无效")
                    return@let
                }
                // 通知外部已经开始翻译
                mChatMessageListeners.mOnTranslateClickListener?.invoke(message)
                translateMessage()
            }
        }

    }

    private fun translateMessage() {
        showTranslateLoading(true)
        // 翻译前期准备
        val needTranslate = binding.tvMessage.text.toString()
        LanguageUtils.getAppLanguage(binding.root.context, scope) { targetLang ->
            // 内部有分配线程执行翻译
            TranslateManager.translate(scope, needTranslate, targetLang) { newTxt ->
                ThreadUtils.runOnMain {
                    showTranslateLoading(false)
                    if (newTxt == null) {
                        // 翻译失败
                        Timber.d("翻译失败")
                        showTranslateButton(true)
                        return@runOnMain
                    }
                    // 翻译成功
                    Timber.d("翻译成功--$needTranslate-->$newTxt")
                    showTranslateResult(newTxt)
                }
            }
        }

    }

    override fun bind(message: ChatMessageEntity) {
        mCurrentMessage = message
        // 显示消息内容
        binding.tvMessage.text = message.content
        binding.tvOriginMessage.text = message.content
        // 暂时不需要显示时间
//        binding.tvTime.text = message.getFormattedTime()

        if (message.isRobot()) {
            // 客服账号
            GlideUtils.load(
                view = binding.ivAvatar,
                url = R.drawable.customer_service,
                placeholder = R.drawable.customer_service,
                isCircle = true
            )
        } else {
            // 加载头像
            val userInfo = UserInfoManager.getCachedUserInfo(message.senderId)
             val urls = buildList {
                // 优先使用userInfo里面的头像
                add(userInfo?.avatarThumbUrl)
                 add(userInfo?.avatarMiddleThumbUrl)
                add(userInfo?.avatar)
                add(message.senderAvatar)
            }

            GlideUtils.loadWithFallbackList(
                view =binding.ivAvatar,
                uriList = urls,
                error = R.drawable.placeholder
            )

        }

        // 根据消息类型显示不同内容
        when (message.messageType) {
            MessageType.TEXT -> {
                binding.tvMessage.visibility = View.VISIBLE
                binding.ivImage.visibility = View.GONE

                // 根据消息内容判断是否显示翻译按钮
                if (message.content.isNotEmpty()) {
                    showTranslateButton(true)
                    // 检查是否开启了自动翻译
                    if(message.isAutoTrans){
                        binding.tvMessage.post { translateMessage() }
                    }

                } else {
                    showTranslateButton(false)
                }
                // 其他消息类型视图处理...
            }

            MessageType.IMAGE -> {
                binding.tvMessage.visibility = View.GONE
                binding.ivImage.visibility = View.VISIBLE

                showTranslateButton(false)

                // 加载图片
                val urls = buildList {
                    // 优先级1: 本地URI (如果有访问权限)
//                    message.thumbUri?.let { add(it) }
                    // 优先级2: 缩略图URI
                    message.mediaLocalUri?.let { add(it) }
                    // 优先级3: 远程媒体URL
                    message.mediaUri?.let { add(it) }
                }
                GlideUtils.loadWithFallbackList(
                    view =binding.ivImage,
                    uriList = urls,
                    placeholder = R.drawable.image_placeholder,
                    error = R.drawable.image_placeholder,
                    radius = DisplayUtils.dp2px(12f)
                )
            }

            MessageType.LINK -> {
                // text
                val content = message.content
                val spanned = SpannableString(content)

                // 应用点击事件和样式
                spanned.setSpan(
                    ForegroundColorSpan(ContextCompat.getColor(binding.root.context, R.color.link_color)),
                    0,
                    content.length,
                    SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
                )

                // 添加下划线样式（备用）
                spanned.setSpan(
                       UnderlineSpan(),
                       0,
                       content.length,
                       Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                   )

                binding.tvMessage.text = spanned
                // 这里不需要跳转
//                binding.tvMessage.movementMethod = LinkMovementMethod.getInstance()  // 必须设置，否则链接不可点击
//                binding.tvMessage.setLinkTextColor(Color.BLUE) // 在xml中设置了

                binding.tvMessage.visibility = View.VISIBLE
                binding.ivImage.visibility = View.GONE
                showTranslateButton(false)
            }

            else -> {
                binding.tvMessage.visibility = View.VISIBLE
                binding.ivImage.visibility = View.GONE
                // 其他消息类型暂不处理，可根据需求扩展
            }
        }

    }


    private fun showTranslateButton(show: Boolean) {
        binding.tvTranslate.visibility = if (show) View.VISIBLE else View.GONE
        binding.progressTranslate.visibility = View.GONE
        binding.translateParent.visibility = View.GONE
    }

    private fun showTranslateLoading(show: Boolean) {
        binding.tvTranslate.visibility = if (show) View.VISIBLE else View.GONE
        binding.progressTranslate.visibility = if (show) View.VISIBLE else View.GONE
        mIsTransIng.set(show)
    }

    private fun showTranslateResult(text: String) {
        binding.tvTranslate.visibility = View.GONE
        binding.progressTranslate.visibility = View.GONE
        binding.tvMessage.visibility = View.INVISIBLE
        binding.translateParent.visibility = View.VISIBLE
        binding.tvTranslateContent.text = text
    }
}