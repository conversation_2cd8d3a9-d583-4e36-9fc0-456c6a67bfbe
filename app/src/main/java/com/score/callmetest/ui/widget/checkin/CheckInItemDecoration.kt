package com.score.callmetest.ui.widget.checkin

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 * 签到Item间距装饰器
 */
class CheckInItemDecoration(
    private val spacing: Int, // 间距，单位为px
    private val spanCount: Int = 4 // 每行的Item数量
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view)
        val column = position % spanCount

        // 设置左右间距
        outRect.left = spacing - column * spacing / spanCount
        outRect.right = (column + 1) * spacing / spanCount

        // 设置上下间距
        if (position < spanCount) {
            outRect.top = spacing
        }
        outRect.bottom = spacing
    }
}
