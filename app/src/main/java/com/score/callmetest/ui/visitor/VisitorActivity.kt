package com.score.callmetest.ui.visitor

import android.view.View
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityVisitorBinding
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.manager.VipChangeEvent
import com.score.callmetest.manager.VipManager
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.click
import timber.log.Timber

class VisitorActivity : BaseActivity<ActivityVisitorBinding, VisitorViewModel>() {

    private val TAG = "VisitorActivity"

    // recyclerview 相关
    private lateinit var mAdapter: VisitorAdapter
    private val mLayoutManager by lazy { LinearLayoutManager(this) }

    // 是否需要滚动到顶部的标志
    private var mNeedScrollToTop = false

    override fun getViewBinding(): ActivityVisitorBinding {
        return ActivityVisitorBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = VisitorViewModel::class.java

    override fun initView() {
        GlobalManager.addViewStatusBarTopMargin(this, binding.toolbar)
        CustomUtils.flipViewIfRTL(binding.ivBack)

        // 初始化空视图，避免后续 ViewStub 问题
        if (binding.emptyView.parent != null) {
            binding.emptyView.inflate()
        }
        binding.emptyView.visibility = View.GONE

        setupRecyclerView()
        setupSwipeRefresh()

        refresh()
    }

    override fun initListener() {
        // 返回按钮
        binding.ivBack.click {
            finish()
        }

        EventBus.observe(this, VipChangeEvent::class.java) { event ->
            refresh()
        }

        EventBus.observe(this, RechargeManager.GPSuccessEvent::class.java) { event ->
            refresh()
        }

        // 监听三方支付回调
        DualChannelEventManager.observeRechargeOrderStatus(this) {
            if (it.orderNo == null) return@observeRechargeOrderStatus
            when (it.status) {
                2 -> {
                    refresh()
                }

                3 -> {
                    // 充值失败
                }
            }
        }

        // 监听状态变化
        EventBus.observe(
            scope = lifecycleScope,
            eventType = CustomEvents.NewStatusEvent::class.java
        ) { event ->
            val newStatusMap = event.statusMap
            // 直接更新适配器中的在线状态
            mAdapter.updateVisitorStatus(newStatusMap)
        }
    }

    private fun refresh() {
        if (VipManager.isVip()) {
            binding.dialogView.visibility = View.GONE
            // VIP用户：禁用模糊效果
            mAdapter.setBlurEnabled(false)
        } else {
            binding.dialogView.visibility = View.VISIBLE
            // 非VIP用户：启用模糊效果
            mAdapter.setBlurEnabled(true, 30.0f)

            CustomUtils.playSvga(binding.unlockBtnSvga, "vip_unlock_btn.svga")

            binding.close.click {
                finish()
            }

            binding.unlockBtnSvga.click {
                VipManager.showVipDialog()
            }

            binding.dialogView.click {
                // 拦截点击事件
            }
        }
    }


    private fun setupRecyclerView() {
        mAdapter = VisitorAdapter()
        // 设置聊天点击监听器
        mAdapter.setOnChatClickListener { visitorRecord ->
            // 跳转到聊天页面
            gotoChat(visitorRecord.userId?.toString() ?: "")
        }

        binding.recyclerView.apply {
            adapter = mAdapter
            layoutManager = mLayoutManager

            // 添加滚动监听，实现加载更多
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    // 只有向下滚动时才检查
                    if (dy > 0) {
                        val visibleItemCount = mLayoutManager.childCount
                        val totalItemCount = mLayoutManager.itemCount
                        val firstVisibleItemPosition = mLayoutManager.findFirstVisibleItemPosition()

                        // 当滚动到倒数第3个item时开始加载更多
                        // 只有在底部状态为HIDDEN时才允许触发加载更多
                        if (!viewModel.isVisitorLoadingMore() &&
                            viewModel.hasMoreVisitorData() &&
                            (visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 3) {
                            loadVisitorList(false)
                        }
                    }
                }
            })
        }
    }

    /**
     * 点击item查询userinfo后跳转到聊天
     */
    fun gotoChat(userId: String) {
        // 使用ViewModel的gotoChat方法
        viewModel.gotoChat(userId) { user ->
            ChatActivity.start(this, user)
        }
    }


    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.apply {
            setColorSchemeResources(R.color.refresh_loading)
            // 下拉刷新
            setOnRefreshListener {
                // 显示刷新动画
                isRefreshing = true
                // 设置需要滚动到顶部的标志
                mNeedScrollToTop = true
                // 重新加载数据（刷新）
                loadVisitorList(isRefresh = true)
            }
        }
    }

    override fun initData() {
        super.initData()

        // 显示下拉刷新状态并加载数据
        binding.swipeRefreshLayout.isRefreshing = true
        mNeedScrollToTop = true
        loadVisitorList(isRefresh = true)
    }

    private fun loadVisitorList(isRefresh: Boolean = true) {
        // 调用ViewModel加载来访者列表
        viewModel.loadVisitorList(
            isRefresh = isRefresh,
            onVisitorListUpdated = { visitorList ->
                Timber.tag(TAG).d("来访者列表数据更新: 获取到${visitorList.size}条记录")
                // 显示/隐藏空视图
                emptyView(visitorList.isEmpty())

                // 提交新列表前保存当前滚动位置
                val firstVisiblePosition = mLayoutManager.findFirstVisibleItemPosition()

                // 更新适配器数据
                mAdapter.setData(visitorList)

                // 数据加载完成后，停止刷新动画
                binding.swipeRefreshLayout.isRefreshing = false

                // 只有在需要滚动到顶部时才滚动（下拉刷新时）
                if (mNeedScrollToTop) {
                    binding.recyclerView.scrollToPosition(0)
                    mNeedScrollToTop = false
                } else if (firstVisiblePosition == 0) {
                    // 如果原本就在顶部，确保仍然在顶部
                    binding.recyclerView.scrollToPosition(0)
                }
            },
            onLoadingStateChanged = { isLoading ->
                // 只有在不是加载更多的情况下才显示顶部加载动画
                if (!viewModel.isVisitorLoadingMore()) {
                    binding.swipeRefreshLayout.isRefreshing = isLoading
                    if (isLoading) {
                        mNeedScrollToTop = true
                    }
                }
            },
            onError = { errorMessage ->
                Timber.tag(TAG).e("Error loading visitor list: $errorMessage")
                // 错误发生时，停止刷新动画
                binding.swipeRefreshLayout.isRefreshing = false
                emptyView(true)
            },
            onBottomStateChanged = { bottomState ->
                mAdapter.setBottomState(bottomState)
            },
            onToastMessage = { resourceId ->
                val message = getString(resourceId)
                com.score.callmetest.util.ToastUtils.showShortToast(message)
            }
        )
    }



    /**
     * 显示/隐藏EmptyView
     */
    private fun emptyView(isEmpty: Boolean) {
        Timber.tag(TAG).d("设置空视图状态: isEmpty=$isEmpty")
        if (isEmpty) {
            // 显示空页面
            binding.emptyView.visibility = View.VISIBLE
            binding.recyclerView.visibility = View.GONE
        } else {
            // 显示列表，隐藏空页面
            binding.emptyView.visibility = View.GONE
            binding.recyclerView.visibility = View.VISIBLE
        }
    }

    override fun onResume() {
        super.onResume()
        // 刷新当前可见项，使头像变为最新的用户信息
        mAdapter.refreshVisibleItems()
        Timber.tag(TAG).d("onResume: 刷新可见项完成")
    }
}