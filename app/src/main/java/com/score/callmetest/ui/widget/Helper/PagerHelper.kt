package com.score.callmetest.ui.widget.Helper

import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.net.toUri
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.score.callmetest.R
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.network.BannerInfoResponse
import com.score.callmetest.ui.preview.MultiImagePreviewActivity
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.click
import timber.log.Timber

object PagerHelper {
    private val autoScrollHandler = Handler(Looper.getMainLooper())
    // 自动滚动map - key为类名，value为Runnable
    private val autoScrollMap = mutableMapOf<String, Runnable>()
    // ViewPager映射 - key为类名，value为ViewPager2
    private val viewPagerMap = mutableMapOf<String, ViewPager2>()
    // 轮播间隔映射 - key为类名，value为间隔时间
    private val intervalMap = mutableMapOf<String, Long>()
    // 轮播启用状态映射 - key为类名，value为是否启用
    private val autoScrollEnabledMap = mutableMapOf<String, Boolean>()
    // 用户滚动状态映射 - key为类名，value为是否正在滚动
    private val userScrollingMap = mutableMapOf<String, Boolean>()
    // 暂停状态映射 - key为类名，value为是否暂停
    private val pausedMap = mutableMapOf<String, Boolean>()

    fun setupBannerPager(
        viewPager: ViewPager2,
        bannerList: List<BannerInfoResponse>,
        simpleName: String,
        enableLoop: Boolean = true,
        enableAutoScroll: Boolean = true,
        autoScrollInterval: Long = 3000L,
        onItemClick: ((realIndex: Int) -> Unit)? = null,
        onPageSelected: ((realIndex: Int, totalCount: Int, isBlurred: Boolean) -> Unit)? = null,
    ) {
        val doubleUrls = ArrayList<GlideUtils.DoubleUrl>().apply {
            bannerList.map {
                add(GlideUtils.DoubleUrl(it.pic.toString(), it.pic.toString()))
            }
        }

        setupPhotoPager(
            viewPager = viewPager,
            doubleUrls = doubleUrls,
            simpleName = simpleName,
            enableLoop = enableLoop,
            enableAutoScroll = enableAutoScroll,
            autoScrollInterval = autoScrollInterval,
            scaleType = ImageView.ScaleType.FIT_XY,
            onImageClick = { realIndex, urls, isBlurred ->
                onItemClick?.invoke(realIndex)
            },
            onPageSelected = onPageSelected,
            useBlur = false,
        )
    }

    /**
     * 设置图片轮播，支持单图/多图循环，点击可预览，支持自动轮播
     * @param viewPager ViewPager2控件
     * @param doubleUrls 图片url列表
     * @param simpleName 调用类的简单名称，用作缓存key
     * @param enableLoop 是否启用循环（多图时默认true）
     * @param enableAutoScroll 是否启用自动轮播（多图时默认true）
     * @param autoScrollInterval 自动轮播间隔，默认3000ms
     * @param onImageClick 可选，图片点击回调（默认跳转大图预览）
     */
    fun setupPhotoPager(
        viewPager: ViewPager2,
        doubleUrls: List<GlideUtils.DoubleUrl>,
        simpleName: String,
        enableLoop: Boolean = true,
        maxLoopSize: Int = -1,
        enableAutoScroll: Boolean = true,
        autoScrollInterval: Long = 3000L,
        scaleType: ImageView.ScaleType? = ImageView.ScaleType.CENTER_CROP,
        onImageClick: ((realIndex: Int, urls: List<GlideUtils.DoubleUrl>, isBlurred: Boolean) -> Unit)? = null,
        onPageSelected: ((realIndex: Int, totalCount: Int, isBlurred: Boolean) -> Unit)? = null,
        useBlur: Boolean = false,
        blurStartIndex: Int = 3,
    ) {
        // 对图片URL进行去重处理
        val uniqueImageUrls = doubleUrls.distinct()

        if (uniqueImageUrls.isEmpty()) return

        // 清理之前的资源
        cleanup(simpleName)

        // 初始化状态
        userScrollingMap[simpleName] = false
        pausedMap[simpleName] = false
        viewPagerMap[simpleName] = viewPager
        intervalMap[simpleName] = autoScrollInterval

        GlobalManager.setNeverOverScroll(viewPager)

        viewPager.offscreenPageLimit = 4

        if (uniqueImageUrls.size > 1 && enableLoop) {
            // 创建循环图片列表，支持maxLoopSize限制
            val loopImageDoubleUrls = mutableListOf<GlideUtils.DoubleUrl>()
            
            // 确定实际使用的图片数量
            val actualSize = if (maxLoopSize > 0 && maxLoopSize < uniqueImageUrls.size) {
                maxLoopSize
            } else {
                uniqueImageUrls.size
            }
            
            val actualImageUrls = uniqueImageUrls.take(actualSize)
            
            // 构建循环列表：最后一张 + 所有图片 + 第一张
            if (actualImageUrls.size > 1) {
                loopImageDoubleUrls.add(actualImageUrls.last())
                loopImageDoubleUrls.addAll(actualImageUrls)
                loopImageDoubleUrls.add(actualImageUrls.first())
            } else {
                // 如果只有一张图片，直接添加
                loopImageDoubleUrls.addAll(actualImageUrls)
            }
            
            Timber.tag("PagerHelper").d("${simpleName}-创建循环列表，原始数量: ${uniqueImageUrls.size}, 实际使用: $actualSize, 循环后数量: ${loopImageDoubleUrls.size}")

            val adapter = object : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
                override fun onCreateViewHolder(
                    parent: ViewGroup,
                    viewType: Int
                ): RecyclerView.ViewHolder {
                    val imageView = ImageView(parent.context)
                    imageView.layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                    imageView.scaleType = scaleType
                    imageView.click {
                        val realIndex =
                            (viewPager.currentItem - 1 + actualImageUrls.size) % actualImageUrls.size
                        Timber.tag("PagerHelper").d("${simpleName}-点击事件 - 当前位置: ${viewPager.currentItem}, 真实索引: $realIndex, 实际图片数量: ${actualImageUrls.size}")
                        
                        // 检查当前图片是否应该显示模糊效果
                        val shouldUseBlur = useBlur && realIndex >= blurStartIndex
                        Timber.tag("PagerHelper").d("${simpleName}-点击图片 - 真实索引: $realIndex, 是否模糊: $shouldUseBlur")
                        
                        if (onImageClick != null) {
                            // 使用回调处理点击事件，传递模糊状态信息
                            onImageClick(realIndex, actualImageUrls, shouldUseBlur)
                        } else {
                            // 默认行为：模糊图片不处理，清晰图片浏览大图
                            if (!shouldUseBlur) {
                                val intent = Intent(parent.context, MultiImagePreviewActivity::class.java)
                                intent.putParcelableArrayListExtra(
                                    "imageUris", ArrayList(actualImageUrls.map { it.primary.toString().toUri() })
                                )
                                intent.putExtra("startIndex", realIndex)
                                parent.context.startActivity(intent)
                            }
                        }
                    }
                    return object : RecyclerView.ViewHolder(imageView) {}
                }

                override fun getItemCount() = loopImageDoubleUrls.size
                override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
                    val imageView = holder.itemView as ImageView
                    // 计算真实索引（去除循环添加的首尾项）
                    val realPosition = if (position == 0) {
                        actualImageUrls.size - 1
                    } else if (position == loopImageDoubleUrls.size - 1) {
                        0
                    } else {
                        position - 1
                    }
                    
                    // 判断是否使用模糊效果：前三张清晰，从第四张开始模糊
                    val shouldUseBlur = useBlur && realPosition >= blurStartIndex
                    Timber.tag("PagerHelper").d("${simpleName}-循环模式 - 位置: $position, 真实位置: $realPosition, 使用模糊: $shouldUseBlur, 模糊启用: $useBlur, 模糊起始索引: $blurStartIndex")
                    
                    // 使用GlideUtils的模糊功能
                    if (shouldUseBlur) {
                        // 使用模糊效果
                        GlideUtils.loadWithBlur(
                            imageView = imageView,
                            doubleUrl = loopImageDoubleUrls[position],
                            blurRadius = 100.0f,  // 模糊程度
                            onResourceReady = {
                                if (realPosition == 0 || position == loopImageDoubleUrls.size - 1) {
                                    ThreadUtils.runOnMainDelayed(400) {
                                        if (viewPager != null) {
                                            viewPager.visibility = View.VISIBLE
                                        }
                                    }
                                }
                            }
                        )
                    } else {
                        // 不使用模糊效果
                        GlideUtils.load(
                            imageView = imageView,
                            doubleUrl = loopImageDoubleUrls[position],
                            onResourceReady = {
                                if (realPosition == 0 || position == loopImageDoubleUrls.size - 1) {
                                    ThreadUtils.runOnMainDelayed(400) {
                                        if (viewPager != null) {
                                            viewPager.visibility = View.VISIBLE
                                        }
                                    }
                                }
                            }
                        )
                    }
                }
            }
            viewPager.adapter = adapter
            viewPager.setCurrentItem(1, false)

            // 创建页面变化回调
            // 注册回调
            viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageScrollStateChanged(state: Int) {
                    if (state == ViewPager2.SCROLL_STATE_IDLE) {
                        val itemCount = loopImageDoubleUrls.size
                        val cur = viewPager.currentItem
                        when (cur) {
                            0 -> viewPager.setCurrentItem(itemCount - 2, false)
                            itemCount - 1 -> viewPager.setCurrentItem(1, false)
                        }
                        userScrollingMap[simpleName] = false
                        if (enableAutoScroll) {
                            startAutoScroll(simpleName, viewPager, autoScrollInterval)
                        }
                    } else if (state == ViewPager2.SCROLL_STATE_DRAGGING) {
                        userScrollingMap[simpleName] = true
                        stopAutoScroll(simpleName)
                    }
                }

                override fun onPageSelected(position: Int) {
                    val realIndex =
                        (viewPager.currentItem - 1 + actualImageUrls.size) % actualImageUrls.size
                    val shouldUseBlur = useBlur && realIndex >= blurStartIndex
                    Timber.tag("PagerHelper").d("${simpleName}-页面选择 - 位置: $position, 当前项: ${viewPager.currentItem}, 真实索引: $realIndex, 实际图片数量: ${actualImageUrls.size}")
                    onPageSelected?.invoke(realIndex, actualImageUrls.size, shouldUseBlur)
                    super.onPageSelected(position)
                }
            })

            // 启动自动轮播
            if (enableAutoScroll) {
                startAutoScroll(simpleName, viewPager, autoScrollInterval)
            }
        } else {
            // 单图
            val adapter = object : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
                override fun onCreateViewHolder(
                    parent: ViewGroup,
                    viewType: Int
                ): RecyclerView.ViewHolder {
                    val imageView = ImageView(parent.context)
                    imageView.layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                    imageView.scaleType = scaleType
                    imageView.click {
                        // 检查当前图片是否应该显示模糊效果
                        val shouldUseBlur = useBlur && 0 >= blurStartIndex
                        Timber.tag("PagerHelper").d("${simpleName}-单图模式点击 - 是否模糊: $shouldUseBlur")
                        
                        if (onImageClick != null) {
                            // 使用回调处理点击事件，传递模糊状态信息
                            onImageClick(0, uniqueImageUrls, shouldUseBlur)
                        } else {
                            // 默认行为：模糊图片不处理，清晰图片浏览大图
                            if (!shouldUseBlur) {
                                val intent = Intent(parent.context, MultiImagePreviewActivity::class.java)
                                intent.putParcelableArrayListExtra(
                                    "imageUris", ArrayList(uniqueImageUrls.map { it.primary.toString().toUri() })
                                )
                                intent.putExtra("startIndex", 0)
                                parent.context.startActivity(intent)
                            }
                        }
                    }
                    return object : RecyclerView.ViewHolder(imageView) {}
                }

                override fun getItemCount() = uniqueImageUrls.size
                override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
                    val imageView = holder.itemView as ImageView
                    // 判断是否使用模糊效果：前三张清晰，从第四张开始模糊
                    val shouldUseBlur = useBlur && position >= blurStartIndex
                    timber.log.Timber.tag("PagerHelper").d("${simpleName}-单图模式 - 位置: $position, 使用模糊: $shouldUseBlur, 模糊启用: $useBlur, 模糊起始索引: $blurStartIndex")

                    // 不使用模糊效果
                    GlideUtils.load(
                        imageView = imageView,
                        doubleUrl = uniqueImageUrls[position],
                        placeholder = R.drawable.image_placeholder,
                        onResourceReady = {
                            ThreadUtils.runOnMainDelayed(400) {
                                if (viewPager != null) {
                                    viewPager.visibility = View.VISIBLE
                                }
                            }
                        }
                    )
                }
            }
            viewPager.adapter = adapter
            viewPager.setCurrentItem(0, false)
            stopAutoScroll(simpleName)
        }
    }

    /**
     * 启动自动轮播
     */
    private fun startAutoScroll(key: String, viewPager: ViewPager2, interval: Long) {
        if (autoScrollEnabledMap[key] == true) return
        autoScrollEnabledMap[key] = true

        val runnable = object : Runnable {
            override fun run() {
                val isUserScrolling = userScrollingMap[key] ?: false
                val isPaused = pausedMap[key] ?: false
                if (!isUserScrolling && !isPaused) {
                    val cur = viewPager.currentItem
                    viewPager.setCurrentItem(cur + 1, true)
                }
                autoScrollHandler.postDelayed(this, interval)
            }
        }

        autoScrollMap[key] = runnable
        autoScrollHandler.postDelayed(runnable, interval)
        
        Timber.tag("PagerHelper").d("自动轮播已启动 - key: $key, 间隔: ${interval}ms")
    }

    /**
     * 停止自动轮播
     */
    private fun stopAutoScroll(key: String) {
        autoScrollEnabledMap[key] = false
        autoScrollMap[key]?.let { runnable ->
            autoScrollHandler.removeCallbacks(runnable)
            Timber.tag("PagerHelper").d("自动轮播已停止 - key: $key")
        }
        autoScrollMap.remove(key)
    }

    /**
     * 暂停自动轮播
     * 可以在外部调用，比如用户正在查看内容时暂停轮播
     * @param key 调用类的简单名称
     */
    fun pauseAutoScroll(key: String) {
        pausedMap[key] = true
        Timber.tag("PagerHelper").d("自动轮播已暂停 - key: $key")
    }

    /**
     * 恢复自动轮播
     * 可以在外部调用，恢复之前暂停的轮播
     * @param key 调用类的简单名称
     */
    fun resumeAutoScroll(key: String) {
        val isPaused = pausedMap[key] ?: false
        if (isPaused) {
            pausedMap[key] = false
            Timber.tag("PagerHelper").d("自动轮播已恢复 - key: $key")
        }
    }

    /**
     * 检查当前轮播是否处于暂停状态
     * @param key 调用类的简单名称
     */
    fun isAutoScrollPaused(key: String): Boolean {
        return pausedMap[key] ?: false
    }

    /**
     * 检查当前是否有活跃的轮播
     * @param key 调用类的简单名称
     */
    fun isAutoScrollActive(key: String): Boolean {
        val isEnabled = autoScrollEnabledMap[key] ?: false
        val isPaused = pausedMap[key] ?: false
        return isEnabled && !isPaused
    }

    /**
     * 清理指定key的资源，在Activity/Fragment销毁时调用
     * @param key 调用类的简单名称
     */
    fun cleanup(key: String) {
        stopAutoScroll(key)
        userScrollingMap.remove(key)
        pausedMap.remove(key)
        viewPagerMap.remove(key)
        intervalMap.remove(key)
        autoScrollEnabledMap.remove(key)
        Timber.tag("PagerHelper").d("已清理资源 - key: $key")
    }

    /**
     * 清理所有资源，在应用退出或需要全局清理时调用
     */
    fun cleanupAll() {
        // 停止所有自动轮播
        autoScrollMap.keys.toList().forEach { key ->
            stopAutoScroll(key)
        }
        
        // 清理所有映射
        autoScrollMap.clear()
        viewPagerMap.clear()
        intervalMap.clear()
        autoScrollEnabledMap.clear()
        userScrollingMap.clear()
        pausedMap.clear()
        
        Timber.tag("PagerHelper").d("已清理所有资源")
    }
} 