package com.score.callmetest.ui.main

data class RechargeOption(
    val iconRes: Int,
    val coinAmount: Int,
    val price: String,
    val oldPrice: String? = null,
    val discount: Int? = null,  //折扣
    val goodsCode: String? = null,
    val tags: String? = null,
    val extraCoinPercent: Int? = null, // 额外的金币比例
    val type: String? = "0",  //0-普通商品；1-促销商品；2-活动商品 ；3-订阅商品
    val name: String? = null,
    val remainMilliseconds: Long? = null, // 剩余时间，单位毫秒
    val invitationId: String? = null, // 邀请链接id
    val priceSymbol: String? = "$" // 价格符号
)
