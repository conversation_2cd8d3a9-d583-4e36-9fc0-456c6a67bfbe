package com.score.callmetest.ui.chat.adapter

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemChatMessageVoiceLeftBinding
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.GlideUtils

/**
 * 接收语音消息ViewHolder
 */
internal class ReceivedVoiceMessageViewHolder(
    private val binding: ItemChatMessageVoiceLeftBinding,
    private val mChatMessageListeners: ChatAdapterListeners
) : RecyclerView.ViewHolder(binding.root), VoiceMessageViewHolder,MessageHolder {
    private var mCurrentMessage: ChatMessageEntity? = null

    init {
        // 语音消息点击播放
        ClickUtils.setOnGlobalDebounceClickListener(binding.llVoiceContainer){
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnMessageClickListener?.invoke(message,binding.llVoiceContainer)
            }
        }
        // 语音消息长按
        binding.llVoiceContainer.setOnLongClickListener{
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnMessageLongClickListener?.invoke(message,binding.llVoiceContainer) ?: false
            } ?: false
        }

        // 头像
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivAvatar){
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnAvatarClickListener?.invoke(message,binding.ivAvatar)
            }
        }

    }

    override fun bind(message: ChatMessageEntity) {
        mCurrentMessage = message
        // 保存引用，方便在RecycledView回收后找到对应的Message
        itemView.tag = message

        // 显示发送者名称
//        binding.tvName.text = message.senderName

        // 加载头像
        val userInfo = UserInfoManager.getCachedUserInfo(message.senderId)
        val urls = buildList {
            add(userInfo?.avatarThumbUrl)
            add(userInfo?.avatarMiddleThumbUrl)
            add(userInfo?.avatar)
            add(message.senderAvatar)
        }
        GlideUtils.loadWithFallbackList(
            view =binding.ivAvatar,
            uriList = urls,
            error = R.drawable.placeholder
        )

        // 显示语音时长
        val duration = message.mediaDuration / 1000
        binding.tvDuration.text = if (duration > 0) "$duration\"" else "1\""

    }

    override fun updateEntity(messageEntity: ChatMessageEntity) {
        this.mCurrentMessage = messageEntity
    }

    override fun startPlayAnimation() {
        // 实现语音播放动画 - 切换到播放中的图标
        CustomUtils.playSvga(binding.ivVoice, "voice_left.svga")
    }

    override fun stopPlayAnimation() {
        // 停止语音播放动画 - 切换回静态图标
        binding.ivVoice.stopAnimation()
    }

}