package com.score.callmetest.ui.home.ranklist.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemCoupleListBinding
import com.score.callmetest.network.RankData
import com.score.callmetest.network.UserRankModel
import com.score.callmetest.ui.widget.CircleIconButton
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click
import timber.log.Timber

/**
 * 情侣排行榜RecyclerView适配器
 * 负责展示情侣排行榜列表项（从第4名开始）
 */
class CoupleListAdapter : ListAdapter<RankData, CoupleListAdapter.CoupleRankViewHolder>(CoupleRankDiffCallback()) {
    private val TAG = "CoupleListAdapter"

    private var mOnItemClickListener: ((RankData) -> Unit)? = null
    private var mOnAvatarClickListener: ((UserRankModel) -> Unit)? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CoupleRankViewHolder {
        val binding = ItemCoupleListBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CoupleRankViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CoupleRankViewHolder, position: Int) {
        val item = getItem(position)
        holder.bind(item, position)
    }

    /**
     * 设置列表项点击监听器
     */
    fun setOnItemClickListener(listener: (RankData) -> Unit) {
        mOnItemClickListener = listener
    }
    
    /**
     * 设置头像点击监听器
     */
    fun setOnAvatarClickListener(listener: (UserRankModel) -> Unit) {
        mOnAvatarClickListener = listener
    }

    /**
     * 获取实际数据列表（从第4名开始）
     */
    fun submitRankList(rankList: List<RankData>) {
        // 从第4名开始，跳过前三名
        val listFromFourth = if (rankList.size > 3) {
            rankList.subList(3, rankList.size)
        } else {
            emptyList()
        }
        submitList(listFromFourth)
    }

    /**
     * ViewHolder类
     */
    inner class CoupleRankViewHolder(private val binding: ItemCoupleListBinding) : RecyclerView.ViewHolder(binding.root) {
        
        init {
            // 设置整个item的点击事件
            itemView.click {
                val position = bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val rankData = getItem(position)
                    // 先改变背景颜色
                    changeBackgroundColor()
                    mOnItemClickListener?.invoke(rankData)
                }
            }
        }
        
        /**
         * 改变背景颜色为#fff5f5fa
         */
        private fun changeBackgroundColor() {
            try {
                val backgroundColor = "#fff5f5fa".toColorInt()
                val radius = DisplayUtils.dp2pxInternalFloat(8f) // 8dp圆角
                
                itemView.background = DrawableUtils.createRoundRectDrawable(
                    color = backgroundColor,
                    radius = radius
                )
                
                // 1秒后恢复原背景
                itemView.postDelayed({
                    itemView.background = null
                }, 1000)
                
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "改变背景颜色失败")
            }
        }
        
        fun bind(rankData: RankData, position: Int) {
            try {
                // 显示排名（从4开始，因为前三名单独显示）
                val rank = rankData.sort ?: (position + 4)
                binding.tvMineRank.text = rank.toString()
                
                // 统一头像加载逻辑
                loadAvatars(rankData)
                
                // 显示情侣昵称组合
                displayCoupleNames(rankData)
                
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "情侣排行榜项绑定时出错 position=$position")
                
                // 出错时显示默认值
                binding.tvMineRank.text = (position + 4).toString()
                binding.tvMineName.text = "Unknown"
                binding.tvCoupleAvatar1.setImageResource(R.drawable.placeholder)
                binding.tvCoupleAvatar2.setImageResource(R.drawable.placeholder)
            }
        }
        
        /**
         * 统一头像加载方法
         */
        private fun loadAvatars(rankData: RankData) {
            // 获取两个用户模型
            val userModel = rankData.userModel
            val broadcasterModel = rankData.broadcastetModel
            
            // 加载第一个头像
            loadAvatar(userModel, binding.tvCoupleAvatar1)
            
            // 加载第二个头像
            loadAvatar(broadcasterModel, binding.tvCoupleAvatar2)
            
            // 设置头像点击事件
            setupAvatarClickListeners(broadcasterModel)
        }
        
        /**
         * 设置头像点击事件
         */
        private fun setupAvatarClickListeners( broadcasterModel: UserRankModel?) {

            // 第二个头像点击事件，主播头像
            binding.tvCoupleAvatar2.click {
                broadcasterModel?.let { broadcaster ->
                    mOnAvatarClickListener?.invoke(broadcaster)
                }
            }
        }
        
        /**
         * 加载单个头像
         */
        private fun loadAvatar(userRankModel: UserRankModel?, imageView: CircleIconButton) {
            if (userRankModel == null) {
                imageView.setImageResource(R.drawable.placeholder)
                return
            }
            
            // 优先使用avatar，其次使用avatarMapPath，最后使用默认头像
            val avatarUrl = userRankModel.avatar ?: userRankModel.avatarMapPath
            
            if (!avatarUrl.isNullOrBlank()) {
                GlideUtils.load(
                    imageView,
                    avatarUrl,
                    placeholder = R.drawable.placeholder,
                    error = R.drawable.placeholder,
                    isCircle = true
                )
            } else {
                imageView.setImageResource(R.drawable.placeholder)
            }
        }
        
        /**
         * 显示情侣昵称组合
         */
        private fun displayCoupleNames(rankData: RankData) {
            val userModel = rankData.userModel
            val broadcasterModel = rankData.broadcastetModel
            
            val nickname1 = CustomUtils.rankTruncateNickname(userModel?.nickname ?: "Unknown",16)
            val nickname2 = CustomUtils.rankTruncateNickname(broadcasterModel?.nickname ?: "Unknown",16)
            
            // 如果两个用户不同，则显示组合昵称，否则只显示一个
            binding.tvMineName.text = if (userModel != broadcasterModel && nickname1 != nickname2) {
                "$nickname1 & $nickname2"
            } else {
                nickname1
            }
        }
    }

    /**
     * DiffUtil回调，用于优化列表更新性能
     */
    private class CoupleRankDiffCallback : DiffUtil.ItemCallback<RankData>() {
        override fun areItemsTheSame(oldItem: RankData, newItem: RankData): Boolean {
            // 使用排序序号作为唯一标识
            return oldItem.sort == newItem.sort
        }

        override fun areContentsTheSame(oldItem: RankData, newItem: RankData): Boolean {
            return oldItem == newItem
        }
    }
}
