package com.score.callmetest.ui.videocall

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.score.callmetest.Constant
import com.score.callmetest.HangupScene
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentCallIncomingBinding
import com.score.callmetest.manager.AppLifecycleManager
import com.score.callmetest.manager.AudioPlayManager
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.HangUpReason
import com.score.callmetest.manager.LogReportManager
import com.score.callmetest.manager.OnCallMessage
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.LiveCallAction
import com.score.callmetest.network.LiveCallExt
import com.score.callmetest.network.LiveCallExt2
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.home.adapter.BroadcasterAdapter
import com.score.callmetest.ui.widget.Helper.PagerHelper
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.click
import kotlin.text.indexOf

class CallIncomingFragment(
    val channelName: String,
    val userId: String,
    val avatarUrl: String?,
    val nickname: String?,
    val age: String?,
    val country: String?,
    val freeTip: String?,
) : BaseFragment<FragmentCallIncomingBinding, CallIncomingViewModel>() {
    override fun getViewModelClass() = CallIncomingViewModel::class.java
    override fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?) =
        FragmentCallIncomingBinding.inflate(inflater, container, false)

    private var onCallMessage: OnCallMessage? = null

    private val PAGER_KEY = this::class.java.simpleName

    fun getVideoActivity(): VideoCallActivity? {
        return activity as? VideoCallActivity
    }

    override fun initView() {
        startRingtone()
        // 头像优先用avatarUrl，支持多图轮播
        val imageUrls = mutableListOf<GlideUtils.DoubleUrl>()
        if (!avatarUrl.isNullOrEmpty()) {
            imageUrls.add(GlideUtils.DoubleUrl(avatarUrl))
        }

        // 如有mediaList可在此补充
        PagerHelper.setupPhotoPager(
            viewPager = binding.photoPager,
            doubleUrls = imageUrls,
            simpleName = PAGER_KEY,
            onImageClick = { realIndex, urls, isBlurred ->
                // 拦截图片点击，不进入预览大图
            },
            maxLoopSize = Constant.MAX_PHOTO_SIZE_WHEN_VIDEO,
        )
        binding.tvNickname.text = nickname ?: ""
        binding.tvNickname.requestFocus() // 激活跑马灯效果
        binding.tvAge.text = age ?: ""
        binding.tvCountry.text = country ?: ""
        // 免费提示优先用broadcasterCallDesc
        if (freeTip.isNullOrEmpty()) {
            val price = CallIncomingManager.getCurrentCallInfo()?.unitPrice.toString()
            val te = getString(R.string.you_will_cost_format, price)
            val spannable = SpannableString(te)
            // 高亮显示商品名称
            val start = te.indexOf(price)
            if (start >= 0) {
                val end = start + price.length
                spannable.setSpan(
                    ForegroundColorSpan("#2DE314".toColorInt()),
                    start, end,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            binding.tvFreeTip.text = spannable
        } else {
            // 这里不用做翻译，因为下发的就是英文
            val price = "FREE"
            val spannable = SpannableString(freeTip)
            val start = freeTip.indexOf(price)
            if (start >= 0) {
                val end = start + price.length
                spannable.setSpan(
                    ForegroundColorSpan("#2DE314".toColorInt()),
                    start, end,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            binding.tvFreeTip.text = spannable
        }

        if (StrategyManager.isReviewPkg()) {
            binding.tvFreeTip.text = ""
        }

        binding.ageLayout.doOnPreDraw {
            binding.ageLayout.background = DrawableUtils.createRoundRectDrawableWithStrokeDp(
                fillColor = Color.TRANSPARENT,
                radiusDp = binding.ageLayout.height / 2f,
                strokeColor = resources.getColor(R.color.age_color),
                strokeWidth = DisplayUtils.dp2pxInternal(1f)
            )
        }

        binding.tvCountry.doOnPreDraw {
            binding.tvCountry.background = DrawableUtils.createRoundRectDrawableWithStrokeDp(
                fillColor = Color.TRANSPARENT,
                radiusDp = binding.tvCountry.height / 2f,
                strokeColor = resources.getColor(R.color.country_color),
                strokeWidth = DisplayUtils.dp2pxInternal(1f)
            )
        }

        binding.bottomShadow.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#00080029".toColorInt(), Color.BLACK),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM
        )

        UserInfoManager.getUserInfo(userId,scope = lifecycleScope) { getUserInfo ->
            getUserInfo?.let { updateUserInfoUI(it) }
        }

        // 播放SVGA动画
        CustomUtils.playSvga(binding.btnAccept, "video_comming.svga")

        binding.btnAccept.click {
            binding.btnAccept.isEnabled = false
            binding.btnReject.isEnabled = false

            AgodaUtils.observeUserJoined(owner = this, onEvent = { event ->
                if (onCallMessage != null) {
                    binding.tvFreeTip.text = getString(R.string.connecting)
                    ThreadUtils.runOnMainDelayed(1000) {
                        binding.btnAccept.isEnabled = true
                        binding.btnReject.isEnabled = true
                        getVideoActivity()?.getVideoCallViewModel()?.markCallStart()
                        getVideoActivity()?.getVideoCallViewModel()?.hasEnteredOngoing = true
                        VideoCallActivity.startOngoing(
                            requireActivity(),
                            onCallMessage!!.channelName,
                            onCallMessage!!.toUserId,
                            onCallMessage!!.fromUserId,
                            rtcToken = onCallMessage!!.rtcToken
                        )
                    }
                }
            })

            CallIncomingManager.handleAccept(
                onSuccess = { onCallMessage ->
                    this.onCallMessage = onCallMessage
                },
                onError = {
                    binding.btnAccept.isEnabled = true
                    binding.btnReject.isEnabled = true
                },
                onPermissionDeny = {
                    finishActivity()
                }
            )
        }
        binding.btnReject.click {
            binding.btnAccept.isEnabled = false
            binding.btnReject.isEnabled = false

            // 上报拒绝日志
            LogReportManager.reportLiveCallEvent(
                channelName = channelName,
                action = LiveCallAction.HANGUP,
                ext = LiveCallExt.ON_CALL,
                ext2 = LiveCallExt2.HANGUP_BUTTON
            )
            // 用户拒绝通话，保存为REJECTED_CALL
            getVideoActivity()?.getVideoCallViewModel()?.let { videoCallViewModel ->
                videoCallViewModel.saveCallHistory(com.score.callmetest.CallType.REJECTED_CALL)
            }
            CallIncomingManager.handleReject(
                hangUpReason = HangUpReason.NORMAL,
                remark = HangupScene.ON_CALL_HANGUP,
                onSuccess = {
                    binding.btnAccept.isEnabled = true
                    binding.btnReject.isEnabled = true
                    finishActivity()
                },
                onError = {
                    binding.btnAccept.isEnabled = true
                    binding.btnReject.isEnabled = true
                    finishActivity()
                }
            )
        }

        // 监听挂断事件（使用双通道去重）
        DualChannelEventManager.observeOnHangUp(this) { hangUpMessage ->
            if (hangUpMessage.channelName == channelName) {
                // 对方挂断或超时，保存为MISSED_CALL
                getVideoActivity()?.getVideoCallViewModel()?.let { videoCallViewModel ->
                    videoCallViewModel.saveCallHistory(com.score.callmetest.CallType.MISSED_CALL)
                }
                CallIncomingManager.handleReject(
                    hangUpReason = HangUpReason.CL_REMOTE_USER_LEFT,
                    remark = null
                )
                finishActivity()
            }
        }

        // 被呼叫界面进入
        LogReportManager.reportLiveCallEvent(
            channelName = channelName,
            action = LiveCallAction.ENTER,
            ext = LiveCallExt.ON_CALL
        )
    }

    private fun startRingtone() {
        if (AppLifecycleManager.isAppInBackground()) {
            return
        }

        AudioPlayManager.playVideoRingtone(requireContext(),lifecycleScope)
    }

    private fun stopRingtone() {
        AudioPlayManager.stopPlay()
    }

    override fun onResume() {
        super.onResume()
        PagerHelper.resumeAutoScroll(PAGER_KEY)
    }

    override fun onPause() {
        super.onPause()
        PagerHelper.pauseAutoScroll(PAGER_KEY)
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (hidden) {
            PagerHelper.pauseAutoScroll(PAGER_KEY)
        } else {
            PagerHelper.resumeAutoScroll(PAGER_KEY)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        stopRingtone()
        // 被呼叫界面进入
        LogReportManager.reportLiveCallEvent(
            channelName = channelName,
            action = LiveCallAction.EXIT,
            ext = LiveCallExt.ON_CALL
        )
        PagerHelper.cleanup(PAGER_KEY)
    }

    fun finishActivity() {
        try {
            requireActivity().finish()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    /**
     * 更新用户信息UI
     */
    private fun updateUserInfoUI(userInfo: com.score.callmetest.network.UserInfo) {
        // 组装图片列表（优先mediaList和头像）
        val realImageUrls = mutableListOf<GlideUtils.DoubleUrl>()
        realImageUrls.add(GlideUtils.DoubleUrl(userInfo.avatarUrl, userInfo.avatarThumbUrl))

        if (!userInfo.mediaList.isNullOrEmpty()) {
            realImageUrls.addAll(userInfo.mediaList.map {
                GlideUtils.DoubleUrl(
                    it.mediaUrl,
                    it.thumbUrl
                )
            })
        }

        PagerHelper.setupPhotoPager(
            viewPager = binding.photoPager,
            doubleUrls = realImageUrls,
            simpleName = PAGER_KEY,
            onImageClick = { realIndex, urls, isBlurred ->
                // 拦截图片点击，不进入预览大图
            },
            maxLoopSize = Constant.MAX_PHOTO_SIZE_WHEN_VIDEO,
        )

        // 更新其他UI
        binding.tvNickname.text = userInfo.nickname ?: ""
        binding.tvNickname.requestFocus() // 激活跑马灯效果
        binding.tvAge.text = userInfo.age?.toString() ?: ""
        binding.tvCountry.text = userInfo.country ?: ""
    }
} 