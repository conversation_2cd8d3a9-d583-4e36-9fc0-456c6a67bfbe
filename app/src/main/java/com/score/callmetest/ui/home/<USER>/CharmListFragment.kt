package com.score.callmetest.ui.home.ranklist

import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.OVER_SCROLL_NEVER
import android.view.View.VISIBLE
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentCharmListBinding
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterRankModel
import com.score.callmetest.network.toBroadcasterModel
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.home.ranklist.adapter.CharmListAdapter
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click
import timber.log.Timber

/**
 * 主播榜Fragment
 * 显示主播排行榜数据，包括前三名展示和列表
 */
class CharmListFragment : BaseFragment<FragmentCharmListBinding, RankListViewModel>() {
    private val TAG = "CharmListFragment"

    private lateinit var mAdapter: CharmListAdapter
    private var mEmptyView: View? = null

    override fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragmentCharmListBinding {
        return FragmentCharmListBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass(): Class<RankListViewModel> {
        return RankListViewModel::class.java
    }

    override fun initView() {
        super.initView()
        
        // 获取Activity的ViewModel实例，确保数据共享
        viewModel = ViewModelProvider(requireActivity())[RankListViewModel::class.java]
        
        setupRecyclerView()
        setupClickListeners()
        // 设置时间背景
        binding.lineCharmTime.background = DrawableUtils.createRoundRectDrawableWithStroke(
            fillColor = "#30ffffff".toColorInt(),
            radius = DisplayUtils.dp2pxInternalFloat(20f),
            strokeColor = "#4affffff".toColorInt(),
            strokeWidth = DisplayUtils.dp2pxInternal(1f)
        )

        binding.mainContentLayout.doOnPreDraw {
            initMainContentLayout()
            setupRankTopAnimation()
        }
    }

    private fun initMainContentLayout() {
        val screenHeight = binding.rootView.height
        // 限制BottomSheet最大高度为屏幕高度-250dp
        val maxHeight = screenHeight - DisplayUtils.dp2px(250f)

        val params = binding.mainContentLayout.layoutParams
        params.height = maxHeight
        binding.mainContentLayout.layoutParams = params

        val behavior = BottomSheetBehavior.from(binding.mainContentLayout)
        // 设置初始高度为屏幕的55%
        behavior.peekHeight = screenHeight * 55 / 100
        behavior.state = BottomSheetBehavior.STATE_COLLAPSED
    }

    override fun initData() {
        super.initData()

        // 观察排行榜数据
        viewModel.broadcasterRankData.observe(this) { rankResponse ->
            rankResponse?.let { data ->

                // 更新月份显示
                data.monthName?.let { monthName ->
                    binding.tvTime.text = monthName
                }
                
                // 更新排行榜列表数据
                data.rankData?.let { rankList ->
                    // 检查数据是否为空
                    if (rankList.isEmpty()) {
                        updateEmptyViewVisibility(true)
                    } else {
                        updateEmptyViewVisibility(false)
                        // 从第4名开始展示
                        mAdapter.submitRankList(rankList)

                        // 更新前三名显示
                        updateTopThreeDisplay(rankList)
                    }
                } ?: run {
                    // rankData为null时显示空页面
                    updateEmptyViewVisibility(true)
                }

            }
        }

        // 观察错误信息
        viewModel.errorMessage.observe(this) { errorMessage ->
            errorMessage?.let { error ->
                if (error.second == 1) {
                    Timber.tag(TAG).e("主播榜数据加载错误 ${error.first}")
                    // 错误时显示空页面
                    updateEmptyViewVisibility(true)
                }
            }
        }
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        mAdapter = CharmListAdapter()
        
        binding.rvCharm.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = mAdapter
            // 禁用过度滚动效果
            overScrollMode = OVER_SCROLL_NEVER
        }
        
        // 设置列表项点击事件
        mAdapter.setOnItemClickListener { broadcasterRank ->
            navigateToBroadcasterDetail(broadcasterRank)
        }
        // 列表项头像点击事件
        mAdapter.setOnAvatarClickListener { broadcasterRank ->
            navigateToBroadcasterDetail(broadcasterRank)

        }
    }

    /**
     * 设置rank_top的Svga动画
     */
    private fun setupRankTopAnimation() {
        // 播放rank_top.svga动画
        CustomUtils.playSvga(binding.rankTop, "rank_top.svga")
    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        // 前三名头像和名称点击事件
        binding.ivAvatar1.click { 
            viewModel.broadcasterRankData.value?.rankData?.getOrNull(0)?.let { broadcaster ->
                navigateToBroadcasterDetail(broadcaster)
            }
        }
        binding.tvAvatar1Name.click { 
            viewModel.broadcasterRankData.value?.rankData?.getOrNull(0)?.let { broadcaster ->
                navigateToBroadcasterDetail(broadcaster)
            }
        }
        
        binding.ivAvatar2.click { 
            viewModel.broadcasterRankData.value?.rankData?.getOrNull(1)?.let { broadcaster ->
                navigateToBroadcasterDetail(broadcaster)
            }
        }
        binding.tvAvatar2Name.click { 
            viewModel.broadcasterRankData.value?.rankData?.getOrNull(1)?.let { broadcaster ->
                navigateToBroadcasterDetail(broadcaster)
            }
        }
        
        binding.ivAvatar3.click { 
            viewModel.broadcasterRankData.value?.rankData?.getOrNull(2)?.let { broadcaster ->
                navigateToBroadcasterDetail(broadcaster)
            }
        }
        binding.tvAvatar3Name.click { 
            viewModel.broadcasterRankData.value?.rankData?.getOrNull(2)?.let { broadcaster ->
                navigateToBroadcasterDetail(broadcaster)
            }
        }
    }

    /**
     * 导航到主播详情页
     */
    private fun navigateToBroadcasterDetail(broadcasterRank: BroadcasterRankModel) {
        try {
            broadcasterRank.userId?.let { userId ->
                
                // 先获取完整的用户信息
                UserInfoManager.getUserInfo(
                    userId = userId.toString(),
                    scope = lifecycleScope,
                    forceUpdate = false
                ) { userInfo ->
                    if (userInfo != null) {
                        // 获取到完整信息后跳转
                        val intent = Intent(requireContext(), BroadcasterDetailActivity::class.java)
                        intent.putExtra(Constant.BROADCASTER_MODEL,userInfo.toBroadcasterModel())
                        startActivity(intent)
                    } else {
                        // 获取用户信息失败，使用排行榜数据跳转
                        Timber.tag(TAG).w("获取完整用户信息失败，使用排行榜数据跳转 userId=$userId")
                    }
                }
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "跳转主播详情页失败")
        }
    }

    /**
     * 更新前三名显示
     */
    private fun updateTopThreeDisplay(rankList: List<BroadcasterRankModel>) {
        try {
            // 第一名
            if (rankList.isNotEmpty()) {
                val firstBroadcaster = rankList[0]
                binding.tvAvatar1Name.text = CustomUtils.rankTruncateNickname(firstBroadcaster.nickname ?: "Unknown",20)
                loadAvatar(firstBroadcaster, binding.ivAvatar1)
            }
            
            // 第二名
            if (rankList.size > 1) {
                val secondBroadcaster = rankList[1]
                binding.tvAvatar2Name.text = CustomUtils.rankTruncateNickname(secondBroadcaster.nickname ?: "Unknown",15)
                loadAvatar(secondBroadcaster, binding.ivAvatar2)
            }
            
            // 第三名
            if (rankList.size > 2) {
                val thirdBroadcaster = rankList[2]
                binding.tvAvatar3Name.text = CustomUtils.rankTruncateNickname(thirdBroadcaster.nickname ?: "Unknown",15)
                loadAvatar(thirdBroadcaster, binding.ivAvatar3)
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "更新前三名显示时出错")
        }
    }

    /**
     * 统一头像加载方法
     */
    private fun loadAvatar(broadcasterRank: BroadcasterRankModel, imageView: android.widget.ImageView) {
        // 优先使用avatar，其次使用avatarMapPath，最后使用默认头像
        val avatarUrl = broadcasterRank.avatar ?: broadcasterRank.avatarMapPath
        
        if (!avatarUrl.isNullOrBlank()) {
            GlideUtils.load(
                imageView,
                avatarUrl,
                placeholder = R.drawable.placeholder,
                error = R.drawable.placeholder,
                isCircle = true
            )
        } else {
            imageView.setImageResource(R.drawable.placeholder)
        }
    }

    /**
     * 更新空页面的可见性
     * @param show true: 显示空页面, false: 隐藏空页面
     */
    private fun updateEmptyViewVisibility(show: Boolean) {
        try {
            if (show) {
                // 确保空状态视图已经被inflate
                if (mEmptyView == null) {
                    mEmptyView = binding.emptyView.inflate()
                }
                // 显示空状态视图，并隐藏列表
                mEmptyView?.visibility = VISIBLE
                binding.rvCharm.visibility = GONE
            } else {
                // 隐藏空状态视图，并显示列表
                mEmptyView?.visibility = GONE
                binding.rvCharm.visibility = VISIBLE
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "更新空页面可见性失败")
        }
    }
}
