package com.score.callmetest.ui.videocall

import android.app.Activity
import android.app.Dialog
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentCallEndEvaluateBinding
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterEvaluateRequest
import com.score.callmetest.network.CallResult
import com.score.callmetest.network.CallResultRequest
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RecommendedBroadcaster
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.UserInfo
import com.score.callmetest.network.toBroadcasterModel
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.widget.decoration.SpaceHorItemDecoration
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.TimeUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import androidx.core.graphics.drawable.toDrawable
import com.score.callmetest.util.CustomUtils

class CallEndEvaluateFragmentDialog(
    val userInfo: UserInfo,
    val channelName: String,
    val onDismiss: (DialogInterface) -> Unit = {}
) : BottomSheetDialogFragment() {
    private var _binding: FragmentCallEndEvaluateBinding? = null
    private val binding get() = _binding!!

    private lateinit var recommendAdapter: RecommendUserAdapter

    private var isLikeSelected = true
    private var isDislikeSelected = false
    private var selectedTags: List<String> = emptyList()

    private var tagList: List<String> = emptyList()
    private var badTagList: List<String> = emptyList()
    private var recommendList: List<RecommendedBroadcaster> = emptyList()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentCallEndEvaluateBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT
        )
        //dialog?.window?.setBackgroundDrawable(Color.GRAY.toDrawable())

        // 头像、昵称、时长
        GlideUtils.load(
            imageView = binding.avatar,
            doubleUrl = GlideUtils.DoubleUrl(userInfo.avatarUrl, userInfo.avatarThumbUrl),
            placeholder = R.drawable.placeholder
        )
        binding.tvNickname.text = userInfo.nickname

        // 推荐区Adapter
        recommendAdapter = RecommendUserAdapter()
        binding.recyclerRecommend.layoutManager =
            LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
        binding.recyclerRecommend.adapter = recommendAdapter
        val spacing12dp = (12 * resources.displayMetrics.density).toInt()
        binding.recyclerRecommend.addItemDecoration(SpaceHorItemDecoration(spacing12dp))

        // 喜恶按钮
        binding.likeBtn.doOnPreDraw {
            binding.likeBtn.background = DrawableUtils.createRoundRectDrawableWithStrokeDp(
                fillColor = Color.WHITE,
                radiusDp = binding.likeBtn.height / 2f,
                strokeColor = "#73FDCCE7".toColorInt(),
                strokeWidth = DisplayUtils.dp2pxInternal(1f)
            )
        }
        binding.dislikeBtn.doOnPreDraw {
            binding.dislikeBtn.background = DrawableUtils.createRoundRectDrawableWithStrokeDp(
                fillColor = Color.WHITE,
                radiusDp = binding.likeBtn.height / 2f,
                strokeColor = "#73FDCCE7".toColorInt(),
                strokeWidth = DisplayUtils.dp2pxInternal(1f)
            )
        }
        binding.likeBtn.elevation = DisplayUtils.dp2px(2f).toFloat()
        binding.dislikeBtn.elevation = DisplayUtils.dp2px(2f).toFloat()

        binding.likeBtn.click {
            if (!isLikeSelected) {
                updateLike()
            }
        }
        binding.dislikeBtn.click {
            if (!isDislikeSelected) {
                updateUnlike()
            }
        }

        // Next按钮
        binding.nextBtn.click {
            if (selectedTags.isEmpty()) {
                ToastUtils.showToast(getString(R.string.no_label_selected))
                return@click
            }

            // 直接显示成功提示并关闭弹窗，无需等待网络响应
            ToastUtils.showToast(CustomUtils.getString(R.string.submitted))

            // 在后台提交评价
            submitEvaluateInBackground()

            Timber.d("dsc--Next clicked: like=$isLikeSelected, tags=$selectedTags")
            dismiss()
        }
        updateLike()
        initData()
    }

    private fun initData() {
        // 获取通话评价数据
        getCallResult(
            channelName,
            onSuccess = { result ->
                val durationStr = result.duration?.toString() ?: ""
                binding.tvCallDurationTime.text =
                    TimeUtils.formatDurationToReadable(durationStr.toIntOrNull() ?: 0)

                tagList = result.tagList ?: emptyList()
                badTagList = result.badTagList ?: emptyList()
                recommendList = result.recommendList ?: emptyList()

                // 默认展示like标签
                updateLike()

                recommendAdapter.submitList(recommendList)
                val length = recommendList.size
                if (length > 0) {
                    val listWidth = binding.recyclerRecommend.width
                    val spacing = (22 * resources.displayMetrics.density).toInt()
                    var avatarSize = (listWidth - spacing * (length - 1)) / length
                    var maxAvatarSize = (listWidth - spacing * 3) / 4
                    recommendAdapter.setAvatarSize(
                        avatarSize.coerceAtMost(maxAvatarSize)
                    )
                    binding.lineRecommendTitle.visibility = View.VISIBLE
                } else {
                    binding.lineRecommendTitle.visibility = View.GONE
                }
                Timber.d("dsc--getCallResult success: $result")
            },
            onFail = {
                dismiss()
            }
        )
    }

    private fun updateLike() {
        isLikeSelected = true
        isDislikeSelected = false
        binding.likeImg.setColorFilter("#FFFF40B0".toColorInt())
        binding.dislikeImg.clearColorFilter()
        selectedTags = emptyList()
        updateLikeDislikeUI()
        updateImpressionTags(tagList)
        updateNextBtnState()
        Timber.d("dsc--Like clicked")
    }

    private fun updateUnlike() {
        isLikeSelected = false
        binding.likeImg.clearColorFilter()
        isDislikeSelected = true
        binding.dislikeImg.setColorFilter("#FFFF40B0".toColorInt())
        selectedTags = emptyList()
        updateLikeDislikeUI()
        updateImpressionTags(badTagList)
        updateNextBtnState()
        Timber.d("dsc--Dislike clicked")
    }

    private fun submitEvaluateInBackground() {
        lifecycleScope.launch {
            try {
                RetrofitUtils.dataRepository.evaluateBroadcaster(
                    BroadcasterEvaluateRequest(
                        channelName = channelName,
                        score = if (isLikeSelected) 1 else 0,
                        tags = selectedTags
                    )
                )
                Timber.d("dsc--Background submit successful")
            } catch (e: Exception) {
                Timber.e("dsc--Background submit failed: ${e.message}")
            }
        }
    }

    private fun getCallResult(
        channelName: String,
        onSuccess: (CallResult) -> Unit = {},
        onFail: (String?) -> Unit = {}
    ) {
        lifecycleScope.launch {
            try {
                // 假设CallApi.getCallResult为挂起函数，返回CallResult对象
                val result =
                    RetrofitUtils.dataRepository.getCallResult(CallResultRequest(channelName))
                lifecycleScope.launch(Dispatchers.Main) {
                    if (result is NetworkResult.Success) {
                        if (result.data != null) {
                            onSuccess.invoke(result.data)
                        }
                    } else {
                        onFail.invoke(
                            if (result is NetworkResult.Error) result.message
                            else "unknown error"
                        )
                    }
                }
            } catch (e: Exception) {
                lifecycleScope.launch(Dispatchers.Main) {
                    onFail.invoke("获取评价数据失败: ${e.message}")
                    Timber.e("dsc--getCallResult error: ${e.message}")
                }
            }
        }
    }

    private fun updateLikeDislikeUI() {
        binding.likeBtn.isSelected = isLikeSelected
        binding.dislikeBtn.isSelected = isDislikeSelected
    }

    private fun updateImpressionTags(tags: List<String>?) {
        val flexbox = binding.recyclerImpression
        flexbox.removeAllViews()
        val context = flexbox.context
        val tagList = tags ?: emptyList()
        for ((index, tag) in tagList.withIndex()) {
            val tv = LayoutInflater.from(context)
                .inflate(R.layout.item_impression_tag, flexbox, false) as android.widget.TextView
            tv.text = tag
            tv.isSelected = selectedTags.contains(tag)
            tv.click {
                if (selectedTags.contains(tag)) {
                    selectedTags = selectedTags.filter { it != tag }
                } else {
                    // 判断是否超过3个
                    if (selectedTags.size >= 3) {
                        ToastUtils.showToast(getString(R.string.you_can_only_choose_3))
                        return@click
                    }
                    selectedTags = selectedTags + tag
                }
                updateImpressionTags(tagList)
                updateNextBtnState()
                Timber.d("dsc--ImpressionTag clicked: $tag, isSelected=${tv.isSelected}")
            }
            val lp = tv.layoutParams as? ViewGroup.MarginLayoutParams
            lp?.setMargins(5, 5, 10, 20)
            tv.layoutParams = lp
            flexbox.addView(tv)
        }
    }

    private fun isNextEnabled(): Boolean {
        return (isLikeSelected || isDislikeSelected)
    }

    private fun updateNextBtnState() {
        val enabled = isNextEnabled()
        // 按钮始终可点击（只要选择了喜欢或不喜欢）
        binding.nextBtn.isEnabled = enabled

        if (enabled) {
            if (selectedTags.isEmpty()) {
                binding.nextBtn.setTextColor(
                    ContextCompat.getColor(
                        requireContext(),
                        R.color.gray_ccc
                    )
                )
                // 未选择标签时显示置灰背景
                GlobalManager.setViewRoundBackground(binding.nextBtn, "#F3F5FA".toColorInt())
            } else {
                binding.nextBtn.setTextColor(
                    ContextCompat.getColor(
                        requireContext(),
                        R.color.black
                    )
                )
                // 选择了标签时显示正常背景
                binding.nextBtn.doOnPreDraw {
                    binding.nextBtn.background = DrawableUtils.createGradientDrawable(
                        colors = GlobalManager.getMainButtonBgGradientColors(),
                        radius = binding.nextBtn.height / 2f
                    )
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener {
            val bottomSheet =
                (dialog as? BottomSheetDialog)?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.setBackgroundResource(android.R.color.transparent)

            // 设置BottomSheet为完全展开状态，禁用拖拽
            val bottomSheetBehavior = BottomSheetBehavior.from(bottomSheet!!)
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            bottomSheetBehavior.isDraggable = false
            bottomSheetBehavior.isHideable = false
        }
        return dialog
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDismiss.invoke(dialog)
    }

    inner class RecommendUserAdapter : RecyclerView.Adapter<RecommendUserAdapter.UserViewHolder>() {
        private var users: List<RecommendedBroadcaster> = emptyList()
        var size: Int = DisplayUtils.dp2pxInternal(60f)

        fun submitList(newUsers: List<RecommendedBroadcaster>) {
            users = newUsers
            notifyDataSetChanged()
        }

        fun setAvatarSize(size: Int) {
            this.size = size
            notifyDataSetChanged()
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_recommend_user, parent, false)
            return UserViewHolder(view)
        }

        override fun onBindViewHolder(holder: UserViewHolder, position: Int) {
            val user = users[position]
            GlideUtils.load(
                holder.avatar,
                user.avatarThumbUrl,
                placeholder = R.drawable.placeholder
            )

            holder.avatar.layoutParams.width = size
            holder.avatar.layoutParams.height = size
            holder.avatar.click {
                user.broadcasterId?.let {
                    UserInfoManager.putCachedDrawable(it, holder.avatar.drawable)
                }

                val intent = Intent(holder.avatar.context, BroadcasterDetailActivity::class.java)
                intent.putExtra(Constant.BROADCASTER_MODEL, user.toBroadcasterModel())
                holder.avatar.context.startActivity(intent)

                if (context is Activity) {
                    (context as Activity).finish()
                }
            }
            holder.avatar.requestLayout()
        }

        override fun getItemCount(): Int = users.size

        inner class UserViewHolder(view: View) :
            RecyclerView.ViewHolder(view) {
            val avatar: android.widget.ImageView = view.findViewById(R.id.avatar)
        }
    }
}