package com.score.callmetest.ui.message.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.CallStatus
import com.score.callmetest.CallType
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemCallHistoryBinding
import com.score.callmetest.databinding.ItemListBottomBinding
import com.score.callmetest.entity.CallHistoryEntity
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.GlideUtils


/**
 * 通话历史记录列表适配器
 * 实现消息列表的展示和长按菜单功能
 */
class CallHistoryAdapter : ListAdapter<CallHistoryEntity, RecyclerView.ViewHolder>(DiffCallback()) {

    companion object {
        private const val VIEW_TYPE_CALL_HISTORY = 0
        private const val VIEW_TYPE_BOTTOM = 1

        private const val PAY_LOAD_ONLINE_STATUS = 0x101
    }



    private var onCallItemClickListener: OnCallItemClickListener? = null

    /**
     * 设置通话记录点击监听器
     */
    fun setOnCallItemClickListener(listener: OnCallItemClickListener) {
        this.onCallItemClickListener = listener
    }

    override fun getItemViewType(position: Int): Int {
        val item = getItem(position)
        return if (item is CallHistoryEntity && !item.isBottomView) {
            VIEW_TYPE_CALL_HISTORY
        } else {
            VIEW_TYPE_BOTTOM
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            VIEW_TYPE_CALL_HISTORY -> {
                val binding = ItemCallHistoryBinding.inflate(
                    layoutInflater, parent, false
                )
                CallHistoryViewHolder(binding)
            }
            VIEW_TYPE_BOTTOM -> {
                val binding = ItemListBottomBinding.inflate(
                    layoutInflater, parent, false
                )
                BottomViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
        payloads: List<Any?>
    ) {
        if (holder is MsgListAdapter.BottomViewHolder) {
            super.onBindViewHolder(holder, position, payloads)
            return
        }
        if (payloads.isEmpty()) {
            super.onBindViewHolder(holder, position, payloads)
            return
        }
        val payLoad = payloads[0]
        val obj = getItem(position)
        if (holder is CallHistoryViewHolder) {
            // 刷新
            if (payLoad == PAY_LOAD_ONLINE_STATUS) {
                // 更新状态
                holder.updateOnlineStatus(obj.onlineStatus)
                holder.updateVideoIndicatorVisibility(obj.userId,obj.onlineStatus)
                return
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is CallHistoryViewHolder -> {
                val callHistory = getItem(position) as CallHistoryEntity
                holder.bind(callHistory, position)
            }
            is BottomViewHolder -> {
                holder.bind("Bottom")
            }
        }
    }


    /**
     * 释放资源
     */
    fun release() {
        this.onCallItemClickListener = null
    }

    /**
     * 通话记录ViewHolder
     */
    inner class CallHistoryViewHolder(private val binding: ItemCallHistoryBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(callHistory: CallHistoryEntity, position: Int) {
            binding.apply {
                // 设置头像
                val userInfo = UserInfoManager.getCachedUserInfo(callHistory.userId)
                val urls = buildList {
                    add(userInfo?.avatarThumbUrl)
                    add(userInfo?.avatarMiddleThumbUrl)
                    add(userInfo?.avatar)
                    add(callHistory.avatar)
                }
                GlideUtils.loadWithFallbackList(
                    view = ivAvatar,
                    uriList = urls,
                    error = R.drawable.placeholder
                )

                // 设置用户名
                tvUsername.text = callHistory.userName

                // 获取显示用的通话类型
                val displayCallType = callHistory.callType

                // 设置通话状态图标
                val callTypeIcon = when (displayCallType) {
                    CallType.INCOMING_CALL -> R.drawable.call_connected
                    CallType.CANCELLED_CALL -> R.drawable.call_disconnected
                    CallType.UNANSWERED_CALL -> R.drawable.call_disconnected
                    CallType.REJECTED_CALL -> R.drawable.call_disconnected
                    CallType.MISSED_CALL -> R.drawable.call_disconnected
                    else -> null
                }

                if (callTypeIcon != null) {
                    ivCallType.setImageResource(callTypeIcon)
                }

                // 设置通话类型和时长
                val callTypeText = if (displayCallType == CallType.INCOMING_CALL && callHistory.callDuration > 0) {
                    // 对于INCOMING_CALL类型，显示时间
                    "$displayCallType ${callHistory.getCallDurationText()}"
                } else {
                    displayCallType
                }
                if (displayCallType == CallType.OUTGOING_CALL || displayCallType == CallType.INCOMING_CALL) {
                    tvCallType.setTextColor(ContextCompat.getColor(itemView.context, R.color.black))
                } else {
                    tvCallType.setTextColor(ContextCompat.getColor(itemView.context, R.color.msg_busy))
                }
                tvCallType.text = callTypeText

                // 设置状态指示器颜色
                setupOnlineStatus(callHistory.onlineStatus)

                // 设置视频通话标识
                // 优先使用实时状态缓存，避免显示过期的数据库状态
                updateVideoIndicatorVisibility( callHistory.userId, callHistory.onlineStatus)

                // 设置时间戳
                tvTimestamp.text = formatCallEndTime(callHistory.callEndTime)
                
                // 设置点击和长按事件
                setupClickListeners(callHistory, position)
            }
        }
        private fun setupOnlineStatus(status: String) {
            val statusBackground = when (status) {
                CallStatus.ONLINE,CallStatus.AVAILABLE -> R.drawable.shape_status_online
                CallStatus.BUSY -> R.drawable.shape_status_busy
                CallStatus.IN_CALL -> R.drawable.shape_status_in_call
                CallStatus.OFFLINE -> R.drawable.shape_status_offline
                CallStatus.UNKNOWN -> null
                else -> null
            }

            statusBackground?.let {
                binding.statusIndicator.setBackgroundResource(it)
                binding.statusIndicator.visibility = View.VISIBLE
            }?: {
                binding.statusIndicator.visibility = View.GONE
            }
        }

        fun updateOnlineStatus(status: String) {
            setupOnlineStatus(status)
        }

        /**
         * 更新视频图标的显示状态
         * 默认显示btn_message图标，只有当在线状态为ONLINE时才显示call_video图标
         */
        fun updateVideoIndicatorVisibility(userId: String, fallbackStatus: String) {
            // 始终显示视频图标
            binding.ivVideoIndicator.visibility = View.VISIBLE

            var actualStatus = fallbackStatus

            // 适配审核模式
            if (StrategyManager.isReviewPkg()) {
                val cacheUserInfo = UserInfoManager.getCachedUserInfo(userId)
                actualStatus = if (cacheUserInfo?.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(userId)) {
                    CallStatus.ONLINE
                }else {
                    GlobalManager.getReviewOtherStatus(userId)
                }
            }

            // 根据在线状态设置不同的图标
            if (actualStatus == CallStatus.ONLINE || actualStatus == CallStatus.AVAILABLE) {
                binding.ivVideoIndicator.setImageResource(R.drawable.call_video)
            } else {
                binding.ivVideoIndicator.setImageResource(R.drawable.btn_message)
            }
        }
        private fun setupClickListeners(callHistory: CallHistoryEntity, position: Int) {
            // 头像点击
            ClickUtils.setOnGlobalDebounceClickListener(binding.ivAvatar) {
                UserInfoManager.putCachedDrawable(
                    callHistory.userId,
                    binding.ivAvatar.drawable
                )
                onCallItemClickListener?.onItemAvatarClick(position, callHistory)
            }

            // 视频通话图标点击
            ClickUtils.setOnGlobalDebounceClickListener(binding.ivVideoIndicator) {
                onCallItemClickListener?.onVideoCallClick(position, callHistory)
            }
            binding.root.apply {
                // 点击事件
                ClickUtils.setOnGlobalDebounceClickListener(this) {
                    UserInfoManager.putCachedDrawable(
                        callHistory.userId,
                        binding.ivAvatar.drawable
                    )
                    onCallItemClickListener?.onItemClick(position, callHistory)
                }
            }
        }



        /**
         * 格式化通话结束时间
         * 通话结束时间，显示yyyy.MM.dd
         */
        private fun formatCallEndTime(timeInMillis: Long): String {
            return android.text.format.DateFormat.format("yyyy.MM.dd", timeInMillis).toString()
        }
    }

    /**
     * 底部ViewHolder
     */
    inner class BottomViewHolder(private val binding: ItemListBottomBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(text: String) {
            binding.tvBottomText.text = "Bottom"
        }
    }

    /**
     * DiffCallback
     */
    class DiffCallback : DiffUtil.ItemCallback<CallHistoryEntity>() {
        override fun areItemsTheSame(oldItem: CallHistoryEntity, newItem: CallHistoryEntity): Boolean {
            return oldItem.userId == newItem.userId
        }

        override fun areContentsTheSame(oldItem: CallHistoryEntity, newItem: CallHistoryEntity): Boolean {
            return oldItem == newItem
        }

        override fun getChangePayload(
            oldItem: CallHistoryEntity,
            newItem: CallHistoryEntity
        ): Any? {
            if (oldItem.isBottomView || newItem.isBottomView) {
                return null
            }
            if (oldItem.onlineStatus != newItem.onlineStatus) {
                return PAY_LOAD_ONLINE_STATUS
            }

            return null
        }
    }

    /**
     * 通话记录点击监听器
     */
    interface OnCallItemClickListener {
        fun onItemClick(position: Int, callHistory: CallHistoryEntity)
        fun onItemAvatarClick(position: Int, callHistory: CallHistoryEntity)
        fun onVideoCallClick(position: Int, callHistory: CallHistoryEntity)
    }
}