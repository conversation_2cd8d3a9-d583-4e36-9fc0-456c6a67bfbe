package com.score.callmetest.ui.widget.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemPaymentMethodBinding
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.ui.widget.PaymentMethodItem
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click

class PaymentMethodAdapter(
    private var paymentMethods: List<PaymentMethodItem>,
    private val goodsInfo: GoodsInfo,
    private val onItemClick: (Int,String) -> Unit
) : RecyclerView.Adapter<PaymentMethodAdapter.ViewHolder>() {

    class ViewHolder(val binding: ItemPaymentMethodBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemPaymentMethodBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = paymentMethods[position]
        
        holder.binding.apply {
//            ivIcon.setImageResource(item.iconRes)
            GlideUtils.load(ivIcon, item.iconUrl)

            tvName.text = item.displayName

            // recommend && lastUsed
            val showTag = item.isRecommend || item.isLastUsed
            tvTag.isVisible = showTag
            if(item.isRecommend){
                tvTag.text = CustomUtils.getString(R.string.str_recommend)
            }
            if(item.isLastUsed){
                tvTag.text = CustomUtils.getString(R.string.str_recent_use)
            }

            // 优惠信息
            if (item.discount > 0) {
                val discountStr = "+${item.discount}% ${CustomUtils.getString(R.string.more_coins)}"
                tvDiscount.text = discountStr
                tvBtDiscount.text = discountStr
                tvDiscount.isVisible = !showTag
                ivDiscount.isVisible = !showTag
                llItemPaymentMethodMore.isVisible = showTag
            } else {
                tvDiscount.isVisible = false
                ivDiscount.isVisible = false
                llItemPaymentMethodMore.isVisible = false
            }

            // 选中状态
            ivSelected.isSelected = item.isSelected
            itemLayout.isSelected = item.isSelected

            itemLayout.click {
                onItemClick(position,item.payChannel)
            }
        }
    }

    override fun getItemCount(): Int = paymentMethods.size

    fun updateData(newPaymentMethods: List<PaymentMethodItem>) {
        paymentMethods = newPaymentMethods
        notifyDataSetChanged()
    }
} 