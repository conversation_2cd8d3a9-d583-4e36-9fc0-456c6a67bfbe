package com.score.callmetest.ui.home.ranklist

import android.view.View
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityRankListBinding
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.LoadingUtils
import timber.log.Timber

/**
 * 排行榜Activity
 */
class RankListActivity : BaseActivity<ActivityRankListBinding, RankListViewModel>() {

    private lateinit var mPagerAdapter: RankPagerAdapter

    override fun getViewBinding(): ActivityRankListBinding {
        return ActivityRankListBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass(): Class<RankListViewModel> {
        return RankListViewModel::class.java
    }

    override fun initView() {
        super.initView()
        // 显示加载状态
        LoadingUtils.showLoading(this)
        
        // 为TabLayout添加状态栏顶部间距
        GlobalManager.addViewStatusBarTopMargin(this, binding.tabRanking)
        
        setupViewPager()
        setupTabs()
    }

    override fun initData() {
        super.initData()
        
        // 监听所有排行榜数据的加载状态
        viewModel.isLoading.observe(this) { isLoading ->
            if (!isLoading) {
                // 检查是否所有数据都已加载完成
                checkAllDataLoaded()
            }
        }
        
        // 监听错误信息
        viewModel.errorMessage.observe(this) { errorMessage ->
            errorMessage?.let { error ->
                if (error.second == 0) {
                    LoadingUtils.dismissLoading()
                }

            }
        }
        // 开始加载所有排行榜数据
        loadAllRankData()
    }

    /**
     * 加载所有排行榜数据
     */
    private fun loadAllRankData() {
        viewModel.loadUserRankData()
        viewModel.loadUserCoupleRankData()
        viewModel.loadBroadcasterRankData()
    }

    /**
     * 检查所有数据是否都已加载完成
     */
    private fun checkAllDataLoaded() {
        val userRankData = viewModel.userRankData.value
        val userCoupleRankData = viewModel.userCoupleRankData.value
        val broadcasterRankData = viewModel.broadcasterRankData.value
        
        // 如果所有数据都已加载完成，隐藏加载状态
        if (userRankData != null && userCoupleRankData != null && broadcasterRankData != null) {
            LoadingUtils.dismissLoading()
        }
    }

    override fun initListener() {
        super.initListener()
        
        // 返回按钮点击事件
        ClickUtils.setOnIsolatedClickListener(binding.btnReturn) {
            finish()
        }
        
        // Tab切换监听
        binding.tabRanking.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                updateTabStyle(tab.customView, true)
                binding.viewPager.currentItem = tab.position
                // 切换背景
                updateBackground(tab.position)
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                updateTabStyle(tab.customView, false)
            }

            override fun onTabReselected(tab: TabLayout.Tab) {
                // 重复选择时不处理
            }
        })

        // ViewPager页面切换监听
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                binding.tabRanking.getTabAt(position)?.select()
                // 切换背景
                updateBackground(position)
            }
        })
    }

    /**
     * 设置ViewPager
     */
    private fun setupViewPager() {
        mPagerAdapter = RankPagerAdapter(this)
        
        binding.viewPager.apply {
            adapter = mPagerAdapter
            offscreenPageLimit = 3 // 现在有三个Tab
        }
        
        GlobalManager.setNeverOverScroll(binding.viewPager)
    }

    /**
     * 设置Tab
     */
    private fun setupTabs() {
        // 禁用Tab涟漪效果
        binding.tabRanking.tabRippleColor = null
        // 添加主播榜Tab
        val charmTab = binding.tabRanking.newTab()
        charmTab.customView = createCustomTabView(getString(R.string.charm))
        binding.tabRanking.addTab(charmTab)

        // 添加用户榜Tab
        val richTab = binding.tabRanking.newTab()
        richTab.customView = createCustomTabView(getString(R.string.rich))
        binding.tabRanking.addTab(richTab)

        // 添加cp榜Tab
        val coupleTab = binding.tabRanking.newTab()
        coupleTab.customView = createCustomTabView(getString(R.string.couple))
        binding.tabRanking.addTab(coupleTab)

        // 设置默认选中第一个Tab
        updateTabStyle(charmTab.customView, true)
        
        // 设置初始背景（用户榜背景）
        updateBackground(0)
    }



    /**
     * 创建自定义Tab视图
     */
    private fun createCustomTabView(text: String): View {
        val view = layoutInflater.inflate(R.layout.custom_tab_item, null)
        val textView = view.findViewById<TextView>(R.id.tab_text)
        textView.text = text
        return view
    }

    /**
     * 更新Tab样式
     */
    private fun updateTabStyle(view: View?, selected: Boolean) {
        if (view == null) return
        val indicator = view.findViewById<View>(R.id.tab_indicator)

        if (selected) {
            // 选中状态：显示指示器
            indicator?.visibility = View.VISIBLE
        } else {
            // 未选中状态：隐藏指示器
            indicator?.visibility = View.GONE
        }
    }

    /**
     * 根据Tab位置更新背景
     */
    private fun updateBackground(position: Int) {
        try {
            val backgroundResource = when (position) {
                0 -> R.drawable.bg_rank_charm     // 主播榜
                1 -> R.drawable.bg_rank_rich    // 用户榜
                2 -> R.drawable.bg_rank_couple     // cp榜
                else -> R.drawable.bg_rank_rich   // 默认用户榜背景
            }
            
            // 更新根布局背景
            binding.root.setBackgroundResource(backgroundResource)

        } catch (e: Exception) {
            Timber.tag("dsc--").e(e, "切换背景失败 position=$position")
        }
    }

    /**
     * ViewPager适配器
     */
    private inner class RankPagerAdapter(activity: RankListActivity) : FragmentStateAdapter(activity) {
        override fun getItemCount(): Int = 3 // 用户榜、主播榜和cp榜

        override fun createFragment(position: Int): Fragment {
            return when (position) {
                0 -> CharmListFragment()
                1 -> RichListFragment()
                2 -> CoupleListFragment()
                else -> throw IllegalArgumentException("Invalid position: $position")
            }
        }
    }
}