package com.score.callmetest.ui.widget.checkin

import android.content.Context
import androidx.recyclerview.widget.GridLayoutManager

/**
 * 签到网格布局管理器
 * 确保最后一个Item填满剩余宽度
 */
class CheckInGridLayoutManager(
    context: Context,
    spanCount: Int
) : GridLayoutManager(context, spanCount) {

    init {
        spanSizeLookup = object : SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                val totalItems = itemCount
                val lastRowStartPosition = ((totalItems - 1) / spanCount) * spanCount
                
                return when {
                    // 如果是最后一行的最后一个Item，占满剩余空间
                    position == totalItems - 1 && position >= lastRowStartPosition -> {
                        val remainingSpans = spanCount - (totalItems - lastRowStartPosition - 1)
                        remainingSpans
                    }
                    // 其他Item正常占用1个span
                    else -> 1
                }
            }
        }
    }
}
