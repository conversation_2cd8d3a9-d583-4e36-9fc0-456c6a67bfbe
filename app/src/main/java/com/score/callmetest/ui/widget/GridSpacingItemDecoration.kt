package com.score.callmetest.ui.widget

import android.graphics.Rect
import android.view.View
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView

class GridSpacingItemDecoration(
    private val spanCount: Int,
    private val spacing: Int,
    private val includeEdge: Boolean = true
) : RecyclerView.ItemDecoration() {
    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view) // item position
        val column = position % spanCount // item column
        val isRtl = ViewCompat.getLayoutDirection(parent) == ViewCompat.LAYOUT_DIRECTION_RTL

        if (includeEdge) {
            val leftSpacing = spacing - column * spacing / spanCount
            val rightSpacing = (column + 1) * spacing / spanCount
            
            if (isRtl) {
                outRect.left = rightSpacing
                outRect.right = leftSpacing
            } else {
                outRect.left = leftSpacing
                outRect.right = rightSpacing
            }

            if (position < spanCount) { // top edge
                outRect.top = spacing
            }
            outRect.bottom = spacing // item bottom
        } else {
            val leftSpacing = column * spacing / spanCount
            val rightSpacing = spacing - (column + 1) * spacing / spanCount
            
            if (isRtl) {
                outRect.left = rightSpacing
                outRect.right = leftSpacing
            } else {
                outRect.left = leftSpacing
                outRect.right = rightSpacing
            }
            
            if (position >= spanCount) {
                outRect.top = spacing // item top
            }
        }
    }
}