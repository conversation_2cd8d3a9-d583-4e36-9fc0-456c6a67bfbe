package com.score.callmetest.ui.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.graphics.toColorInt
import com.score.callmetest.R
import com.score.callmetest.databinding.ViewVipItemCardBinding
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.network.GoodsInfo

/**
 * VIP订阅卡片自定义控件
 * 支持设置标题、图标、奖励数量、价格、每周价格和背景样式
 */
@SuppressLint("SetTextI18n")
class VipItemCardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val binding: ViewVipItemCardBinding
    private var mOnCardClickListener: OnCardClickListener? = null
    private var mVipItemData: GoodsInfo? = null
    private var iconRes: Int = 0
    private var currentStyle = Style.LIGHT
    private var goodsName: String? = null

    init {
        binding = ViewVipItemCardBinding.inflate(LayoutInflater.from(context), this, true)
        setupClickListeners()
    }

    private fun setupClickListeners() {
        binding.cardContent.setOnClickListener {
            mOnCardClickListener?.onCardClick(mVipItemData)
        }
    }

    /**
     * 设置VIP卡片数据
     */
    fun setData(data: GoodsInfo, iconRes: Int, style: Style = Style.LIGHT): VipItemCardView {
        mVipItemData = data
        this.iconRes = iconRes
        this.currentStyle = style
        updateUI(style)
        return this
    }

    fun getData(): GoodsInfo? {
        return mVipItemData
    }
    fun getGoodsName(): String? {
        return goodsName
    }

    /**
     * 更新UI显示
     */
    fun updateUI(style: Style = Style.LIGHT) {
        this.currentStyle = style
        mVipItemData?.let { data ->
            binding.tvTitle.text = "${data.validity} ${data.validityUnit}"
            binding.tvBonus.text = "${data.exchangeCoin}"
            val (priceSymbol, price) = GoodsManager.getLocaleGoodsPrice(data, false)
            binding.tvPrice.text = "$priceSymbol $price"
            val (weekPriceSymbol, weekPrice) = GoodsManager.getLocaleGoodsPrice(data, false, isWeek = true)
            binding.tvWeeklyPrice.text = "$weekPriceSymbol $weekPrice/week"
            goodsName = price + priceSymbol + "-" +data.exchangeCoin
            // 设置图标
            if (iconRes != 0) {
                binding.ivIcon.setImageResource(iconRes)
            }

            when(style) {
                Style.LIGHT -> {
                    binding.cardContent.setBackgroundResource(R.drawable.vip_dialog_normal_bg)
                    binding.tvTitle.setTextColor(Color.BLACK)
                    binding.tvBonus.setTextColor(Color.BLACK)
                    binding.tvPrice.setTextColor("#FF8A7D73".toColorInt())
                    binding.tvWeeklyPrice.setTextColor("#FF7A6969".toColorInt())
                }
                Style.DARK -> {
                    binding.cardContent.setBackgroundResource(R.drawable.vip_dialog_selected_bg)
                    binding.tvTitle.setTextColor(Color.WHITE)
                    binding.tvBonus.setTextColor("#FFFFE51D".toColorInt())
                    binding.tvPrice.setTextColor(Color.WHITE)
                    binding.tvWeeklyPrice.setTextColor(resources.getColor(android.R.color.white, null))
                }
            }
        }
    }

    fun getStyle(): Style {
        return currentStyle
    }

    /**
     * 设置卡片点击监听器
     */
    fun setOnCardClickListener(listener: OnCardClickListener) {
        mOnCardClickListener = listener
    }

    enum class Style {
        LIGHT,
        DARK
    }

    /**
     * 卡片点击监听器接口
     */
    interface OnCardClickListener {
        fun onCardClick(data: GoodsInfo?)
    }
}
