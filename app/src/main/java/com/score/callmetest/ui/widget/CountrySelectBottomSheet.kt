package com.score.callmetest.ui.widget

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.R
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click

class CountrySelectBottomSheet(
    private val context: Context,
    private val selectedCountry: String?,
    private val onCountrySelected: (String) -> Unit
) : BottomSheetDialogFragment() {

    private lateinit var rvCountry: RecyclerView
    private lateinit var cancelBtn: ImageView
    // 国家列表 - 使用开源库数据，延迟获取以确保数据已初始化
    private val countryList: List<CountryUtils.Country> by lazy {
        CountryUtils.getCountriesFromLibrary()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        // 禁用默认背景
        dialog.setOnShowListener {
            val bottomSheet = dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.setBackgroundResource(android.R.color.transparent)
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.bottom_sheet_country_select, container, false)

        rvCountry = view.findViewById(R.id.rv_country)
        cancelBtn = view.findViewById(R.id.image_cancel)
        rvCountry.layoutManager = LinearLayoutManager(context)
        val adapter = CountryAdapter(countryList, selectedCountry) { countryName ->
            onCountrySelected(countryName)
            dismiss()
        }
        rvCountry.adapter = adapter
        cancelBtn.click { dismiss() }
        
        // 如果有选中的国家，滚动到该位置并居中显示
        selectedCountry?.let { country ->
            val selectedIndex = countryList.indexOfFirst { it.enName == country }
            if (selectedIndex >= 0) {
                // 延迟执行，确保RecyclerView已经完成布局
                rvCountry.post {
                    val layoutManager = rvCountry.layoutManager as? LinearLayoutManager
                    // 计算RecyclerView高度的一半，作为居中偏移量
                    val recyclerViewHeight = rvCountry.height
                    val itemHeight = DisplayUtils.dp2pxInternal(60f) + DisplayUtils.dp2pxInternal(10f) // item高度 + margin
                    val centerOffset = recyclerViewHeight / 2 - itemHeight / 2
                    
                    // 使用scrollToPositionWithOffset让选中项居中显示
                    layoutManager?.scrollToPositionWithOffset(selectedIndex, centerOffset)
                }
            }
        }

        // 设置RecyclerView底部padding，避免被导航栏遮挡
        rvCountry.setPadding(
            rvCountry.paddingLeft,
            rvCountry.paddingTop,
            rvCountry.paddingRight,
            rvCountry.paddingBottom + DisplayUtils.dp2pxInternal(10f)
        )
        rvCountry.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: android.graphics.Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                val margin = DisplayUtils.dp2pxInternal(5f)
                val lastMargin = DisplayUtils.dp2pxInternal(20f)
                val position = parent.getChildAdapterPosition(view)
                val isLast = position == (parent.adapter?.itemCount?.minus(1) ?: -1)
                outRect.top = margin
                outRect.bottom = if (isLast) lastMargin else margin
            }
        })
        return view
    }

    override fun onStart() {
        super.onStart()
        val dialog = dialog as? com.google.android.material.bottomsheet.BottomSheetDialog
        val bottomSheet = dialog?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
        //禁止弹窗滑动
        bottomSheet?.let {
            val behavior = com.google.android.material.bottomsheet.BottomSheetBehavior.from(it)
            behavior.state = com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_EXPANDED
            behavior.isDraggable = false // 禁止拖拽
            behavior.skipCollapsed = true // 跳过折叠状态
            behavior.isHideable = true  // 禁止下拉关闭
            
       /*     // 设置弹窗距离顶部50dp
            val topMargin = DisplayUtils.dp2pxInternal(50f)
            val layoutParams = it.layoutParams as? android.view.ViewGroup.MarginLayoutParams
            layoutParams?.topMargin = topMargin
            it.layoutParams = layoutParams*/
        }
    }

    class CountryAdapter(
        private val countries: List<CountryUtils.Country>,
        private val selectedCountry: String?,
        private val onItemClick: (String) -> Unit
    ) : RecyclerView.Adapter<CountryAdapter.CountryViewHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CountryViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_bottom_sheet_country_select, parent, false)
            return CountryViewHolder(view)
        }

        override fun onBindViewHolder(holder: CountryViewHolder, position: Int) {
            val country = countries[position]
            holder.tvCountryName.text = country.enName
            // 设置国家图标，如果没有图标则使用默认图标
            val iconRes = country.iconRes ?: R.drawable.map_language
            holder.ivCountryIcon.setImageResource(iconRes)
            val isSelected = country.enName == selectedCountry
            holder.itemView.isSelected = isSelected
            holder.itemView.click { onItemClick(country.enName) }
            // 根据选中状态设置不同背景
            if (isSelected) {
                holder.itemView.background = DrawableUtils.createRoundRectDrawableWithStrokeDp(
                    fillColor = "#FFFFE6F6".toColorInt(),
                    radiusDp = 30f,
                    strokeColor = "#FFFFBFE5".toColorInt(),
                    strokeWidth = DisplayUtils.dp2pxInternal(1f)
                )
            } else {
                holder.itemView.background = DrawableUtils.createRoundRectDrawable(
                   color = "#FFF3F5FA".toColorInt(),
                   radius = DisplayUtils.dp2pxInternalFloat(30f)
                )
            }
        }

        override fun getItemCount(): Int = countries.size

        class CountryViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val ivCountryIcon: ImageView = itemView.findViewById(R.id.iv_country_icon)
            val tvCountryName: TextView = itemView.findViewById(R.id.tv_flag_country_name)
        }
    }
}
