package com.score.callmetest.ui.widget

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.databinding.DialogPaymentMethodSelectBinding
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.manager.PaymentMethodManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.network.PayChannelItem
import com.score.callmetest.ui.widget.adapter.PaymentMethodAdapter
import com.score.callmetest.ui.widget.decoration.SpaceVerticalItemDecoration
import com.score.callmetest.util.DialogUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PaymentMethodDialog(
    private val goodsInfo: GoodsInfo,
    private val onPaymentMethodSelected: (String, Int) -> Unit,
    private val onDialogClosed: (() -> Unit)? = null,
    private val onDialogClosedWithoutPayment: (() -> Unit)? = null
) : BottomSheetDialogFragment() {

    private var _binding: DialogPaymentMethodSelectBinding? = null
    private val binding get() = _binding!!
    private lateinit var adapter: PaymentMethodAdapter
    private val mPaymentMethodList = mutableListOf<PaymentMethodItem>()
    private var mSelectedPosition = -1
    private var mIsPaymentSelected = false // 标记用户是否选择了支付方式
    private var coinSum: Int = 0;

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        _binding = DialogPaymentMethodSelectBinding.inflate(inflater, container, false)
        binding.bottomShadow.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#00FFFFFF".toColorInt(), "#FFFFFF".toColorInt()),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM,
        )
        setupRecyclerView()
        setupButtons()
        setupProductInfo()
        loadPaymentMethods()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // 通过禁用嵌套滚动和设置 BottomSheet 行为来防止拖拽
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        if (mIsPaymentSelected) {
            // 用户选择了支付方式，调用正常关闭回调
            onDialogClosed?.invoke()
        } else {
            // 用户直接关闭弹窗，调用无支付关闭回调
            onDialogClosedWithoutPayment?.invoke()
        }
    }

    private fun setupRecyclerView() {
        binding.rvPaymentMethods.layoutManager = LinearLayoutManager(requireContext())
        binding.rvPaymentMethods.addItemDecoration(SpaceVerticalItemDecoration(DisplayUtils.dp2px(10f)))
        adapter = PaymentMethodAdapter(mPaymentMethodList, goodsInfo) { position,payChannel ->
           /* if (payChannel == PaymentMethodManager.PAY_CHANNEL_GP) {
                // 可在此处直接处理 GP 支付逻辑，或通过回调让外部处理
                onPaymentMethodSelected("GP", coinSum)
            } else {
                onPaymentMethodSelected(payChannel, coinSum)
            }*/
            if(mSelectedPosition == position) return@PaymentMethodAdapter
            if(mSelectedPosition != -1) {
                mPaymentMethodList[mSelectedPosition].isSelected = false
                adapter.notifyItemChanged(mSelectedPosition)
            }
            mSelectedPosition = position
            mPaymentMethodList[position].isSelected = true
            adapter.notifyItemChanged(position)
            updateCoinMore(position)
        }
        binding.rvPaymentMethods.adapter = adapter
    }

    private fun setupButtons() {
        binding.btnPay.click {
            mIsPaymentSelected = true // 标记用户选择了支付方式
            DialogUtils.safeDismissDialogFragment(this)
            if(mSelectedPosition == -1 || mSelectedPosition >= mPaymentMethodList.size) return@click
            onPaymentMethodSelected(mPaymentMethodList[mSelectedPosition].payChannel, coinSum)
        }
    }

    private fun setupProductInfo() {
        // 显示商品信息
        val coinAmount = goodsInfo.exchangeCoin ?: 0
        val extraCoin = goodsInfo.extraCoin ?: 0

        binding.tvCoinBalance.text = "$coinAmount"
        binding.tvCoinMore.text = if (extraCoin > 0) "+$extraCoin" else ""
        coinSum =  coinAmount + extraCoin

        val (priceSymbol, price) = GoodsManager.getLocaleGoodsPrice(goodsInfo)
        binding.tvPrice.text = "$priceSymbol$price"
    }

    private fun updateCoinMore(position: Int) {
        if(position < 0 || position > mPaymentMethodList.size) return
        // 当前选中item的优惠
        val selectDiscount = mPaymentMethodList[position].discount
        if((goodsInfo.extraCoinPercent ?: 0) >= selectDiscount){
            // 用默认的
            val extraCoin = goodsInfo.extraCoin ?: 0
            binding.tvCoinMore.text = if (extraCoin > 0) "+$extraCoin" else ""
            return
        }
        // item的优惠力度更大，显示item的
        val coinMore = ((goodsInfo.exchangeCoin ?: 0) * selectDiscount / 100f).toInt()
        binding.tvCoinMore.text = "+$coinMore"
    }

    private fun loadPaymentMethods() {
        // 先获取支付渠道列表，然后计算折扣
        CoroutineScope(Dispatchers.Main).launch {
            val payChannelList = PaymentMethodManager.ensurePayChannelListLoaded()
            val defaultMethod = PaymentMethodManager.getDefaultPaymentMethod()
            val lastUsedMethod = PaymentMethodManager.getLastUsedPaymentMethod()
            val paymentMethodList = mutableListOf<PaymentMethodItem>()
            payChannelList?.forEachIndexed { index,item ->
                val discount = calculateChannelDiscount(item)
                val isSelected = item.payChannel == defaultMethod
                paymentMethodList.add(
                    PaymentMethodItem(
                        payChannel = item.payChannel ?: "",
                        displayName = item.title ?: item.payChannel ?: "",
                        iconRes = -1,
                        iconUrl = item.iconUrl ?: "",
                        isRecommend = item.itemType == 1,
                        isLastUsed = lastUsedMethod == item.payChannel,
                        discount = discount,
                        isSelected = isSelected
                    )
                )
            }
            mPaymentMethodList.addAll(
                paymentMethodList.sortedWith(
                    compareByDescending<PaymentMethodItem> {
                        // 1. 上一次优先
                        it.isLastUsed
                    }
                        .thenByDescending { it.isRecommend }                       // 2. 推荐其次
                )
            )
            mSelectedPosition = mPaymentMethodList.indexOfFirst{it.isSelected}
            adapter.notifyDataSetChanged()
            updateCoinMore(mSelectedPosition)
        }
    }

    /**
     * 根据商品类型和支付渠道计算折扣
     */
    private fun calculateChannelDiscount(payChannel: PayChannelItem?): Int {
        if (payChannel == null) return 0
        return when (goodsInfo.type) {
            "0" -> payChannel.presentCoinRatio ?: 0  // 普通商品
            "1" -> payChannel.promotionPresentCoinRatio ?: 0  // 促销商品
            "2" -> {  // 活动商品：取thirdpartyCoinPercent和promotionPresentCoinRatio的最大值
                val thirdpartyCoinPercent = goodsInfo.thirdpartyCoinPercent ?: 0
                val promotionPresentCoinRatio = payChannel.promotionPresentCoinRatio ?: 0
                maxOf(thirdpartyCoinPercent, promotionPresentCoinRatio)
            }
            "3" -> payChannel.presentCoinRatio ?: 0  // 订阅商品
            else -> payChannel.presentCoinRatio ?: 0  // 默认使用普通商品的比例
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?) = super.onCreateDialog(savedInstanceState).apply {
        setOnShowListener {
            val bottomSheet = (this as? BottomSheetDialog)
                ?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.setBackgroundColor(Color.TRANSPARENT)
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        
        // 设置 BottomSheet 行为，参考 CountrySelectBottomSheet 的稳定配置
        val dialog = dialog as? BottomSheetDialog
        val bottomSheet = dialog?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
        bottomSheet?.let {
            val behavior = com.google.android.material.bottomsheet.BottomSheetBehavior.from(it)
            behavior.state = com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_EXPANDED
            behavior.isDraggable = false // 禁止拖拽
            behavior.skipCollapsed = true // 跳过折叠状态
            behavior.isHideable = true  // 禁止下拉关闭
        }
    }
    
}

data class PaymentMethodItem(
    val payChannel: String,
    val displayName: String,
    val iconRes: Int,
    var iconUrl: String? = null,
    var isRecommend: Boolean = false,
    var isLastUsed: Boolean = false,
    val discount: Int,
    var isSelected: Boolean
)