package com.score.callmetest.ui.home.ranklist.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemRankBinding
import com.score.callmetest.network.UserRankModel
import com.score.callmetest.util.GlideUtils
import timber.log.Timber

/**
 * 富豪榜RecyclerView适配器
 * 负责展示排行榜列表项（从第4名开始）
 */
class RichListAdapter : ListAdapter<UserRankModel, RichListAdapter.RankViewHolder>(RankDiffCallback()) {
    private val  TAG = "RichListAdapter"
    

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RankViewHolder {
        val binding = ItemRankBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return RankViewHolder(binding)
    }

    override fun onBindViewHolder(holder: RankViewHolder, position: Int) {
        val item = getItem(position)
        holder.bind(item, position)
    }
    
    /**
     * 获取实际数据列表（从第4名开始）
     */
    fun submitRankList(rankList: List<UserRankModel>) {
        // 从第4名开始，跳过前三名
        val listFromFourth = if (rankList.size > 3) {
            rankList.subList(3, rankList.size)
        } else {
            emptyList()
        }
        submitList(listFromFourth)
    }

    /**
     * ViewHolder类
     */
    inner class RankViewHolder(private val binding: ItemRankBinding) : RecyclerView.ViewHolder(binding.root) {
        
   /*     init {
            // 设置整个item的点击事件
            itemView.click {}
        }*/
        
        fun bind(userRank: UserRankModel, position: Int) {
            try {
                // 显示排名（从4开始，因为前三名单独显示）
                val rank = userRank.sort ?: (position + 4)
                binding.tvMineRank.text = rank.toString()
                
                // 统一头像加载逻辑
                loadAvatar(userRank)
                
                // 显示用户昵称
                binding.tvMineName.text = userRank.nickname ?: "Unknown"
                
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "排行榜项绑定时出错 position=$position")
                
                // 出错时显示默认值
                binding.tvMineRank.text = (position + 4).toString()
                binding.tvMineName.text = "Unknown"
                binding.tvMineAvatar.setImageResource(R.drawable.placeholder)
            }
        }
        
        /**
         * 统一头像加载方法
         */
        private fun loadAvatar(userRank: UserRankModel) {
            // 优先使用avatar，其次使用avatarMapPath，最后使用默认头像
            val avatarUrl = userRank.avatar ?: userRank.avatarMapPath
            
            if (!avatarUrl.isNullOrBlank()) {
                GlideUtils.load(
                    binding.tvMineAvatar,
                    avatarUrl,
                    placeholder = R.drawable.placeholder,
                    error = R.drawable.placeholder,
                    isCircle = true
                )
            } else {
                binding.tvMineAvatar.setImageResource(R.drawable.placeholder)
            }
        }
    }

    /**
     * DiffUtil回调，用于优化列表更新性能
     */
    private class RankDiffCallback : DiffUtil.ItemCallback<UserRankModel>() {
        override fun areItemsTheSame(oldItem: UserRankModel, newItem: UserRankModel): Boolean {
            return oldItem.userId == newItem.userId
        }

        override fun areContentsTheSame(oldItem: UserRankModel, newItem: UserRankModel): Boolean {
            return oldItem == newItem
        }
    }
}