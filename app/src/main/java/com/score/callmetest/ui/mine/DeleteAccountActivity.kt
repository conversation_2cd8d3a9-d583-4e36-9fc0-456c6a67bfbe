package com.score.callmetest.ui.mine

import androidx.lifecycle.lifecycleScope
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityDeleteAccountBinding
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.base.EmptyViewModel
import com.score.callmetest.ui.widget.BaseCustomDialog
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.launch
import timber.log.Timber

class DeleteAccountActivity : BaseActivity<ActivityDeleteAccountBinding, EmptyViewModel>() {
    
    override fun getViewBinding(): ActivityDeleteAccountBinding {
        return ActivityDeleteAccountBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = EmptyViewModel::class.java

    override fun initView() {
        // 初始化视图
        GlobalManager.addViewStatusBarTopMargin(this, binding.toolbar)
        CustomUtils.flipViewIfRTL(binding.ivBack)
    }

    override fun initListener() {
        // 返回按钮点击事件
        binding.ivBack.click { 
            finish() 
        }

        // 取消按钮点击事件
        binding.btnCancelLayout.click { 
            finish() 
        }

        // 删除账户按钮点击事件
        binding.btnDelete.click {
            showDeleteConfirmDialog()
        }
    }

    private fun showDeleteConfirmDialog() {
        BaseCustomDialog(
            context = this,
            emojiResId = R.drawable.emoji_cry,
            title = getString(R.string.delete_account_confirm_title),
            content = null,
            onAgree = {
                SharePreferenceUtil.putBoolean(Constant.KEY_IS_FIRST_LOGIN, true)
                // 调用logout接口
                lifecycleScope.launch {
                    try {
                        val response = RetrofitUtils.dataRepository.deleteAccount()
                        if (response is NetworkResult.Success) {
                            CustomUtils.logoutAndClearData(1, this@DeleteAccountActivity) // 退出登录时清理所有本地数据、SDK资源，并跳转到登录页
                        }
                    } catch (e: Exception) {
                        Timber.e(e)
                    }
                }

                ToastUtils.showToast(getString(R.string.delete_account))
            },
            onCancel = {}
        ).show()
    }
}
