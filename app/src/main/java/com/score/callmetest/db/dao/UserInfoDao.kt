package com.score.callmetest.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.score.callmetest.db.entity.UserInfoRoomEntity
import kotlinx.coroutines.flow.Flow

/**
 * 用户信息DAO接口
 * 提供用户信息的基本CRUD操作
 */
@Dao
interface UserInfoDao {
    
    /**
     * 插入一条用户信息
     * 如果用户ID已存在，则替换原有数据
     * @param userInfo 用户信息实体
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserInfo(userInfo: UserInfoRoomEntity)
    
    /**
     * 批量插入用户信息
     * 如果用户ID已存在，则替换原有数据
     * @param userInfos 用户信息实体列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserInfos(userInfos: List<UserInfoRoomEntity>)
    
    /**
     * 更新一条用户信息
     * @param userInfo 用户信息实体
     */
    @Update
    suspend fun updateUserInfo(userInfo: UserInfoRoomEntity)
    
    /**
     * 删除一条用户信息
     * @param userInfo 用户信息实体
     */
    @Delete
    suspend fun deleteUserInfo(userInfo: UserInfoRoomEntity)
    
    /**
     * 根据用户ID删除一条用户信息
     * @param userId 用户ID
     */
    @Query("DELETE FROM user_info WHERE userId = :userId")
    suspend fun deleteUserInfoById(userId: String)
    
    /**
     * 根据用户ID获取用户信息
     * @param userId 用户ID
     * @return 用户信息实体，如果不存在则返回null
     */
    @Query("SELECT * FROM user_info WHERE userId = :userId")
    suspend fun getUserInfoById(userId: String): UserInfoRoomEntity?
    
    /**
     * 根据用户ID获取用户信息（Flow版本）
     * 适用于需要监听数据变化的场景
     * @param userId 用户ID
     * @return 用户信息实体流
     */
    @Query("SELECT * FROM user_info WHERE userId = :userId")
    fun getUserInfoByIdFlow(userId: String): Flow<UserInfoRoomEntity?>
    
    /**
     * 获取所有用户信息，按更新时间倒序排列
     * @return 用户信息实体流
     */
    @Query("SELECT * FROM user_info ORDER BY updated_time DESC")
    fun getAllUserInfos(): Flow<List<UserInfoRoomEntity>>
    
    /**
     * 根据昵称模糊搜索用户信息
     * @param nickName 昵称关键字
     * @return 匹配的用户信息列表
     */
    @Query("SELECT * FROM user_info WHERE nick_name LIKE '%' || :nickName || '%' ORDER BY updated_time DESC")
    fun searchUserInfosByNickName(nickName: String): Flow<List<UserInfoRoomEntity>>
    
    /**
     * 获取用户信息总数
     * @return 用户信息总数
     */
    @Query("SELECT COUNT(*) FROM user_info")
    suspend fun getUserInfoCount(): Int
    
    /**
     * 检查用户是否存在
     * @param userId 用户ID
     * @return true 如果用户存在，false 否则
     */
    @Query("SELECT COUNT(*) > 0 FROM user_info WHERE userId = :userId")
    suspend fun isUserExists(userId: String): Boolean
    
    /**
     * 更新用户头像信息
     * @param userId 用户ID
     * @param avatarThumbUrl 头像缩略图URL
     * @param avatarMiddleThumbUrl 头像中等尺寸缩略图URL
     * @param avatar 头像原图URL
     */
    @Query("""
        UPDATE user_info 
        SET avatar_thumb_url = :avatarThumbUrl, 
            avatar_middle_thumb_url = :avatarMiddleThumbUrl, 
            avatar = :avatar, 
            updated_time = :updatedTime 
        WHERE userId = :userId
    """)
    suspend fun updateUserAvatar(
        userId: String, 
        avatarThumbUrl: String, 
        avatarMiddleThumbUrl: String, 
        avatar: String,
        updatedTime: Long = System.currentTimeMillis()
    )
    
    /**
     * 更新用户昵称
     * @param userId 用户ID
     * @param nickName 新昵称
     */
    @Query("""
        UPDATE user_info 
        SET nick_name = :nickName, 
            updated_time = :updatedTime 
        WHERE userId = :userId
    """)
    suspend fun updateUserNickName(
        userId: String, 
        nickName: String,
        updatedTime: Long = System.currentTimeMillis()
    )
    
    /**
     * 批量删除用户信息
     * @param userIds 用户ID列表
     */
    @Query("DELETE FROM user_info WHERE userId IN (:userIds)")
    suspend fun deleteUserInfosByIds(userIds: List<String>)
    
    /**
     * 清空所有用户信息
     */
    @Query("DELETE FROM user_info")
    suspend fun clearAllUserInfos()
    
    /**
     * 获取最近更新的用户信息
     * @param limit 限制数量
     * @return 最近更新的用户信息列表
     */
    @Query("SELECT * FROM user_info ORDER BY updated_time DESC LIMIT :limit")
    fun getRecentlyUpdatedUserInfos(limit: Int = 10): Flow<List<UserInfoRoomEntity>>
    
    /**
     * 根据创建时间范围获取用户信息
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 指定时间范围内创建的用户信息列表
     */
    @Query("""
        SELECT * FROM user_info 
        WHERE created_time >= :startTime AND created_time <= :endTime 
        ORDER BY created_time DESC
    """)
    fun getUserInfosByCreatedTimeRange(startTime: Long, endTime: Long): Flow<List<UserInfoRoomEntity>>
}
