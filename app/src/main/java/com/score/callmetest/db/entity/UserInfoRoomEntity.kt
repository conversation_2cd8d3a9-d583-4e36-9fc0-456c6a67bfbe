package com.score.callmetest.db.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 用户信息Room实体类
 * 用于存储用户的基本信息，包括头像、昵称等
 */
@Entity(
    tableName = "user_info",
    indices = [Index(value = ["userId"])]
)
data class UserInfoRoomEntity(
    /**
     * 用户ID，作为主键
     */
    @PrimaryKey
    val userId: String,

    /**
     * 头像缩略图URL
     * 用于列表显示等场景，文件较小，加载速度快
     */
    @ColumnInfo(name = "avatar_thumb_url")
    val avatarThumbUrl: String = "",

    /**
     * 头像中等尺寸缩略图URL
     * 用于详情页等需要中等尺寸头像的场景
     */
    @ColumnInfo(name = "avatar_middle_thumb_url")
    val avatarMiddleThumbUrl: String = "",

    /**
     * 头像原图URL
     * 用于查看大图等场景，文件较大，清晰度高
     */
    @ColumnInfo(name = "avatar")
    val avatar: String = "",

    /**
     * 用户昵称
     */
    @ColumnInfo(name = "nick_name")
    val nickName: String = ""
) {

    /**
     * 获取最适合的头像URL
     * 优先级：avatar > avatarMiddleThumbUrl > avatarThumbUrl
     * @return 头像URL，如果都为空则返回空字符串
     */
    fun getBestAvatar(): String {
        return when {
            avatar.isNotBlank() -> avatar
            avatarMiddleThumbUrl.isNotBlank() -> avatarMiddleThumbUrl
            avatarThumbUrl.isNotBlank() -> avatarThumbUrl
            else -> ""
        }
    }

    /**
     * 获取缩略图头像URL
     * 优先级：avatarThumbUrl > avatarMiddleThumbUrl > avatar
     * @return 缩略图头像URL，如果都为空则返回空字符串
     */
    fun getThumbAvatar(): String {
        return when {
            avatarThumbUrl.isNotBlank() -> avatarThumbUrl
            avatarMiddleThumbUrl.isNotBlank() -> avatarMiddleThumbUrl
            avatar.isNotBlank() -> avatar
            else -> ""
        }
    }

    /**
     * 获取显示名称
     * 如果昵称为空，则返回用户ID
     * @return 显示名称
     */
    fun getDisplayName(): String {
        return if (nickName.isNotBlank()) nickName else userId
    }

    /**
     * 检查用户信息是否有效
     * @return true 如果用户ID不为空，false 否则
     */
    fun isValid(): Boolean {
        return userId.isNotBlank()
    }
}
