package com.score.callmetest.db

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.score.callmetest.db.entity.UserInfoRoomEntity
import com.score.callmetest.db.repository.UserInfoRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 用户信息简单使用示例
 * 展示如何使用简化后的用户信息表进行基本操作
 */
class UserInfoSimpleExample(context: Context) : ViewModel() {
    
    private val database = AppDatabase.getInstance(context)
    private val userInfoRepository = UserInfoRepository(database.userInfoDao())
    
    // 用户信息状态
    private val _userInfo = MutableStateFlow<UserInfoRoomEntity?>(null)
    val userInfo: StateFlow<UserInfoRoomEntity?> = _userInfo.asStateFlow()
    
    // 用户信息列表状态
    private val _userInfoList = MutableStateFlow<List<UserInfoRoomEntity>>(emptyList())
    val userInfoList: StateFlow<List<UserInfoRoomEntity>> = _userInfoList.asStateFlow()
    
    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    init {
        // 初始化时加载所有用户信息
        loadAllUserInfos()
    }
    
    /**
     * 保存用户信息
     * @param userId 用户ID
     * @param avatarThumbUrl 头像缩略图URL
     * @param avatarMiddleThumbUrl 头像中等尺寸缩略图URL
     * @param avatar 头像原图URL
     * @param nickName 用户昵称
     * @param onEvent 结果回调，true表示成功，false表示失败
     */
    fun saveUserInfo(
        userId: String,
        avatarThumbUrl: String = "",
        avatarMiddleThumbUrl: String = "",
        avatar: String = "",
        nickName: String = "",
        onEvent: (Boolean) -> Unit
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                val userInfo = UserInfoRoomEntity(
                    userId = userId,
                    avatarThumbUrl = avatarThumbUrl,
                    avatarMiddleThumbUrl = avatarMiddleThumbUrl,
                    avatar = avatar,
                    nickName = nickName
                )
                
                userInfoRepository.insertUserInfo(userInfo)
                onEvent(true)
                Timber.d("保存用户信息成功: userId=$userId")
                
            } catch (e: Exception) {
                onEvent(false)
                Timber.e(e, "保存用户信息失败: userId=$userId")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 根据用户ID获取用户信息
     * @param userId 用户ID
     */
    fun getUserInfo(userId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val userInfo = userInfoRepository.getUserInfoById(userId)
                _userInfo.value = userInfo
                Timber.d("获取用户信息: userId=$userId, found=${userInfo != null}")
            } catch (e: Exception) {
                Timber.e(e, "获取用户信息失败: userId=$userId")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 更新用户昵称
     * @param userId 用户ID
     * @param nickName 新昵称
     * @param onEvent 结果回调
     */
    fun updateNickName(userId: String, nickName: String, onEvent: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.updateUserNickName(userId, nickName)
                onEvent(true)
                Timber.d("更新昵称成功: userId=$userId, nickName=$nickName")
            } catch (e: Exception) {
                onEvent(false)
                Timber.e(e, "更新昵称失败: userId=$userId")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 更新用户头像
     * @param userId 用户ID
     * @param avatarThumbUrl 头像缩略图URL
     * @param avatarMiddleThumbUrl 头像中等尺寸缩略图URL
     * @param avatar 头像原图URL
     * @param onEvent 结果回调
     */
    fun updateAvatar(
        userId: String,
        avatarThumbUrl: String,
        avatarMiddleThumbUrl: String,
        avatar: String,
        onEvent: (Boolean) -> Unit
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.updateUserAvatar(userId, avatarThumbUrl, avatarMiddleThumbUrl, avatar)
                onEvent(true)
                Timber.d("更新头像成功: userId=$userId")
            } catch (e: Exception) {
                onEvent(false)
                Timber.e(e, "更新头像失败: userId=$userId")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 删除用户信息
     * @param userId 用户ID
     * @param onEvent 结果回调
     */
    fun deleteUserInfo(userId: String, onEvent: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.deleteUserInfoById(userId)
                onEvent(true)
                Timber.d("删除用户信息成功: userId=$userId")
            } catch (e: Exception) {
                onEvent(false)
                Timber.e(e, "删除用户信息失败: userId=$userId")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 搜索用户信息
     * @param nickName 昵称关键字
     */
    fun searchUserInfos(nickName: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.searchUserInfosByNickName(nickName).collect { userInfos ->
                    _userInfoList.value = userInfos
                    Timber.d("搜索用户信息: keyword=$nickName, count=${userInfos.size}")
                }
            } catch (e: Exception) {
                Timber.e(e, "搜索用户信息失败: keyword=$nickName")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 加载所有用户信息
     */
    private fun loadAllUserInfos() {
        viewModelScope.launch {
            try {
                userInfoRepository.getAllUserInfos().collect { userInfos ->
                    _userInfoList.value = userInfos
                    Timber.d("加载所有用户信息: count=${userInfos.size}")
                }
            } catch (e: Exception) {
                Timber.e(e, "加载所有用户信息失败")
            }
        }
    }
    
    /**
     * 批量保存用户信息
     * @param userInfos 用户信息列表
     * @param onEvent 结果回调
     */
    fun batchSaveUserInfos(userInfos: List<UserInfoRoomEntity>, onEvent: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.insertUserInfos(userInfos)
                onEvent(true)
                Timber.d("批量保存用户信息成功: count=${userInfos.size}")
            } catch (e: Exception) {
                onEvent(false)
                Timber.e(e, "批量保存用户信息失败: count=${userInfos.size}")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 检查用户是否存在
     * @param userId 用户ID
     * @param onResult 结果回调
     */
    fun checkUserExists(userId: String, onResult: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                val exists = userInfoRepository.isUserExists(userId)
                onResult(exists)
                Timber.d("检查用户是否存在: userId=$userId, exists=$exists")
            } catch (e: Exception) {
                onResult(false)
                Timber.e(e, "检查用户是否存在失败: userId=$userId")
            }
        }
    }
    
    /**
     * 清空所有用户信息
     * @param onEvent 结果回调
     */
    fun clearAllUserInfos(onEvent: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.clearAllUserInfos()
                onEvent(true)
                Timber.d("清空所有用户信息成功")
            } catch (e: Exception) {
                onEvent(false)
                Timber.e(e, "清空所有用户信息失败")
            } finally {
                _isLoading.value = false
            }
        }
    }
}
