package com.score.callmetest.db.repository

import com.score.callmetest.db.dao.UserInfoDao
import com.score.callmetest.db.entity.UserInfoRoomEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * 用户信息仓库，负责处理用户信息相关的数据操作
 * 提供线程安全的数据访问接口，所有数据库操作都在IO线程中执行
 */
class UserInfoRepository(private val userInfoDao: UserInfoDao) {
    
    /**
     * 插入一条用户信息
     * @param userInfo 用户信息实体
     */
    suspend fun insertUserInfo(userInfo: UserInfoRoomEntity) = withContext(Dispatchers.IO) {
        try {
            userInfoDao.insertUserInfo(userInfo)
            Timber.d("用户信息插入成功: userId=${userInfo.userId}")
        } catch (e: Exception) {
            Timber.e(e, "用户信息插入失败: userId=${userInfo.userId}")
            throw e
        }
    }
    
    /**
     * 批量插入用户信息
     * @param userInfos 用户信息实体列表
     */
    suspend fun insertUserInfos(userInfos: List<UserInfoRoomEntity>) = withContext(Dispatchers.IO) {
        try {
            userInfoDao.insertUserInfos(userInfos)
            Timber.d("批量用户信息插入成功: count=${userInfos.size}")
        } catch (e: Exception) {
            Timber.e(e, "批量用户信息插入失败: count=${userInfos.size}")
            throw e
        }
    }
    
    /**
     * 更新一条用户信息
     * @param userInfo 用户信息实体
     */
    suspend fun updateUserInfo(userInfo: UserInfoRoomEntity) = withContext(Dispatchers.IO) {
        try {
            userInfoDao.updateUserInfo(userInfo)
            Timber.d("用户信息更新成功: userId=${userInfo.userId}")
        } catch (e: Exception) {
            Timber.e(e, "用户信息更新失败: userId=${userInfo.userId}")
            throw e
        }
    }
    
    /**
     * 删除一条用户信息
     * @param userInfo 用户信息实体
     */
    suspend fun deleteUserInfo(userInfo: UserInfoRoomEntity) = withContext(Dispatchers.IO) {
        try {
            userInfoDao.deleteUserInfo(userInfo)
            Timber.d("用户信息删除成功: userId=${userInfo.userId}")
        } catch (e: Exception) {
            Timber.e(e, "用户信息删除失败: userId=${userInfo.userId}")
            throw e
        }
    }
    
    /**
     * 根据用户ID删除一条用户信息
     * @param userId 用户ID
     */
    suspend fun deleteUserInfoById(userId: String) = withContext(Dispatchers.IO) {
        try {
            userInfoDao.deleteUserInfoById(userId)
            Timber.d("用户信息删除成功: userId=$userId")
        } catch (e: Exception) {
            Timber.e(e, "用户信息删除失败: userId=$userId")
            throw e
        }
    }
    
    /**
     * 根据用户ID获取用户信息
     * @param userId 用户ID
     * @return 用户信息实体，如果不存在则返回null
     */
    suspend fun getUserInfoById(userId: String): UserInfoRoomEntity? = withContext(Dispatchers.IO) {
        try {
            val result = userInfoDao.getUserInfoById(userId)
            Timber.d("获取用户信息: userId=$userId, found=${result != null}")
            result
        } catch (e: Exception) {
            Timber.e(e, "获取用户信息失败: userId=$userId")
            null
        }
    }
    
    /**
     * 根据用户ID获取用户信息（Flow版本）
     * 适用于需要监听数据变化的场景
     * @param userId 用户ID
     * @return 用户信息实体流
     */
    fun getUserInfoByIdFlow(userId: String): Flow<UserInfoRoomEntity?> {
        return userInfoDao.getUserInfoByIdFlow(userId)
    }
    
    /**
     * 获取所有用户信息，按用户ID排序
     * @return 用户信息实体流
     */
    fun getAllUserInfos(): Flow<List<UserInfoRoomEntity>> {
        return userInfoDao.getAllUserInfos()
    }
    
    /**
     * 根据昵称模糊搜索用户信息
     * @param nickName 昵称关键字
     * @return 匹配的用户信息列表
     */
    fun searchUserInfosByNickName(nickName: String): Flow<List<UserInfoRoomEntity>> {
        return userInfoDao.searchUserInfosByNickName(nickName)
    }
    
    /**
     * 获取用户信息总数
     * @return 用户信息总数
     */
    suspend fun getUserInfoCount(): Int = withContext(Dispatchers.IO) {
        try {
            userInfoDao.getUserInfoCount()
        } catch (e: Exception) {
            Timber.e(e, "获取用户信息总数失败")
            0
        }
    }
    
    /**
     * 检查用户是否存在
     * @param userId 用户ID
     * @return true 如果用户存在，false 否则
     */
    suspend fun isUserExists(userId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            userInfoDao.isUserExists(userId)
        } catch (e: Exception) {
            Timber.e(e, "检查用户是否存在失败: userId=$userId")
            false
        }
    }
    
    /**
     * 更新用户头像信息
     * @param userId 用户ID
     * @param avatarThumbUrl 头像缩略图URL
     * @param avatarMiddleThumbUrl 头像中等尺寸缩略图URL
     * @param avatar 头像原图URL
     */
    suspend fun updateUserAvatar(
        userId: String,
        avatarThumbUrl: String,
        avatarMiddleThumbUrl: String,
        avatar: String
    ) = withContext(Dispatchers.IO) {
        try {
            userInfoDao.updateUserAvatar(userId, avatarThumbUrl, avatarMiddleThumbUrl, avatar)
            Timber.d("用户头像更新成功: userId=$userId")
        } catch (e: Exception) {
            Timber.e(e, "用户头像更新失败: userId=$userId")
            throw e
        }
    }
    
    /**
     * 更新用户昵称
     * @param userId 用户ID
     * @param nickName 新昵称
     */
    suspend fun updateUserNickName(userId: String, nickName: String) = withContext(Dispatchers.IO) {
        try {
            userInfoDao.updateUserNickName(userId, nickName)
            Timber.d("用户昵称更新成功: userId=$userId, nickName=$nickName")
        } catch (e: Exception) {
            Timber.e(e, "用户昵称更新失败: userId=$userId")
            throw e
        }
    }
    
    /**
     * 批量删除用户信息
     * @param userIds 用户ID列表
     */
    suspend fun deleteUserInfosByIds(userIds: List<String>) = withContext(Dispatchers.IO) {
        try {
            userInfoDao.deleteUserInfosByIds(userIds)
            Timber.d("批量删除用户信息成功: count=${userIds.size}")
        } catch (e: Exception) {
            Timber.e(e, "批量删除用户信息失败: count=${userIds.size}")
            throw e
        }
    }
    
    /**
     * 清空所有用户信息
     */
    suspend fun clearAllUserInfos() = withContext(Dispatchers.IO) {
        try {
            userInfoDao.clearAllUserInfos()
            Timber.d("清空所有用户信息成功")
        } catch (e: Exception) {
            Timber.e(e, "清空所有用户信息失败")
            throw e
        }
    }
    
    /**
     * 保存或更新用户信息
     * 如果用户不存在则插入，存在则更新
     * @param userInfo 用户信息实体
     */
    suspend fun saveOrUpdateUserInfo(userInfo: UserInfoRoomEntity) = withContext(Dispatchers.IO) {
        try {
            val exists = isUserExists(userInfo.userId)
            if (exists) {
                updateUserInfo(userInfo)
            } else {
                insertUserInfo(userInfo)
            }
            Timber.d("保存或更新用户信息成功: userId=${userInfo.userId}, isUpdate=$exists")
        } catch (e: Exception) {
            Timber.e(e, "保存或更新用户信息失败: userId=${userInfo.userId}")
            throw e
        }
    }

    /**
     * 使用SQL进行upsert操作：存在则更新，不存在则插入
     * 性能更好，一次SQL操作完成
     * @param userInfo 用户信息实体
     */
    suspend fun upsertUserInfo(userInfo: UserInfoRoomEntity) = withContext(Dispatchers.IO) {
        try {
            userInfoDao.upsertUserInfo(userInfo)
            Timber.d("Upsert用户信息成功: userId=${userInfo.userId}")
        } catch (e: Exception) {
            Timber.e(e, "Upsert用户信息失败: userId=${userInfo.userId}")
            throw e
        }
    }

    /**
     * 使用SQL进行upsert操作：存在则更新，不存在则插入
     * 直接传参数版本
     * @param userId 用户ID
     * @param avatarThumbUrl 头像缩略图URL
     * @param avatarMiddleThumbUrl 头像中等尺寸缩略图URL
     * @param avatar 头像原图URL
     * @param nickName 用户昵称
     */
    suspend fun upsertUserInfo(
        userId: String,
        avatarThumbUrl: String = "",
        avatarMiddleThumbUrl: String = "",
        avatar: String = "",
        nickName: String = ""
    ) = withContext(Dispatchers.IO) {
        try {
            userInfoDao.upsertUserInfo(userId, avatarThumbUrl, avatarMiddleThumbUrl, avatar, nickName)
            Timber.d("Upsert用户信息成功: userId=$userId")
        } catch (e: Exception) {
            Timber.e(e, "Upsert用户信息失败: userId=$userId")
            throw e
        }
    }
}
