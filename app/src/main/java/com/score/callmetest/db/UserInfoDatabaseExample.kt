package com.score.callmetest.db

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.score.callmetest.db.repository.UserInfoRepository
import com.score.callmetest.entity.UserInfoEntity
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 用户信息数据库使用示例
 * 展示如何在ViewModel中使用UserInfoRepository进行CRUD操作
 * 
 * 使用方法：
 * 1. 在Activity/Fragment中注入UserInfoRepository
 * 2. 创建ViewModel实例并传入repository
 * 3. 调用相应的方法进行数据操作
 */
class UserInfoDatabaseExample(
    private val userInfoRepository: UserInfoRepository
) : ViewModel() {
    
    // 用户信息列表状态
    private val _userInfoList = MutableStateFlow<List<UserInfoEntity>>(emptyList())
    val userInfoList: StateFlow<List<UserInfoEntity>> = _userInfoList.asStateFlow()
    
    // 当前选中的用户信息
    private val _currentUserInfo = MutableStateFlow<UserInfoEntity?>(null)
    val currentUserInfo: StateFlow<UserInfoEntity?> = _currentUserInfo.asStateFlow()
    
    // 操作状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    init {
        // 初始化时加载所有用户信息
        loadAllUserInfos()
    }
    
    /**
     * 加载所有用户信息
     */
    fun loadAllUserInfos() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.getAllUserInfos().collect { userInfos ->
                    _userInfoList.value = userInfos
                    Timber.d("加载用户信息列表成功: count=${userInfos.size}")
                }
            } catch (e: Exception) {
                Timber.e(e, "加载用户信息列表失败")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 根据用户ID获取用户信息
     * @param userId 用户ID
     */
    fun getUserInfoById(userId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val userInfo = userInfoRepository.getUserInfoById(userId)
                _currentUserInfo.value = userInfo
                Timber.d("获取用户信息成功: userId=$userId, found=${userInfo != null}")
            } catch (e: Exception) {
                Timber.e(e, "获取用户信息失败: userId=$userId")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 保存用户信息
     * @param userInfo 用户信息实体
     * @param onSuccess 成功回调
     * @param onError 错误回调
     */
    fun saveUserInfo(
        userInfo: UserInfoEntity,
        onSuccess: () -> Unit = {},
        onError: (Exception) -> Unit = {}
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.insertUserInfo(userInfo)
                onSuccess()
                Timber.d("保存用户信息成功: userId=${userInfo.userId}")
            } catch (e: Exception) {
                onError(e)
                Timber.e(e, "保存用户信息失败: userId=${userInfo.userId}")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 更新用户信息
     * @param userInfo 用户信息实体
     * @param onSuccess 成功回调
     * @param onError 错误回调
     */
    fun updateUserInfo(
        userInfo: UserInfoEntity,
        onSuccess: () -> Unit = {},
        onError: (Exception) -> Unit = {}
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.updateUserInfo(userInfo)
                onSuccess()
                Timber.d("更新用户信息成功: userId=${userInfo.userId}")
            } catch (e: Exception) {
                onError(e)
                Timber.e(e, "更新用户信息失败: userId=${userInfo.userId}")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 删除用户信息
     * @param userId 用户ID
     * @param onSuccess 成功回调
     * @param onError 错误回调
     */
    fun deleteUserInfo(
        userId: String,
        onSuccess: () -> Unit = {},
        onError: (Exception) -> Unit = {}
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.deleteUserInfoById(userId)
                onSuccess()
                Timber.d("删除用户信息成功: userId=$userId")
            } catch (e: Exception) {
                onError(e)
                Timber.e(e, "删除用户信息失败: userId=$userId")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 更新用户头像
     * @param userId 用户ID
     * @param avatarThumbUrl 头像缩略图URL
     * @param avatarMiddleThumbUrl 头像中等尺寸缩略图URL
     * @param avatar 头像原图URL
     * @param onSuccess 成功回调
     * @param onError 错误回调
     */
    fun updateUserAvatar(
        userId: String,
        avatarThumbUrl: String,
        avatarMiddleThumbUrl: String,
        avatar: String,
        onSuccess: () -> Unit = {},
        onError: (Exception) -> Unit = {}
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.updateUserAvatar(userId, avatarThumbUrl, avatarMiddleThumbUrl, avatar)
                onSuccess()
                Timber.d("更新用户头像成功: userId=$userId")
            } catch (e: Exception) {
                onError(e)
                Timber.e(e, "更新用户头像失败: userId=$userId")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 更新用户昵称
     * @param userId 用户ID
     * @param nickName 新昵称
     * @param onSuccess 成功回调
     * @param onError 错误回调
     */
    fun updateUserNickName(
        userId: String,
        nickName: String,
        onSuccess: () -> Unit = {},
        onError: (Exception) -> Unit = {}
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.updateUserNickName(userId, nickName)
                onSuccess()
                Timber.d("更新用户昵称成功: userId=$userId, nickName=$nickName")
            } catch (e: Exception) {
                onError(e)
                Timber.e(e, "更新用户昵称失败: userId=$userId")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 搜索用户信息
     * @param nickName 昵称关键字
     */
    fun searchUserInfosByNickName(nickName: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.searchUserInfosByNickName(nickName).collect { userInfos ->
                    _userInfoList.value = userInfos
                    Timber.d("搜索用户信息成功: keyword=$nickName, count=${userInfos.size}")
                }
            } catch (e: Exception) {
                Timber.e(e, "搜索用户信息失败: keyword=$nickName")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 批量保存用户信息
     * @param userInfos 用户信息列表
     * @param onSuccess 成功回调
     * @param onError 错误回调
     */
    fun batchSaveUserInfos(
        userInfos: List<UserInfoEntity>,
        onSuccess: () -> Unit = {},
        onError: (Exception) -> Unit = {}
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.insertUserInfos(userInfos)
                onSuccess()
                Timber.d("批量保存用户信息成功: count=${userInfos.size}")
            } catch (e: Exception) {
                onError(e)
                Timber.e(e, "批量保存用户信息失败: count=${userInfos.size}")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 清空所有用户信息
     * @param onSuccess 成功回调
     * @param onError 错误回调
     */
    fun clearAllUserInfos(
        onSuccess: () -> Unit = {},
        onError: (Exception) -> Unit = {}
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                userInfoRepository.clearAllUserInfos()
                onSuccess()
                Timber.d("清空所有用户信息成功")
            } catch (e: Exception) {
                onError(e)
                Timber.e(e, "清空所有用户信息失败")
            } finally {
                _isLoading.value = false
            }
        }
    }
}
