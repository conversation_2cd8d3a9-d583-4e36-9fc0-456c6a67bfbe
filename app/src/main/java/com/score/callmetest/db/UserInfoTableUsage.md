# 用户信息表使用说明

## 概述

新增了用户信息表 `user_info`，用于存储用户的基本信息，包括头像、昵称等。该表提供了完整的CRUD功能，支持线程安全的数据操作。

## 表结构

| 字段名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| userId | TEXT | 用户ID（主键） | - |
| avatar_thumb_url | TEXT | 头像缩略图URL | "" |
| avatar_middle_thumb_url | TEXT | 头像中等尺寸缩略图URL | "" |
| avatar | TEXT | 头像原图URL | "" |
| nick_name | TEXT | 用户昵称 | "" |
| created_time | INTEGER | 创建时间戳（毫秒） | 当前时间 |
| updated_time | INTEGER | 更新时间戳（毫秒） | 当前时间 |

## 核心组件

### 1. 实体类
- **UserInfoEntity**: 业务实体类，用于业务逻辑层
- **UserInfoRoomEntity**: Room数据库实体类，用于数据持久化

### 2. DAO接口
- **UserInfoDao**: 数据访问对象，提供基础的数据库操作方法

### 3. Repository
- **UserInfoRepository**: 数据仓库，封装数据访问逻辑，提供线程安全的操作

## 基本使用方法

### 1. 获取Repository实例

```kotlin
// 在Activity/Fragment中
val database = AppDatabase.getInstance(this)
val userInfoRepository = UserInfoRepository(database.userInfoDao())
```

### 2. 基本CRUD操作

#### 插入用户信息
```kotlin
// 创建用户信息
val userInfo = UserInfoEntity.create(
    userId = "user123",
    avatarThumbUrl = "https://example.com/thumb.jpg",
    avatarMiddleThumbUrl = "https://example.com/middle.jpg",
    avatar = "https://example.com/avatar.jpg",
    nickName = "张三"
)

// 插入数据库
viewModelScope.launch {
    try {
        userInfoRepository.insertUserInfo(userInfo)
        // 插入成功
    } catch (e: Exception) {
        // 处理错误
    }
}
```

#### 查询用户信息
```kotlin
// 根据用户ID查询
viewModelScope.launch {
    val userInfo = userInfoRepository.getUserInfoById("user123")
    if (userInfo != null) {
        // 找到用户信息
        val displayName = userInfo.getDisplayName()
        val avatar = userInfo.getBestAvatar()
    }
}

// 监听数据变化（Flow）
userInfoRepository.getUserInfoByIdFlow("user123").collect { userInfo ->
    // 数据更新时会自动触发
}
```

#### 更新用户信息
```kotlin
// 更新完整信息
val updatedUserInfo = userInfo.copy(nickName = "李四")
userInfoRepository.updateUserInfo(updatedUserInfo)

// 只更新头像
userInfoRepository.updateUserAvatar(
    userId = "user123",
    avatarThumbUrl = "new_thumb.jpg",
    avatarMiddleThumbUrl = "new_middle.jpg",
    avatar = "new_avatar.jpg"
)

// 只更新昵称
userInfoRepository.updateUserNickName("user123", "王五")
```

#### 删除用户信息
```kotlin
// 根据用户ID删除
userInfoRepository.deleteUserInfoById("user123")

// 删除实体
userInfoRepository.deleteUserInfo(userInfo)
```

### 3. 高级查询

#### 获取所有用户信息
```kotlin
userInfoRepository.getAllUserInfos().collect { userInfoList ->
    // 处理用户信息列表
}
```

#### 搜索用户
```kotlin
userInfoRepository.searchUserInfosByNickName("张").collect { searchResults ->
    // 处理搜索结果
}
```

#### 批量操作
```kotlin
// 批量插入
val userInfoList = listOf(userInfo1, userInfo2, userInfo3)
userInfoRepository.insertUserInfos(userInfoList)

// 批量删除
val userIds = listOf("user1", "user2", "user3")
userInfoRepository.deleteUserInfosByIds(userIds)
```

## 在ViewModel中使用

推荐在ViewModel中使用Repository，参考 `UserInfoDatabaseExample.kt` 文件：

```kotlin
class MyViewModel(
    private val userInfoRepository: UserInfoRepository
) : ViewModel() {
    
    private val _userInfo = MutableStateFlow<UserInfoEntity?>(null)
    val userInfo: StateFlow<UserInfoEntity?> = _userInfo.asStateFlow()
    
    fun loadUserInfo(userId: String) {
        viewModelScope.launch {
            _userInfo.value = userInfoRepository.getUserInfoById(userId)
        }
    }
    
    fun saveUserInfo(userInfo: UserInfoEntity, onEvent: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                userInfoRepository.insertUserInfo(userInfo)
                onEvent(true) // 成功
            } catch (e: Exception) {
                Timber.e(e, "保存用户信息失败")
                onEvent(false) // 失败
            }
        }
    }
}
```

## 数据库迁移

当前数据库版本已更新到版本3，包含了用户信息表的创建。如果是从旧版本升级，会自动执行迁移脚本创建新表。

## 注意事项

1. **线程安全**: 所有数据库操作都在IO线程中执行，使用 `withContext(Dispatchers.IO)`
2. **错误处理**: Repository中包含了完整的错误处理和日志记录
3. **性能优化**: 为常用查询字段创建了索引（nick_name, updated_time）
4. **数据一致性**: 使用 `OnConflictStrategy.REPLACE` 策略处理主键冲突
5. **时间戳管理**: 自动管理创建时间和更新时间

## 扩展功能

如需添加新字段或功能，请：
1. 更新 `UserInfoEntity` 和 `UserInfoRoomEntity`
2. 在 `UserInfoDao` 中添加相应的查询方法
3. 在 `UserInfoRepository` 中添加业务逻辑
4. 更新数据库版本并添加迁移脚本
