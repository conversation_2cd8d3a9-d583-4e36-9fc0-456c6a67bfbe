package com.score.callmetest.db

import android.content.Context
import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.sqlite.db.SupportSQLiteDatabase
import com.score.callmetest.db.converter.MessageTypeConverter
import com.score.callmetest.db.converter.MessageStatusConverter
import com.score.callmetest.db.converter.GiftInfoConverter
import com.score.callmetest.db.dao.CallHistoryDao
import com.score.callmetest.db.dao.ChatMessageDao
import com.score.callmetest.db.dao.MessageListDao
import com.score.callmetest.db.dao.UserInfoDao
import com.score.callmetest.db.entity.CallHistoryRoomEntity
import com.score.callmetest.db.entity.ChatMessageRoomEntity
import com.score.callmetest.db.entity.MessageListRoomEntity
import com.score.callmetest.db.entity.UserInfoRoomEntity
import com.score.callmetest.db.migration.Migrations
import timber.log.Timber

/**
 * 应用数据库
 * 注意：当修改数据库版本时，请同步更新Migrations.CURRENT_VERSION
 */
@Database(
    entities = [
        MessageListRoomEntity::class,
        CallHistoryRoomEntity::class,
        ChatMessageRoomEntity::class,
        UserInfoRoomEntity::class
    ],
    version = Migrations.CURRENT_VERSION,
    exportSchema = true,
    autoMigrations = [
        // 自动迁移
        AutoMigration(from = 1, to = 2),
        AutoMigration(from = 2, to = 3)
    ]
)
@TypeConverters(
    MessageTypeConverter::class,
    MessageStatusConverter::class,
    GiftInfoConverter::class
)
abstract class AppDatabase : RoomDatabase() {
    
    /**
     * 获取消息列表DAO
     */
    abstract fun messageListDao(): MessageListDao
    
    /**
     * 获取通话历史DAO
     */
    abstract fun callHistoryDao(): CallHistoryDao
    
    /**
     * 获取聊天消息DAO
     */
    abstract fun chatMessageDao(): ChatMessageDao

    /**
     * 获取用户信息DAO
     */
    abstract fun userInfoDao(): UserInfoDao
    
    companion object {
        // 数据库实例
        @Volatile
        private var INSTANCE: AppDatabase? = null
        
        // 数据库名称
        const val DATABASE_NAME = "app_database"
        
        /**
         * 获取数据库实例，使用单例模式
         * @param context 上下文
         * @return 数据库实例
         */
        fun getInstance(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    DATABASE_NAME
                )
                .addCallback(object : RoomDatabase.Callback() {
                    /**
                     * 首次创建数据库时调用
                     * 首次安装应用时会调用此方法，不需要迁移
                     */
                    override fun onCreate(db: SupportSQLiteDatabase) {
                        super.onCreate(db)
                        Timber.d("Database created: $DATABASE_NAME (version ${Migrations.CURRENT_VERSION})")
                        // 可以在这里添加初始数据，例如：
                        // 注意：这里的操作运行在事务中，如果抛出异常，数据库创建将失败
                        // db.execSQL("INSERT INTO table_name (column1, column2) VALUES ('value1', 'value2')")
                    }
                    
                    /**
                     * 打开现有数据库时调用
                     * 当应用启动且数据库已经存在时调用
                     */
                    override fun onOpen(db: SupportSQLiteDatabase) {
                        super.onOpen(db)
                        Timber.d("Database opened: $DATABASE_NAME (version ${db.version})")
                    }
                })
                .addMigrations(*Migrations.ALL_MIGRATIONS)
                .fallbackToDestructiveMigration(true) // 删表重建
                .build()
                INSTANCE = instance
                instance
            }
        }
        
        /**
         * 销毁数据库实例
         * 在应用关闭或者需要重新初始化数据库时调用
         */
        fun destroyInstance() {
            if (INSTANCE?.isOpen == true) {
                INSTANCE?.close()
            }
            INSTANCE = null
            Timber.d("Database instance destroyed")
        }
    }

    /**
     * 清空所有表数据
     */
    fun clearAllTablesData() {
        this.clearAllTables()
    }
}