package com.score.callmetest.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.score.callmetest.db.entity.ChatMessageRoomEntity
import com.score.callmetest.entity.ChatMessageEntity
import kotlinx.coroutines.flow.Flow

/**
 * 聊天消息DAO接口
 */
@Dao
interface ChatMessageDao {
    /**
     * 插入一条聊天消息
     * @param chatMessage 聊天消息实体
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChatMessage(chatMessage: ChatMessageRoomEntity)
    
    /**
     * 批量插入聊天消息
     * @param chatMessages 聊天消息实体列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChatMessages(chatMessages: List<ChatMessageRoomEntity>)
    
    /**
     * 更新一条聊天消息
     * @param chatMessage 聊天消息实体
     */
    @Update
    suspend fun updateChatMessage(chatMessage: ChatMessageRoomEntity)
    
    /**
     * 删除一条聊天消息
     * @param chatMessage 聊天消息实体
     */
    @Delete
    suspend fun deleteChatMessage(chatMessage: ChatMessageRoomEntity)
    
    /**
     * 根据消息ID删除一条聊天消息
     * @param messageId 消息ID
     */
    @Query("DELETE FROM chat_messages WHERE messageId = :messageId")
    suspend fun deleteChatMessageById(messageId: String)
    
    /**
     * 获取所有聊天消息
     * @return 聊天消息流
     */
    @Query("SELECT * FROM chat_messages ORDER BY timestamp DESC")
    suspend fun getAllChatMessages(): List<ChatMessageRoomEntity>

    /**
     * 获取特定用户的所有聊天消息，按时间排序
     * @param currentUserId 当前用户ID
     * @param userId 用户ID
     * @return 聊天消息流
     */
    @Query("SELECT * FROM chat_messages WHERE (senderId = :currentUserId AND receiverId = :userId) OR (senderId = :userId AND receiverId = :currentUserId) ORDER BY timestamp DESC")
    suspend fun getChatMessagesByUserId(currentUserId: String, userId: String): List<ChatMessageRoomEntity>

    /**
     * 根据消息ID获取一条聊天消息
     * @param messageId 消息ID
     * @return 聊天消息实体
     */
    @Query("SELECT * FROM chat_messages WHERE messageId = :messageId")
    suspend fun getChatMessageById(messageId: String): ChatMessageRoomEntity?

    /**
     * 根据融云消息ID更新localPath
     * @param rcMsgId rc消息ID
     * @param localPath 下载到本地的文件路径
     */
    @Query("UPDATE chat_messages SET mediaLocalUri = :localPath WHERE rcMsgId = :rcMsgId")
    suspend fun updateLocalPathByRcId(rcMsgId: String,localPath: String)

    /**
     * 更新状态
     * @param messageId 消息ID
     * @param status 状态
     */
    @Query("UPDATE chat_messages SET status = :status WHERE messageId = :messageId")
    suspend fun updateSendStatus(messageId: String,status: String)

    /**
     * 清空所有聊天消息
     */
    @Query("DELETE FROM chat_messages")
    suspend fun clearAllChatMessages()

    /**
     * 清空当前用户所有聊天消息
     */
    @Query("DELETE FROM chat_messages WHERE currentUserId = :currentUserId")
    suspend fun clearCurrentUserAllChatMessages(currentUserId: String)

    /**
     * 清空当前用户与特定用户之间的所有聊天消息
     * @param currentUserId 当前用户ID
     * @param userId 用户ID
     */
    @Query("DELETE FROM chat_messages WHERE (senderId = :currentUserId AND receiverId = :userId) OR (senderId = :userId AND receiverId = :currentUserId)")
    suspend fun clearChatMessagesByUserId(currentUserId: String, userId: String)

    // ==================== 分页查询接口 ====================


    /**
     * 根据messageId进行分页查询特定用户的聊天消息（向前查询更早的消息）
     * 查询指定messageId之前的消息，限定在特定用户对话中，按时间戳降序排列
     * @param currentUserId 当前用户ID
     * @param userId 对话用户ID
     * @param messageId 起始消息ID，查询此消息之前的消息
     * @param limit 每页数量
     * @return 聊天消息列表
     */
    @Query("SELECT * FROM chat_messages WHERE ((senderId = :currentUserId AND receiverId = :userId) OR (senderId = :userId AND receiverId = :currentUserId)) AND timestamp < (SELECT timestamp FROM chat_messages WHERE messageId = :messageId) ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getChatMessagesBeforeMessageIdByUserId(currentUserId: String, userId: String, messageId: String, limit: Int): List<ChatMessageRoomEntity>

    /**
     * 获取特定用户的最新聊天消息（用于初始化分页）
     * @param currentUserId 当前用户ID
     * @param userId 对话用户ID
     * @param limit 每页数量
     * @return 聊天消息列表
     */
    @Query("SELECT * FROM chat_messages WHERE (senderId = :currentUserId AND receiverId = :userId) OR (senderId = :userId AND receiverId = :currentUserId) ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getLatestChatMessagesByUserId(currentUserId: String, userId: String, limit: Int): List<ChatMessageRoomEntity>

    /**
     * 根据rcMsgId获取聊天消息
     *
     * @param currentUserId 当前用户ID
     * @param userId 对话用户ID
     * @param rcMsgId 融云msgId
     * @return 聊天消息实体列表
     */
    @Query("SELECT * FROM chat_messages WHERE (senderId = :currentUserId AND receiverId = :userId AND rcMsgId = :rcMsgId) OR (senderId = :userId AND receiverId = :currentUserId AND rcMsgId = :rcMsgId) ORDER BY timestamp DESC")
    suspend fun getChatMessagesByRcMsgId(currentUserId: String, userId: String, rcMsgId: String): List<ChatMessageRoomEntity>

}