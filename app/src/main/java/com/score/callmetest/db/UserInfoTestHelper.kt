package com.score.callmetest.db

import android.content.Context
import com.score.callmetest.db.repository.UserInfoRepository
import com.score.callmetest.entity.UserInfoEntity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 用户信息表测试辅助类
 * 用于测试和验证用户信息表的CRUD功能
 */
class UserInfoTestHelper(private val context: Context) {
    
    private val database = AppDatabase.getInstance(context)
    private val userInfoRepository = UserInfoRepository(database.userInfoDao())
    
    /**
     * 执行完整的CRUD测试
     * @param onResult 测试结果回调，true表示所有测试通过，false表示有测试失败
     */
    fun runFullCrudTest(onResult: (Boolean) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Timber.d("开始执行用户信息表CRUD测试")
                
                // 1. 测试插入
                val testUserId = "test_user_${System.currentTimeMillis()}"
                val testUserInfo = UserInfoEntity.create(
                    userId = testUserId,
                    avatarThumbUrl = "https://example.com/thumb.jpg",
                    avatarMiddleThumbUrl = "https://example.com/middle.jpg",
                    avatar = "https://example.com/avatar.jpg",
                    nickName = "测试用户"
                )
                
                userInfoRepository.insertUserInfo(testUserInfo)
                Timber.d("✓ 插入测试通过")
                
                // 2. 测试查询
                val retrievedUserInfo = userInfoRepository.getUserInfoById(testUserId)
                if (retrievedUserInfo == null) {
                    throw Exception("查询失败：用户信息不存在")
                }
                if (retrievedUserInfo.nickName != "测试用户") {
                    throw Exception("查询失败：数据不匹配")
                }
                Timber.d("✓ 查询测试通过")
                
                // 3. 测试更新
                userInfoRepository.updateUserNickName(testUserId, "更新后的昵称")
                val updatedUserInfo = userInfoRepository.getUserInfoById(testUserId)
                if (updatedUserInfo?.nickName != "更新后的昵称") {
                    throw Exception("更新失败：昵称未更新")
                }
                Timber.d("✓ 更新测试通过")
                
                // 4. 测试头像更新
                userInfoRepository.updateUserAvatar(
                    userId = testUserId,
                    avatarThumbUrl = "new_thumb.jpg",
                    avatarMiddleThumbUrl = "new_middle.jpg",
                    avatar = "new_avatar.jpg"
                )
                val avatarUpdatedUserInfo = userInfoRepository.getUserInfoById(testUserId)
                if (avatarUpdatedUserInfo?.avatar != "new_avatar.jpg") {
                    throw Exception("头像更新失败")
                }
                Timber.d("✓ 头像更新测试通过")
                
                // 5. 测试存在性检查
                val exists = userInfoRepository.isUserExists(testUserId)
                if (!exists) {
                    throw Exception("存在性检查失败")
                }
                Timber.d("✓ 存在性检查测试通过")
                
                // 6. 测试删除
                userInfoRepository.deleteUserInfoById(testUserId)
                val deletedUserInfo = userInfoRepository.getUserInfoById(testUserId)
                if (deletedUserInfo != null) {
                    throw Exception("删除失败：用户信息仍然存在")
                }
                Timber.d("✓ 删除测试通过")
                
                Timber.d("所有CRUD测试通过！")
                onResult(true)
                
            } catch (e: Exception) {
                Timber.e(e, "CRUD测试失败")
                onResult(false)
            }
        }
    }
    
    /**
     * 测试批量操作
     * @param onResult 测试结果回调
     */
    fun testBatchOperations(onResult: (Boolean) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Timber.d("开始执行批量操作测试")
                
                // 创建测试数据
                val testUserInfos = (1..5).map { index ->
                    UserInfoEntity.create(
                        userId = "batch_test_user_$index",
                        nickName = "批量测试用户$index",
                        avatar = "avatar_$index.jpg"
                    )
                }
                
                // 批量插入
                userInfoRepository.insertUserInfos(testUserInfos)
                Timber.d("✓ 批量插入测试通过")
                
                // 验证插入结果
                val count = userInfoRepository.getUserInfoCount()
                if (count < 5) {
                    throw Exception("批量插入验证失败：数量不正确")
                }
                Timber.d("✓ 批量插入验证通过，当前总数：$count")
                
                // 批量删除
                val userIds = testUserInfos.map { it.userId }
                userInfoRepository.deleteUserInfosByIds(userIds)
                
                // 验证删除结果
                val remainingUser = userInfoRepository.getUserInfoById("batch_test_user_1")
                if (remainingUser != null) {
                    throw Exception("批量删除失败：用户仍然存在")
                }
                Timber.d("✓ 批量删除测试通过")
                
                Timber.d("批量操作测试通过！")
                onResult(true)
                
            } catch (e: Exception) {
                Timber.e(e, "批量操作测试失败")
                onResult(false)
            }
        }
    }
    
    /**
     * 测试搜索功能
     * @param onResult 测试结果回调
     */
    fun testSearchFunctionality(onResult: (Boolean) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Timber.d("开始执行搜索功能测试")
                
                // 创建测试数据
                val searchTestUsers = listOf(
                    UserInfoEntity.create("search_1", nickName = "张三"),
                    UserInfoEntity.create("search_2", nickName = "张四"),
                    UserInfoEntity.create("search_3", nickName = "李五"),
                    UserInfoEntity.create("search_4", nickName = "王六")
                )
                
                userInfoRepository.insertUserInfos(searchTestUsers)
                
                // 测试搜索
                userInfoRepository.searchUserInfosByNickName("张").collect { results ->
                    if (results.size != 2) {
                        throw Exception("搜索测试失败：结果数量不正确，期望2个，实际${results.size}个")
                    }
                    
                    val hasZhangSan = results.any { it.nickName == "张三" }
                    val hasZhangSi = results.any { it.nickName == "张四" }
                    
                    if (!hasZhangSan || !hasZhangSi) {
                        throw Exception("搜索测试失败：结果内容不正确")
                    }
                    
                    Timber.d("✓ 搜索功能测试通过")
                    
                    // 清理测试数据
                    val testUserIds = searchTestUsers.map { it.userId }
                    userInfoRepository.deleteUserInfosByIds(testUserIds)
                    
                    Timber.d("搜索功能测试通过！")
                    onResult(true)
                }
                
            } catch (e: Exception) {
                Timber.e(e, "搜索功能测试失败")
                onResult(false)
            }
        }
    }
    
    /**
     * 创建示例数据
     * @param count 创建数量
     * @param onComplete 完成回调
     */
    fun createSampleData(count: Int = 10, onComplete: (Boolean) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Timber.d("开始创建示例数据，数量：$count")
                
                val sampleUsers = (1..count).map { index ->
                    UserInfoEntity.create(
                        userId = "sample_user_$index",
                        avatarThumbUrl = "https://example.com/thumb_$index.jpg",
                        avatarMiddleThumbUrl = "https://example.com/middle_$index.jpg",
                        avatar = "https://example.com/avatar_$index.jpg",
                        nickName = "示例用户$index"
                    )
                }
                
                userInfoRepository.insertUserInfos(sampleUsers)
                
                Timber.d("示例数据创建成功！")
                onComplete(true)
                
            } catch (e: Exception) {
                Timber.e(e, "示例数据创建失败")
                onComplete(false)
            }
        }
    }
    
    /**
     * 清理所有测试数据
     * @param onComplete 完成回调
     */
    fun clearAllTestData(onComplete: (Boolean) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                userInfoRepository.clearAllUserInfos()
                Timber.d("所有测试数据清理完成")
                onComplete(true)
            } catch (e: Exception) {
                Timber.e(e, "清理测试数据失败")
                onComplete(false)
            }
        }
    }
}
