package com.score.callmetest.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.ui.splash.SplashActivity
import timber.log.Timber
import java.util.Locale

/**
 * 应用语言管理器
 * 统一管理应用的语言设置，支持用户选择优先，系统语言备用的逻辑
 */
object AppLanguageManager {
    private const val TAG = "AppLanguageManager"
    private const val PREF_KEY_USER_LANGUAGE = "user_selected_language"

    // 语言代码到Locale的映射
    private val languageLocaleMap = mapOf(
        "zh-rTW" to Locale("zh", "TW"), // 繁体中文
        "en" to Locale.ENGLISH,
        "ar" to Locale("ar"),
        "de" to Locale.GERMAN,
        "es" to Locale("es"),
        "fr" to Locale.FRENCH,
        "hi" to Locale("hi"),
        "it" to Locale.ITALIAN,
        "ja" to Locale.JAPANESE,
        "ko" to Locale.KOREAN,
        "pt" to Locale("pt"),
        "ru" to Locale("ru"),
        "th" to Locale("th"),
        "tr" to Locale("tr"),
        "vi" to Locale("vi")
    )

    // 语言代码到显示名称的映射
    private val languageDisplayNames = mapOf<String, String>(
        "zh-rTW" to "繁體中文",
        "en" to "English",
        "ar" to "اللغة العربية",
        "de" to "Deutsch",
        "es" to "Español",
        "fr" to "Français",
        "hi" to "हिन्दी",
        "it" to "Italiano",
        "ja" to "日本語",
        "ko" to "한국어",
        "pt" to "Português",
        "ru" to "Русский",
        "th" to "ไทย",
        "tr" to "Türkçe",
        "vi" to "Tiếng Việt"
    )

    /**
     * 获取当前应用应该使用的语言
     * 优先级：用户选择 > 系统语言 > 英语
     */
    fun getCurrentAppLanguage(context: Context): String {
        // 1. 检查用户是否已经选择过语言
        val userSelectedLanguage = getUserSelectedLanguage(context)
        if (userSelectedLanguage != null && isLanguageSupported(userSelectedLanguage)) {
            Timber.d("使用用户选择的语言: $userSelectedLanguage")
            return userSelectedLanguage
        }
        
        // 2. 使用系统语言
        val systemLanguage = LocaleUtils.getSystemLanguage()
        Timber.d("系统语言: $systemLanguage")
        val supportedSystemLanguage = getSupportedLanguageCode(systemLanguage)
        if (supportedSystemLanguage != null) {
            Timber.d( "映射到支持的语言: $supportedSystemLanguage")
            return supportedSystemLanguage
        }
        
        // 3. 默认使用英语
        Timber.d("使用默认语言: en")
        return "en"
    }

    /**
     * 获取用户选择的语言
     */
    fun getUserSelectedLanguage(context: Context): String? {
        val prefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE)
        return prefs.getString(PREF_KEY_USER_LANGUAGE, null)
    }

    /**
     * 保存用户选择的语言
     */
    fun saveUserSelectedLanguage(context: Context, languageCode: String) {
        val prefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE)
        prefs.edit().putString(PREF_KEY_USER_LANGUAGE, languageCode).apply()
    }

    /**
     * 检查语言是否被支持
     */
    fun isLanguageSupported(languageCode: String): Boolean {
        return languageLocaleMap.containsKey(languageCode)
    }

    /**
     * 将系统语言代码映射到支持的语言代码
     */
    private fun getSupportedLanguageCode(systemLanguage: String): String? {
        // 提取语言代码部分，忽略国家代码（如 ja-JP -> ja）
        val languageCode = if (systemLanguage.contains("-")) {
            systemLanguage.substringBefore("-")
        } else {
            systemLanguage
        }
        
        Timber.d(TAG, "系统语言: $systemLanguage, 提取的语言代码: $languageCode")
        
        val result = when {
            // 所有中文变体都映射到繁体中文
            languageCode.startsWith("zh") -> "zh-rTW" // zh, zh-CN, zh-TW, zh-HK 等都使用繁体
            languageCode == "ar" -> "ar"
            languageCode == "de" -> "de"
            languageCode == "es" -> "es"
            languageCode == "fr" -> "fr"
            languageCode == "hi" -> "hi"
            languageCode == "it" -> "it"
            languageCode == "ja" -> "ja"
            languageCode == "ko" -> "ko"
            languageCode == "pt" -> "pt"
            languageCode == "ru" -> "ru"
            languageCode == "th" -> "th"
            languageCode == "tr" -> "tr"
            languageCode == "vi" -> "vi"
            languageCode == "en" -> "en"
            else -> null
        }
        
        Timber.d(TAG, "语言映射结果: $result")
        return result
    }

    /**
     * 应用语言设置
     */
    fun applyLanguage(context: Context, languageCode: String) {
        if (isLanguageSupported(languageCode)) {
            // 使用改进的语言设置方法
            setAppLanguageImproved(context, languageCode)
        } else {
        }
    }

    /**
     * 改进的语言设置方法
     * 确保语言设置能够正确应用到应用
     */
    private fun setAppLanguageImproved(context: Context, languageCode: String) {
        try {
            val locale = getLocaleForLanguage(languageCode)
            
            // 设置系统默认Locale
            Locale.setDefault(locale)

            // 保存语言设置到SharedPreferences
            val prefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE)
            prefs.edit().putString("app_language", languageCode).apply()

        } catch (e: Exception) {
        }
    }

    /**
     * 获取语言显示名称
     * @param languageCode 语言代码
     * @return 语言的显示名称，如果找不到则返回原始代码
     */
    fun getLanguageDisplayName(languageCode: String): String {
        return languageDisplayNames[languageCode] ?: languageCode
    }

    /**
     * 获取当前应用语言的显示名称
     * @param context 上下文
     * @return 当前语言的显示名称
     */
    fun getCurrentLanguageDisplayName(context: Context): String {
        val currentLanguage = getCurrentAppLanguage(context)
        return getLanguageDisplayName(currentLanguage)
    }

    /**
     * 获取所有支持的语言代码列表
     * @return 支持的语言代码列表
     */
    fun getSupportedLanguageCodes(): List<String> {
        return listOf(
            "zh-rTW", "en", "ar", "de", "es", "fr", "hi", "it", "ja", "ko", 
            "pt", "ru", "th", "tr", "vi"
        )
    }

    /**
     * 获取所有支持的语言显示名称列表
     * @return 支持的语言显示名称列表
     */
    fun getSupportedLanguageDisplayNames(): List<String> {
        return languageDisplayNames.values.toList()
    }

    /**
     * 初始化应用语言
     * 在应用启动时调用，确保使用正确的语言
     */
    fun initializeAppLanguage(context: Context) {
        val currentLanguage = getCurrentAppLanguage(context)
        applyLanguage(context, currentLanguage)
    }

    // ==================== 语言上下文相关方法 ====================

    /**
     * 根据语言代码创建对应的Context
     * @param context 原始Context
     * @param languageCode 语言代码
     * @return 应用了语言设置的Context
     */
    fun createLanguageContext(context: Context, languageCode: String): Context {
        return try {
            val locale = getLocaleForLanguage(languageCode)
            val config = Configuration(context.resources.configuration)
            config.setLocale(locale)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                context.createConfigurationContext(config)
            } else {
                @Suppress("DEPRECATION")
                context.resources.updateConfiguration(config, context.resources.displayMetrics)
                context
            }
        } catch (e: Exception) {
            context
        }
    }

    /**
     * 根据语言代码获取对应的Locale
     * @param languageCode 语言代码
     * @return 对应的Locale，如果找不到则返回默认Locale
     */
    fun getLocaleForLanguage(languageCode: String): Locale {
        return languageLocaleMap[languageCode] ?: run {
            // 特殊处理：如果是zh-rTW格式，解析为zh-TW
            if (languageCode.startsWith("zh-r")) {
                val region = languageCode.substring(4) // 获取TW部分
                Locale("zh", region)
            } else {
                Locale(languageCode)
            }
        }
    }

    // ==================== 语言切换相关方法 ====================

    /**
     * 切换应用语言并重启APP
     * @param activity 当前Activity
     * @param languageCode 目标语言代码
     */
    fun switchLanguageAndRestart(activity: Activity, languageCode: String) {
        try {
            // 保存用户选择的语言
            saveUserSelectedLanguage(activity, languageCode)

            // 重启整个APP以应用新语言
            restartApp(activity)
            
        } catch (e: Exception) {
            Timber.e(e, "语言切换失败: $languageCode")
        }
    }

    /**
     * 重启整个APP
     * @param activity 当前Activity
     */
    private fun restartApp(activity: Activity) {
        try {
            ActivityUtils.startActivityClearTask(activity, SplashActivity::class.java,
                Bundle().apply {
                    putBoolean("isNeedLogin", false)
                }
            )


        } catch (e: Exception) {
            Timber.e(e, "重启APP失败，使用recreate作为备选方案")
            activity.recreate()
        }
    }

    /**
     * 检查当前语言是否与目标语言相同
     * @param context 上下文
     * @param targetLanguageCode 目标语言代码
     * @return 是否相同
     */
    fun isCurrentLanguage(context: Context, targetLanguageCode: String): Boolean {
        val currentLanguage = getCurrentAppLanguage(context)
        return currentLanguage == targetLanguageCode
    }

    /**
     * 获取当前语言的Locale
     * @param context 上下文
     * @return 当前语言的Locale
     */
    fun getCurrentLocale(context: Context): Locale {
        val currentLanguage = getCurrentAppLanguage(context)
        return getLocaleForLanguage(currentLanguage)
    }

    /**
     * 检查当前语言是否为阿拉伯语
     * @param context 上下文
     * @return 是否为阿拉伯语
     */
    fun isCurrentLanguageArabic(context: Context): Boolean {
        val currentLanguage = getCurrentAppLanguage(context)
        return currentLanguage == "ar"
    }

    /**
     * 检查指定语言代码是否为阿拉伯语
     * @param languageCode 语言代码
     * @return 是否为阿拉伯语
     */
    fun isArabicLanguage(languageCode: String): Boolean {
        return languageCode == "ar"
    }

    /**
     * 检查当前语言是否为RTL（从右到左）语言
     * @param context 上下文
     * @return 是否为RTL语言
     */
    fun isCurrentLanguageRTL(context: Context): Boolean {
        val currentLanguage = getCurrentAppLanguage(context)
        return isRTLanguage(currentLanguage)
    }

    /**
     * 检查指定语言代码是否为RTL语言
     * @param languageCode 语言代码
     * @return 是否为RTL语言
     */
    fun isRTLanguage(languageCode: String): Boolean {
        return when (languageCode) {
            "ar" -> true // 阿拉伯语
            "he" -> true // 希伯来语
            "fa" -> true // 波斯语
            "ur" -> true // 乌尔都语
            else -> false
        }
    }

    /**
     * 获取当前语言的文本方向
     * @param context 上下文
     * @return 文本方向，true为RTL，false为LTR
     */
    fun getCurrentTextDirection(context: Context): Boolean {
        return isCurrentLanguageRTL(context)
    }

    /**
     * 获取指定语言的文本方向
     * @param languageCode 语言代码
     * @return 文本方向，true为RTL，false为LTR
     */
    fun getTextDirection(languageCode: String): Boolean {
        return isRTLanguage(languageCode)
    }

}
