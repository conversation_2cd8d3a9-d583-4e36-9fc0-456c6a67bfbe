package com.score.callmetest.util

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.widget.ImageView
import android.os.Handler
import android.os.Looper
import androidx.annotation.DrawableRes
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.load.Transformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.BaseRequestOptions
import com.bumptech.glide.request.target.BitmapImageViewTarget
import com.bumptech.glide.request.target.Target
import timber.log.Timber

object GlideUtils {

    data class DoubleUrl(
        val primary: Any?,
        val fallback: Any? = null,
        var useFallback: Boolean = false
    )

    fun load(
        imageView: ImageView,
        doubleUrl: DoubleUrl,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        radius: Int = 0,
        isCircle: Boolean = false,
        useBlur: Boolean = false,
        blurRadius: Float = 15.0f,
        onResourceReady: (() -> Unit)? = null
    ) {
        if (doubleUrl.primary != null) {
            load(
                context = imageView.context,
                url = doubleUrl.primary,
                imageView = imageView,
                placeholder = placeholder,
                error = error,
                radius = radius,
                isCircle = isCircle,
                useBlur = useBlur,
                blurRadius = blurRadius,
                onRequestListener = object : RequestListener<Bitmap> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Bitmap?>,
                        isFirstResource: Boolean
                    ): Boolean {
                        Handler(Looper.getMainLooper()).post {
                            if (!doubleUrl.useFallback && doubleUrl.fallback != null && doubleUrl.fallback != doubleUrl.primary) {
                                doubleUrl.useFallback = true
                                load(
                                    imageView.context,
                                    doubleUrl.fallback,
                                    imageView,
                                    placeholder,
                                    error,
                                    radius,
                                    isCircle,
                                    useBlur,
                                    blurRadius,
                                    onRequestListener = object : RequestListener<Bitmap> {
                                        override fun onLoadFailed(
                                            e: GlideException?,
                                            model: Any?,
                                            target: Target<Bitmap?>,
                                            isFirstResource: Boolean
                                        ): Boolean {
                                            return false
                                        }

                                        override fun onResourceReady(
                                            resource: Bitmap,
                                            model: Any,
                                            target: Target<Bitmap>?,
                                            dataSource: DataSource,
                                            isFirstResource: Boolean
                                        ): Boolean {
                                            Handler(Looper.getMainLooper()).post {
                                                onResourceReady?.invoke()
                                            }
                                            return false
                                        }
                                    }
                                )
                            }
                        }
                        return false
                    }

                    override fun onResourceReady(
                        resource: Bitmap,
                        model: Any,
                        target: Target<Bitmap>?,
                        dataSource: DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        Handler(Looper.getMainLooper()).post {
                            onResourceReady?.invoke()
                        }
                        return false
                    }
                }
            )
        } else if (doubleUrl.fallback != null) {
            load(
                imageView.context,
                doubleUrl.fallback,
                imageView,
                placeholder,
                error,
                radius,
                isCircle,
                useBlur,
                blurRadius,
                onRequestListener = object : RequestListener<Bitmap> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Bitmap?>,
                        isFirstResource: Boolean
                    ): Boolean {
                        return false
                    }

                    override fun onResourceReady(
                        resource: Bitmap,
                        model: Any,
                        target: Target<Bitmap>?,
                        dataSource: DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        Handler(Looper.getMainLooper()).post {
                            onResourceReady?.invoke()
                        }
                        return false
                    }
                }
            )
        }
    }

    /**
     * 加载图片（支持url、资源id、文件），可选圆角、圆形、占位图、错误图
     * 新增：自动补足图片为目标ImageView的宽高比，避免内容被裁剪
     * 新增：支持模糊效果
     */
    @SuppressLint("CheckResult")
    fun load(
        context: Context,
        url: Any?,
        imageView: ImageView,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        radius: Int = 0,
        isCircle: Boolean = false,
        useBlur: Boolean = false,
        blurRadius: Float = 15.0f,
        onRequestListener: RequestListener<Bitmap>? = null,
        vararg extraTransform: Transformation<Bitmap>
    ) {
        val request = Glide.with(context).asBitmap().load(url)
        placeholder?.let { request.placeholder(it) }
        error?.let { request.error(it) }
        request.diskCacheStrategy(DiskCacheStrategy.ALL)
        
        // 创建自定义的RequestListener来处理模糊效果
        val blurListener = if (useBlur) {
            object : RequestListener<Bitmap> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Bitmap?>,
                    isFirstResource: Boolean
                ): Boolean {
                    // 调用原始的onRequestListener
                    onRequestListener?.onLoadFailed(e, model, target, isFirstResource)
                    return false
                }

                override fun onResourceReady(
                    resource: Bitmap,
                    model: Any,
                    target: Target<Bitmap>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    // 应用高质量模糊效果 - 使用Handler确保在主线程执行
                    Handler(Looper.getMainLooper()).post {
                        try {
                            Timber.tag("GlideUtils").d("开始应用高质量模糊效果，模糊半径: $blurRadius，图片尺寸: ${resource.width}x${resource.height}")
                            val blurredBitmap = BlurUtils.blurBitmap(context, resource, blurRadius)
                            if (blurredBitmap != null) {
                                Timber.tag("GlideUtils").d("高质量模糊效果应用成功，模糊后尺寸: ${blurredBitmap.width}x${blurredBitmap.height}")
                                imageView.setImageBitmap(blurredBitmap)
                            } else {
                                Timber.tag("GlideUtils").w("模糊处理返回null，使用原图")
                                imageView.setImageBitmap(resource)
                            }
                        } catch (e: Exception) {
                            Timber.tag("GlideUtils").e("模糊处理失败: ${e.message}，使用原图", e)
                            // 模糊处理失败时使用原图
                            imageView.setImageBitmap(resource)
                        }
                    }
                    
                    // 调用原始的onRequestListener
                    onRequestListener?.onResourceReady(resource, model, target, dataSource, isFirstResource)
                    return true // 拦截默认处理，因为我们已经手动设置了图片
                }
            }
        } else {
            onRequestListener
        }
        
        val options = when {
            isCircle -> RequestOptions().transform(CircleCrop(), *extraTransform)
            radius > 0 -> RequestOptions().transform(RoundedCorners(radius), *extraTransform)
            else -> RequestOptions().transform(*extraTransform) // 不做任何裁剪或缩放变换
        }
        request.apply(options).listener(blurListener).into(imageView)
    }

    fun load(
        view: ImageView,
        url: Any?,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        radius: Int = 0,
        isCircle: Boolean = false,
        useBlur: Boolean = false,
        blurRadius: Float = 15.0f
    ) = load(view.context, url, view, placeholder, error, radius, isCircle, useBlur, blurRadius)

    /**
     * @param uriList 失败后依次加载，都失败加载error，成功一次就不再加载
     *
     * 使用URI列表进行回退加载
     */
    fun loadWithFallbackList(
        view: ImageView,
        uriList: List<Any?>,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        radius: Int = 0,
        isCircle: Boolean = false,
        useBlur: Boolean = false,
        blurRadius: Float = 15.0f,
        onSuccess: (() -> Unit)? = null,
        onFailure: (() -> Unit)? = null,
        currentIndex: Int = 0
    ) {
        if (currentIndex >= uriList.size) {
            // 所有URI都失败了
            error?.let { view.setImageResource(it) }
            onFailure?.invoke()
            return
        }

        val currentUri = uriList[currentIndex]
        val isLastAttempt = currentIndex == uriList.size - 1

        // 检查currentUri是否空
        if (currentUri == null || (currentUri is String && currentUri.isEmpty())
                || (currentUri is Int && currentUri == 0)) {
            // 尝试下一个URI
            view.post{
                loadWithFallbackList(
                    view, uriList, placeholder, error, radius, isCircle,
                    useBlur, blurRadius, onSuccess, onFailure, currentIndex + 1
                )
            }
            return
        }

        load(
            context = view.context,
            url = currentUri,
            imageView = view,
            placeholder = placeholder,
            error = if (isLastAttempt) error else null, // 只在最后一次尝试时显示错误图
            radius = radius,
            isCircle = isCircle,
            useBlur = useBlur,
            blurRadius = blurRadius,
            onRequestListener = object : RequestListener<Bitmap> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Bitmap?>,
                    isFirstResource: Boolean
                ): Boolean {
                    // 尝试下一个URI
                    view.post{
                        loadWithFallbackList(
                            view, uriList, placeholder, error, radius, isCircle,
                            useBlur, blurRadius, onSuccess, onFailure, currentIndex + 1
                        )
                    }
                    return true // 拦截默认错误处理
                }

                override fun onResourceReady(
                    resource: Bitmap,
                    model: Any,
                    target: Target<Bitmap>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    onSuccess?.invoke()
                    return false
                }
            }
        )
    }

    /**
     * 便捷方法：加载图片并应用模糊效果
     */
    fun loadWithBlur(
        imageView: ImageView,
        url: Any?,
        blurRadius: Float = 15.0f,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        onResourceReady: (() -> Unit)? = null
    ) {
        load(
            context = imageView.context,
            url = url,
            imageView = imageView,
            placeholder = placeholder,
            error = error,
            useBlur = true,
            blurRadius = blurRadius,
            onRequestListener = object : RequestListener<Bitmap> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Bitmap?>,
                    isFirstResource: Boolean
                ): Boolean = false

                override fun onResourceReady(
                    resource: Bitmap,
                    model: Any,
                    target: Target<Bitmap>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    onResourceReady?.invoke()
                    return false
                }
            }
        )
    }

    /**
     * 便捷方法：加载DoubleUrl并应用模糊效果
     */
    fun loadWithBlur(
        imageView: ImageView,
        doubleUrl: DoubleUrl,
        blurRadius: Float = 15.0f,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        onResourceReady: (() -> Unit)? = null
    ) {
        load(
            imageView = imageView,
            doubleUrl = doubleUrl,
            placeholder = placeholder,
            error = error,
            useBlur = true,
            blurRadius = blurRadius,
            onResourceReady = onResourceReady
        )
    }

    /**
     * 便捷方法：加载图片并应用强模糊效果（半径50.0f）
     */
    fun loadWithStrongBlur(
        imageView: ImageView,
        url: Any?,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        onResourceReady: (() -> Unit)? = null
    ) {
        loadWithBlur(imageView, url, 50.0f, placeholder, error, onResourceReady)
    }

    /**
     * 便捷方法：加载DoubleUrl并应用强模糊效果（半径50.0f）
     */
    fun loadWithStrongBlur(
        imageView: ImageView,
        doubleUrl: DoubleUrl,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        onResourceReady: (() -> Unit)? = null
    ) {
        loadWithBlur(imageView, doubleUrl, 50.0f, placeholder, error, onResourceReady)
    }

    /**
     * 便捷方法：加载图片并应用极强模糊效果（半径100.0f）
     */
    fun loadWithExtremeBlur(
        imageView: ImageView,
        url: Any?,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        onResourceReady: (() -> Unit)? = null
    ) {
        loadWithBlur(imageView, url, 100.0f, placeholder, error, onResourceReady)
    }

    /**
     * 便捷方法：加载DoubleUrl并应用极强模糊效果（半径100.0f）
     */
    fun loadWithExtremeBlur(
        imageView: ImageView,
        doubleUrl: DoubleUrl,
        @DrawableRes placeholder: Int? = null,
        @DrawableRes error: Int? = null,
        onResourceReady: (() -> Unit)? = null
    ) {
        loadWithBlur(imageView, doubleUrl, 100.0f, placeholder, error, onResourceReady)
    }

    /**
     * 清除图片缓存
     */
    fun clear(context: Context, imageView: ImageView) {
        Glide.with(context).clear(imageView)
    }
} 