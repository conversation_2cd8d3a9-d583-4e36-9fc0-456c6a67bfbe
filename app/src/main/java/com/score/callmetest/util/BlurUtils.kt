package com.score.callmetest.util

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.renderscript.Allocation
import android.renderscript.Element
import android.renderscript.RenderScript
import android.renderscript.ScriptIntrinsicBlur
import android.view.View
import android.widget.ImageView
import androidx.annotation.RequiresApi
import timber.log.Timber

/**
 * 高斯模糊工具类
 * 用于对View或Bitmap进行高斯模糊处理
 */
object BlurUtils {
    
    private const val TAG = "BlurUtils"
    
    /**
     * 对View进行高斯模糊处理
     * @param view 需要模糊的View
     * @param radius 模糊半径 (0.0f - 25.0f)
     * @param scale 缩放比例，用于提高性能
     * @return 模糊后的Bitmap
     */
    fun blurView(view: View, radius: Float = 15.0f, scale: Float = 0.1f): Bitmap? {
        return try {
            // 创建View的Bitmap
            val bitmap = createBitmapFromView(view, scale)
            // 对Bitmap进行模糊处理
            blurBitmap(view.context, bitmap, radius)
        } catch (e: Exception) {
            Timber.tag(TAG).e("模糊处理失败: ${e.message}")
            null
        }
    }
    
    /**
     * 对Bitmap进行高斯模糊处理
     * @param context 上下文
     * @param bitmap 需要模糊的Bitmap
     * @param radius 模糊半径 (0.0f - 100.0f，超过25.0f时使用多次模糊)
     * @return 模糊后的Bitmap
     */
    fun blurBitmap(context: Context, bitmap: Bitmap, radius: Float = 15.0f): Bitmap? {
        return try {
            Timber.tag(TAG).d("开始模糊处理，图片尺寸: ${bitmap.width}x${bitmap.height}，模糊半径: $radius")
            
            val result = if (radius > 25.0f) {
                // 对于超过25.0f的半径，使用高质量单次模糊算法
                blurBitmapHighQuality(context, bitmap, radius)
            } else if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN_MR1) {
                blurBitmapWithRenderScript(context, bitmap, radius)
            } else {
                // 对于低版本Android，使用简单的模糊算法
                blurBitmapSimple(bitmap, radius)
            }
            
            Timber.tag(TAG).d("模糊处理完成，结果: ${if (result != null) "成功" else "失败"}")
            result
        } catch (e: Exception) {
            Timber.tag(TAG).e("Bitmap模糊处理失败: ${e.message}", e)
            null
        }
    }
    
    /**
     * 使用直接操作像素的高斯模糊算法（支持任意半径）
     */
    private fun blurBitmapHighQuality(context: Context, bitmap: Bitmap, radius: Float): Bitmap? {
        return try {
            Timber.tag(TAG).d("使用像素级高斯模糊，半径: $radius")
            
            val width = bitmap.width
            val height = bitmap.height
            
            // 为了性能，先缩小图片
            val scale = when {
                radius > 100.0f -> 0.125f  // 缩小到1/8
                radius > 50.0f -> 0.25f    // 缩小到1/4
                radius > 25.0f -> 0.5f     // 缩小到1/2
                else -> 1.0f               // 不缩放
            }
            
            val scaledWidth = (width * scale).toInt()
            val scaledHeight = (height * scale).toInt()
            val scaledRadius = radius * scale
            
            Timber.tag(TAG).d("缩放图片: ${width}x${height} -> ${scaledWidth}x${scaledHeight}, 缩放半径: $scaledRadius")
            
            // 创建缩放后的图片
            val scaledBitmap = Bitmap.createScaledBitmap(bitmap, scaledWidth, scaledHeight, true)
            
            // 使用像素级高斯模糊
            val blurredScaled = blurBitmapPixelLevel(scaledBitmap, scaledRadius)
            
            if (blurredScaled != null) {
                // 将模糊后的图片放大回原始尺寸
                val finalBitmap = Bitmap.createScaledBitmap(blurredScaled, width, height, true)
                
                // 清理中间图片
                scaledBitmap.recycle()
                blurredScaled.recycle()
                
                Timber.tag(TAG).d("像素级模糊处理完成")
                finalBitmap
            } else {
                scaledBitmap.recycle()
                null
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("像素级模糊处理失败: ${e.message}", e)
            null
        }
    }
    
    /**
     * 直接操作像素的高斯模糊算法
     */
    private fun blurBitmapPixelLevel(bitmap: Bitmap, radius: Float): Bitmap? {
        return try {
            val width = bitmap.width
            val height = bitmap.height
            
            // 创建可变的Bitmap副本
            val blurredBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true) ?: return null
            
            // 获取像素数组
            val pixels = IntArray(width * height)
            blurredBitmap.getPixels(pixels, 0, width, 0, 0, width, height)
            
            // 应用高斯模糊
            val blurredPixels = gaussianBlur(pixels, width, height, radius)
            
            // 设置模糊后的像素
            blurredBitmap.setPixels(blurredPixels, 0, width, 0, 0, width, height)
            
            Timber.tag(TAG).d("像素级高斯模糊完成")
            blurredBitmap
        } catch (e: Exception) {
            Timber.tag(TAG).e("像素级高斯模糊失败: ${e.message}", e)
            null
        }
    }
    
    /**
     * 高斯模糊核心算法
     */
    private fun gaussianBlur(pixels: IntArray, width: Int, height: Int, radius: Float): IntArray {
        val result = pixels.copyOf()
        
        // 计算高斯核
        val kernel = createGaussianKernel(radius)
        val kernelSize = kernel.size
        val halfKernel = kernelSize / 2
        
        // 水平模糊
        for (y in 0 until height) {
            for (x in 0 until width) {
                var r = 0.0
                var g = 0.0
                var b = 0.0
                var a = 0.0
                var weightSum = 0.0
                
                for (i in 0 until kernelSize) {
                    val sampleX = x + i - halfKernel
                    // 使用边缘扩展而不是边界限制，避免裁切边沿
                    val clampedX = when {
                        sampleX < 0 -> 0
                        sampleX >= width -> width - 1
                        else -> sampleX
                    }
                    val pixel = pixels[y * width + clampedX]
                    val weight = kernel[i]
                    
                    r += ((pixel shr 16) and 0xFF) * weight
                    g += ((pixel shr 8) and 0xFF) * weight
                    b += (pixel and 0xFF) * weight
                    a += ((pixel shr 24) and 0xFF) * weight
                    weightSum += weight
                }
                
                if (weightSum > 0) {
                    r /= weightSum
                    g /= weightSum
                    b /= weightSum
                    a /= weightSum
                }
                
                result[y * width + x] = ((a.toInt() and 0xFF) shl 24) or
                        ((r.toInt() and 0xFF) shl 16) or
                        ((g.toInt() and 0xFF) shl 8) or
                        (b.toInt() and 0xFF)
            }
        }
        
        // 垂直模糊
        val temp = result.copyOf()
        for (x in 0 until width) {
            for (y in 0 until height) {
                var r = 0.0
                var g = 0.0
                var b = 0.0
                var a = 0.0
                var weightSum = 0.0
                
                for (i in 0 until kernelSize) {
                    val sampleY = y + i - halfKernel
                    // 使用边缘扩展而不是边界限制，避免裁切边沿
                    val clampedY = when {
                        sampleY < 0 -> 0
                        sampleY >= height -> height - 1
                        else -> sampleY
                    }
                    val pixel = temp[clampedY * width + x]
                    val weight = kernel[i]
                    
                    r += ((pixel shr 16) and 0xFF) * weight
                    g += ((pixel shr 8) and 0xFF) * weight
                    b += (pixel and 0xFF) * weight
                    a += ((pixel shr 24) and 0xFF) * weight
                    weightSum += weight
                }
                
                if (weightSum > 0) {
                    r /= weightSum
                    g /= weightSum
                    b /= weightSum
                    a /= weightSum
                }
                
                result[y * width + x] = ((a.toInt() and 0xFF) shl 24) or
                        ((r.toInt() and 0xFF) shl 16) or
                        ((g.toInt() and 0xFF) shl 8) or
                        (b.toInt() and 0xFF)
            }
        }
        
        return result
    }
    
    /**
     * 创建高斯核
     */
    private fun createGaussianKernel(radius: Float): DoubleArray {
        val sigma = radius / 3.0  // 3-sigma规则
        val kernelSize = (radius * 2 + 1).toInt().coerceAtMost(127)  // 限制核大小
        val kernel = DoubleArray(kernelSize)
        val halfKernel = kernelSize / 2
        
        var sum = 0.0
        for (i in 0 until kernelSize) {
            val x = i - halfKernel
            val value = Math.exp(-(x * x) / (2 * sigma * sigma))
            kernel[i] = value
            sum += value
        }
        
        // 归一化
        for (i in 0 until kernelSize) {
            kernel[i] /= sum
        }
        
        return kernel
    }
    
    /**
     * 使用多次模糊来达到更强的模糊效果（用于半径超过25.0f的情况）
     * 注意：这个方法性能较差，建议使用 blurBitmapHighQuality
     */
    private fun blurBitmapMultiplePasses(context: Context, bitmap: Bitmap, radius: Float): Bitmap? {
        return try {
            Timber.tag(TAG).d("使用多次模糊处理，目标半径: $radius")
            
            // 计算需要的模糊次数，每次使用25.0f的半径
            val passes = (radius / 25.0f).toInt() + 1
            val radiusPerPass = 25.0f
            
            Timber.tag(TAG).d("将进行 $passes 次模糊，每次半径: $radiusPerPass")
            
            var currentBitmap = bitmap
            repeat(passes) { pass ->
                val blurred = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN_MR1) {
                    blurBitmapWithRenderScript(context, currentBitmap, radiusPerPass)
                } else {
                    blurBitmapSimple(currentBitmap, radiusPerPass)
                }
                
                if (blurred != null) {
                    // 如果不是第一次模糊，释放之前的bitmap
                    if (pass > 0) {
                        currentBitmap.recycle()
                    }
                    currentBitmap = blurred
                    Timber.tag(TAG).d("完成第 ${pass + 1} 次模糊")
                } else {
                    Timber.tag(TAG).e("第 ${pass + 1} 次模糊失败")
                    return null
                }
            }
            
            Timber.tag(TAG).d("多次模糊处理完成")
            currentBitmap
        } catch (e: Exception) {
            Timber.tag(TAG).e("多次模糊处理失败: ${e.message}", e)
            null
        }
    }
    
    /**
     * 使用RenderScript进行高斯模糊（API 17+）
     */
    @RequiresApi(17)
    private fun blurBitmapWithRenderScript(context: Context, bitmap: Bitmap, radius: Float): Bitmap? {
        var renderScript: RenderScript? = null
        var input: Allocation? = null
        var output: Allocation? = null
        var scriptIntrinsicBlur: ScriptIntrinsicBlur? = null
        
        try {
            Timber.tag(TAG).d("使用RenderScript进行模糊处理")
            renderScript = RenderScript.create(context)
            input = Allocation.createFromBitmap(renderScript, bitmap)
            output = Allocation.createTyped(renderScript, input.type)
            scriptIntrinsicBlur = ScriptIntrinsicBlur.create(renderScript, Element.U8_4(renderScript))
            
            val clampedRadius = radius.coerceIn(0.0f, 25.0f)
            Timber.tag(TAG).d("设置模糊半径: $clampedRadius (原始半径: $radius)")
            scriptIntrinsicBlur.setRadius(clampedRadius)
            scriptIntrinsicBlur.setInput(input)
            scriptIntrinsicBlur.forEach(output)
            
            // 创建可变的Bitmap副本
            val blurredBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true) ?: run {
                Timber.tag(TAG).e("无法创建Bitmap副本")
                return null
            }
            output.copyTo(blurredBitmap)
            
            Timber.tag(TAG).d("RenderScript模糊处理成功")
            return blurredBitmap
        } catch (e: Exception) {
            Timber.tag(TAG).e("RenderScript模糊处理失败: ${e.message}", e)
            return null
        } finally {
            // 清理资源
            renderScript?.destroy()
            input?.destroy()
            output?.destroy()
            scriptIntrinsicBlur?.destroy()
        }
    }
    
    /**
     * 简单的模糊算法（用于低版本Android）
     */
    private fun blurBitmapSimple(bitmap: Bitmap, radius: Float): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        // 检查Bitmap尺寸
        if (width <= 0 || height <= 0) {
            Timber.tag(TAG).e("Bitmap尺寸无效: width=$width, height=$height")
            return bitmap
        }
        
        val blurredBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true) ?: run {
            Timber.tag(TAG).e("无法创建Bitmap副本，使用原始Bitmap")
            return bitmap
        }
        
        val pixels = IntArray(width * height)
        blurredBitmap.getPixels(pixels, 0, width, 0, 0, width, height)
        
        val kernelSize = (radius * 2 + 1).toInt()
        val kernel = createGaussianKernel(kernelSize, radius)
        
        val newPixels = IntArray(width * height)
        
        for (y in 0 until height) {
            for (x in 0 until width) {
                var r = 0
                var g = 0
                var b = 0
                var a = 0
                var weightSum = 0f
                
                for (ky in 0 until kernelSize) {
                    for (kx in 0 until kernelSize) {
                        val sampleX = x + kx - kernelSize / 2
                        val sampleY = y + ky - kernelSize / 2
                        // 使用边缘扩展而不是边界限制，避免裁切边沿
                        val px = when {
                            sampleX < 0 -> 0
                            sampleX >= width -> width - 1
                            else -> sampleX
                        }
                        val py = when {
                            sampleY < 0 -> 0
                            sampleY >= height -> height - 1
                            else -> sampleY
                        }
                        val pixelIndex = py * width + px
                        
                        // 检查像素索引是否有效
                        if (pixelIndex >= 0 && pixelIndex < pixels.size) {
                            val pixel = pixels[pixelIndex]
                            val weight = kernel[ky * kernelSize + kx]
                            
                            r += (((pixel shr 16) and 0xFF) * weight).toInt()
                            g += (((pixel shr 8) and 0xFF) * weight).toInt()
                            b += ((pixel and 0xFF) * weight).toInt()
                            a += (((pixel shr 24) and 0xFF) * weight).toInt()
                            weightSum += weight
                        }
                    }
                }
                
                // 避免除零错误
                if (weightSum > 0) {
                    r = (r / weightSum).toInt().coerceIn(0, 255)
                    g = (g / weightSum).toInt().coerceIn(0, 255)
                    b = (b / weightSum).toInt().coerceIn(0, 255)
                    a = (a / weightSum).toInt().coerceIn(0, 255)
                } else {
                    // 如果权重和为0，使用原始像素值
                    val originalPixel = pixels[y * width + x]
                    r = (originalPixel shr 16) and 0xFF
                    g = (originalPixel shr 8) and 0xFF
                    b = originalPixel and 0xFF
                    a = (originalPixel shr 24) and 0xFF
                }
                
                newPixels[y * width + x] = (a shl 24) or (r shl 16) or (g shl 8) or b
            }
        }
        
        blurredBitmap.setPixels(newPixels, 0, width, 0, 0, width, height)
        return blurredBitmap
    }
    
    /**
     * 创建高斯核
     */
    private fun createGaussianKernel(size: Int, sigma: Float): FloatArray {
        val kernel = FloatArray(size * size)
        val center = size / 2
        var sum = 0f
        
        for (y in 0 until size) {
            for (x in 0 until size) {
                val dx = x - center
                val dy = y - center
                val distance = dx * dx + dy * dy
                val value = kotlin.math.exp(-distance / (2 * sigma * sigma))
                kernel[y * size + x] = value
                sum += value
            }
        }
        
        // 归一化
        for (i in kernel.indices) {
            kernel[i] /= sum
        }
        
        return kernel
    }
    
    /**
     * 从View创建Bitmap
     */
    private fun createBitmapFromView(view: View, scale: Float): Bitmap {
        val width = (view.width * scale).toInt()
        val height = (view.height * scale).toInt()
        
        if (width <= 0 || height <= 0) {
            throw IllegalArgumentException("View尺寸无效: width=$width, height=$height")
        }
        
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        // 缩放Canvas
        canvas.scale(scale, scale)
        
        // 绘制View到Canvas
        view.draw(canvas)
        
        return bitmap
    }
    
    /**
     * 对ImageView设置模糊效果
     * @param imageView 目标ImageView
     * @param originalBitmap 原始Bitmap
     * @param radius 模糊半径
     */
    fun setBlurredImage(imageView: ImageView, originalBitmap: Bitmap, radius: Float = 15.0f) {
        try {
            val blurredBitmap = blurBitmap(imageView.context, originalBitmap, radius)
            if (blurredBitmap != null) {
                imageView.setImageBitmap(blurredBitmap)
                Timber.tag(TAG).d("成功设置模糊图片")
            } else {
                Timber.tag(TAG).w("模糊处理失败，使用原始图片")
                imageView.setImageBitmap(originalBitmap)
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("设置模糊图片失败: ${e.message}")
            imageView.setImageBitmap(originalBitmap)
        }
    }
    
    /**
     * 对View应用模糊遮罩效果
     * @param view 需要模糊的View
     * @param radius 模糊半径
     * @param overlayAlpha 遮罩透明度 (0-255)
     */
    fun applyBlurOverlay(view: View, radius: Float = 15.0f, overlayAlpha: Int = 128) {
        try {
            val blurredBitmap = blurView(view, radius)
            if (blurredBitmap != null) {
                // 创建带透明度的遮罩
                val overlayBitmap = Bitmap.createBitmap(
                    blurredBitmap.width,
                    blurredBitmap.height,
                    Bitmap.Config.ARGB_8888
                )
                val canvas = Canvas(overlayBitmap)
                val paint = Paint().apply {
                    alpha = overlayAlpha
                }
                canvas.drawBitmap(blurredBitmap, 0f, 0f, paint)
                
                // 这里可以根据需要设置遮罩效果
                Timber.tag(TAG).d("成功应用模糊遮罩效果")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("应用模糊遮罩失败: ${e.message}")
        }
    }
}
