package com.score.callmetest.util.keyboard

import android.annotation.SuppressLint
import android.app.Activity
import android.content.res.Configuration
import android.graphics.Point
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.widget.PopupWindow
import com.score.callmetest.R
import timber.log.Timber
import java.lang.ref.WeakReference
import androidx.core.graphics.drawable.toDrawable

/**
 * Keyboard height implementation using PopupWindow
 * <AUTHOR>
 */
class KeyboardHeightPopupImpl: PopupWindow, KeyboardHeightPresenter {

    companion object {
        private const val TAG = "KeyboardHeightPopupImpl"
        private const val KEYBOARD_OPEN_THRESHOLD = 100
    }

    /** The view that is used to calculate the keyboard height */
    private val popupView: View

    /** The parent view */
    private val parentView: View

    private var mNavigationBarHeight = 0
    private var preKeyboardHeight = 0
    private var mGlobalLayoutListener: ViewTreeObserver.OnGlobalLayoutListener? = null
    private var isStart = false
    private var mKeyboardChangeAction: KeyboardChangeAction? = null

    private val activity: Activity

    constructor(acty: Activity): super(acty) {
        Timber.tag(TAG).d("KeyboardHeightPopupImpl initialized")
        activity = acty

        val inflater = acty.getSystemService(Activity.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        popupView = inflater.inflate(R.layout.rc_keyboard_popupwindow, null, false)
        contentView = popupView

        softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE or
                WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE
        inputMethodMode = PopupWindow.INPUT_METHOD_NEEDED

        parentView = acty.findViewById(android.R.id.content)

        width = 0
        height = WindowManager.LayoutParams.MATCH_PARENT
    }

    /**
     * Retry showing popup window with delay
     */
    private fun retryShowPopup(parent: View, gravity: Int, x: Int, y: Int) {
        // FIXME: 2021/10/11 在部分机型上 popupWindow 立刻显示会和键盘重合，500ms 之后重新测量一次
        if (!isStart) {
            return
        }
        dismiss()
        mGlobalLayoutListener?.let { listener ->
            popupView.viewTreeObserver.addOnGlobalLayoutListener(listener)
        }
        super.showAtLocation(parent, gravity, x, y)
    }

    override fun showAtLocation(parent: View, gravity: Int, x: Int, y: Int) {
        if (activity.isFinishing || activity.isDestroyed) {
            return
        }
        
        try {
            super.showAtLocation(parent, gravity, x, y)
        } catch (e: Exception) {
            // Ignore exception
        }
        
        popupView.postDelayed({
            if (activity.isFinishing || activity.isDestroyed) {
                return@postDelayed
            }
            try {
                retryShowPopup(parent, gravity, x, y)
            } catch (e: Exception) {
                Timber.tag(TAG).e(e.message)
                popupView.postDelayed({
                    if (activity.isFinishing || activity.isDestroyed) {
                        return@postDelayed
                    }
                    try {
                        retryShowPopup(parent, gravity, x, y)
                    } catch (exception: Exception) {
                        // Ignore exception
                    }
                }, 1000)
            }
        }, 500)
    }

    /**
     * Start the KeyboardHeightProvider, this must be called after the onResume of the Activity.
     * PopupWindows are not allowed to be registered before the onResume has finished of the
     * Activity.
     */
    override fun start() {
        if (!isShowing) {
            isStart = true
            if (mGlobalLayoutListener == null) {
                mGlobalLayoutListener = ViewTreeObserver.OnGlobalLayoutListener {
                    // 分屏模式下不响应
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N || !activity.isInMultiWindowMode) {
                        handleOnGlobalLayout()
                    }
                }
            }
            mGlobalLayoutListener?.let { listener ->
                popupView.viewTreeObserver.addOnGlobalLayoutListener(listener)
            }
            setBackgroundDrawable(0.toDrawable())
            showAtLocation(parentView, Gravity.NO_GRAVITY, 0, 0)
        }
    }

    /** Close the keyboard height provider, this provider will not be used anymore. */
    override fun stop() {
        isStart = false
        mKeyboardChangeAction?.let { action ->
            popupView.removeCallbacks(action)
        }
        // 移除mGlobalLayoutListener，防止内存泄露、减少FD句柄占用
        mGlobalLayoutListener?.let { listener ->
            popupView.viewTreeObserver.removeOnGlobalLayoutListener(listener)
        }
        dismiss()
    }

    /**
     * Set the keyboard height observer to this provider. The observer will be notified when the
     * keyboard height has changed. For example when the keyboard is opened or closed.
     *
     * @param observer The observer to be added to this provider.
     */
    override fun setKeyboardHeightObserver(observer: KeyboardHeightObserver?) {
        observer?.let {
            mKeyboardChangeAction = KeyboardChangeAction(it)
        }
    }

    /**
     * Popup window itself is as big as the window of the Activity. The keyboard can then be
     * calculated by extracting the popup view bottom from the activity window height.
     */
    private fun handleOnGlobalLayout() {
        val screenSize = Point()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            activity.windowManager.currentWindowMetrics.bounds.let {
                screenSize.x = it.width()
                screenSize.y = it.height()
            }
        }else {
            activity.windowManager.defaultDisplay.getSize(screenSize)
        }

        val rect = Rect()
        popupView.getWindowVisibleDisplayFrame(rect)

        // REMIND, you may like to change this using the fullscreen size of the phone
        // and also using the status bar and navigation bar heights of the phone to calculate
        // the keyboard height. But this worked fine on a Nexus.
        val orientation = getScreenOrientation()
        var keyboardHeight = screenSize.y - rect.bottom - mNavigationBarHeight

        Timber.d("keyboardHeight===popup")
        Timber.d("mNavigationBarHeight:$mNavigationBarHeight")
        Timber.d("keyboardHeight:$keyboardHeight")

        // 当权限弹窗弹出的时候，有的机型会重新layout，导致rect.bottom=0或过低，keyboardHeight计算错误
        // keyboardHeight大小如果大于屏幕一半高度的话，则视为不是键盘
        if(keyboardHeight >= screenSize.y / 2){
            keyboardHeight = 0
        }

        // 首次进入的时候，记录底部空白高度
        if(mNavigationBarHeight == 0){
            mNavigationBarHeight = screenSize.y - rect.bottom
        }
        
        when {
            keyboardHeight == 0 -> notifyKeyboardHeightChanged(0, orientation)
            orientation == Configuration.ORIENTATION_PORTRAIT -> notifyKeyboardHeightChanged(keyboardHeight, orientation)
            else -> notifyKeyboardHeightChanged(keyboardHeight, orientation)
        }
    }

    /**
     * Get current screen orientation
     */
    private fun getScreenOrientation(): Int {
        return activity.resources.configuration.orientation
    }

    /**
     * Notify observer about keyboard height changes
     */
    private fun notifyKeyboardHeightChanged(height: Int, orientation: Int) {
        if(height < 0){
            return
        }

        if (preKeyboardHeight == height) {
            return
        }
        preKeyboardHeight = height

        mKeyboardChangeAction?.let { action ->
            popupView.removeCallbacks(action)
            action.orientation = orientation
            action.isOpen = height >= KEYBOARD_OPEN_THRESHOLD && height > mNavigationBarHeight
            action.keyboardHeight = height
            popupView.postDelayed(action, 100)
        }
    }

    /**
     * Runnable class for handling keyboard change events
     */
    private class KeyboardChangeAction(observer: KeyboardHeightObserver) : Runnable {
        private val observer: WeakReference<KeyboardHeightObserver> = WeakReference(observer)
        var orientation: Int = 0
        var isOpen: Boolean = false
        var keyboardHeight: Int = 0

        override fun run() {
            observer.get()?.onKeyboardHeightChanged(orientation, isOpen, keyboardHeight)
        }
    }
}
