package com.score.callmetest.util

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import android.view.TextureView
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import com.score.callmetest.CallmeApplication
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.manager.LogReportManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.JoinChannelRequest
import com.score.callmetest.network.LiveCallAction
import com.score.callmetest.network.LiveCallExt
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.UpdateAgoraUidRequest
import io.agora.rtc2.Constants
import io.agora.rtc2.IRtcEngineEventHandler
import io.agora.rtc2.RtcEngine
import io.agora.rtc2.video.VideoCanvas
import io.agora.rtc2.video.VideoEncoderConfiguration
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonPrimitive
import timber.log.Timber

sealed class AgodaCallEvent {
    data class Error(val err: Int, val msg: String) : AgodaCallEvent()
    data class JoinChannelSuccess(val channel: String?, val uid: Int, val elapsed: Int) :
        AgodaCallEvent()

    data class UserJoined(val uid: Int, val elapsed: Int) : AgodaCallEvent()
    data class UserOffline(val uid: Int, val reason: Int) : AgodaCallEvent()
    data class UserMuteVideo(val uid: Int, val muted: Boolean) : AgodaCallEvent()
    data class UserMuteAudio(val uid: Int, val muted: Boolean) : AgodaCallEvent()
    data class ConnectionStateChanged(val state: Int, val reason: Int) : AgodaCallEvent()
    data class NetworkQuality(val uid: Int, val txQuality: Int, val rxQuality: Int) :
        AgodaCallEvent()
    data class NetworkQualityChanged(val quality: NetworkQualityLevel, val config: AgoraConfig) :
        AgodaCallEvent()
}

/**
 * 网络质量等级枚举
 */
enum class NetworkQualityLevel {
    EXCELLENT,  // 优秀 (txQuality: 0-1, rxQuality: 0-1)
    GOOD,       // 良好 (txQuality: 2, rxQuality: 2)
    POOR,       // 较差 (txQuality: 3-4, rxQuality: 3-4)
    BAD,        // 很差 (txQuality: 5-6, rxQuality: 5-6)
    UNKNOWN     // 未知
}

/**
 * 声网配置数据类
 */
data class AgoraConfig(
    val videoResolution: VideoEncoderConfiguration.VideoDimensions = VideoEncoderConfiguration.VD_1280x720,
    val frameRate: VideoEncoderConfiguration.FRAME_RATE,
    val bitrate: Int,
    val orientationMode: VideoEncoderConfiguration.ORIENTATION_MODE,
    val enableDualStream: Boolean,
    val description: String
)

@SuppressLint("StaticFieldLeak")
object AgodaUtils {
    private var rtcEngine: RtcEngine? = null
    private var isInitialized = false
    var remoteUid: Int = 0
    var currentChannel: String? = null

    var remoteView: TextureView = TextureView(CallmeApplication.context)

    fun removeRemoteView() {
        (remoteView.parent as? ViewGroup)?.removeAllViews()
    }

    // 网络质量相关
    private var currentNetworkQuality: NetworkQualityLevel = NetworkQualityLevel.UNKNOWN
    private var lastQualityChangeTime: Long = 0
    private val qualityChangeThreshold = 2000L // 2秒内不重复调整
    
    // 预定义的配置策略
    private val configStrategies = mapOf(
        NetworkQualityLevel.EXCELLENT to AgoraConfig(
            videoResolution = VideoEncoderConfiguration.VD_1280x720,
            frameRate = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_30,
            bitrate = VideoEncoderConfiguration.STANDARD_BITRATE,
            orientationMode = VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_ADAPTIVE,
            enableDualStream = true,
            description = "高清配置 - 网络优秀"
        ),
        NetworkQualityLevel.GOOD to AgoraConfig(
            videoResolution = VideoEncoderConfiguration.VD_960x720,
            frameRate = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_24,
            bitrate = VideoEncoderConfiguration.STANDARD_BITRATE,
            orientationMode = VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_ADAPTIVE,
            enableDualStream = true,
            description = "标清配置 - 网络良好"
        ),
        NetworkQualityLevel.POOR to AgoraConfig(
            videoResolution = VideoEncoderConfiguration.VD_640x480,
            frameRate = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_15,
            bitrate = VideoEncoderConfiguration.STANDARD_BITRATE,
            orientationMode = VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_ADAPTIVE,
            enableDualStream = false,
            description = "流畅配置 - 网络较差"
        ),
        NetworkQualityLevel.BAD to AgoraConfig(
            videoResolution = VideoEncoderConfiguration.VD_320x240,
            frameRate = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_10,
            bitrate = VideoEncoderConfiguration.STANDARD_BITRATE,
            orientationMode = VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_ADAPTIVE,
            enableDualStream = false,
            description = "低清配置 - 网络很差"
        )
    )

    /**
     * 全局初始化Agora，在登录成功后调用
     */
    fun initializeGlobal(context: Context) {
        val appId =
            AppConfigManager.getDecryptedAppConfig()?.items?.find { it.name == "rtck" }?.data?.jsonPrimitive?.contentOrNull

        if (appId.isNullOrEmpty()) {
            Log.w("AgodaUtils", "未获取到声网AppId，跳过初始化")
            return
        }

        if (isInitialized) {
            Log.d("AgodaUtils", "Agora已经初始化过了")
            return
        }

        try {
            rtcEngine = RtcEngine.create(
                context.applicationContext,
                appId,
                object : IRtcEngineEventHandler() {
                    var lastJoinChannel: String = ""
                    override fun onError(err: Int) {
                        Log.e("AgodaUtils", "Agora错误: " + getErrorDescription(err))
                        EventBus.post(AgodaCallEvent.Error(err, getErrorDescription(err)))
                    }

                    override fun onJoinChannelSuccess(channel: String?, uid: Int, elapsed: Int) {
                        Log.d(
                            "AgodaUtils",
                            "加入频道成功: channel=$channel, uid=$uid, elapsed=$elapsed"
                        )
                        currentChannel = channel
                        Timber.tag("bbbbbbb").d("onJoinChannelSuccess  $currentChannel")

                        LogReportManager.reportLiveCallEvent(
                            action = LiveCallAction.JOIN_SUCCESS,
                            ext = LiveCallExt.CHATTING
                        )

                        if (channel != lastJoinChannel) {
                            lastJoinChannel = channel.toString()
                            ThreadUtils.runOnIO {
                                try {
                                    RetrofitUtils.dataRepository.joinChannel(
                                        JoinChannelRequest(
                                            channel
                                        )
                                    )
                                    RetrofitUtils.dataRepository.updateAgoraUid(
                                        UpdateAgoraUidRequest(
                                            uid.toString()
                                        )
                                    )
                                } catch (e: Exception) {

                                }
                            }
                            EventBus.post(AgodaCallEvent.JoinChannelSuccess(channel, uid, elapsed))
                        }
                    }

                    override fun onUserJoined(uid: Int, elapsed: Int) {
                        Log.d("AgodaUtils", "远端用户加入: uid=$uid, elapsed=$elapsed")
                        remoteUid = uid

                        LogReportManager.reportLiveCallEvent(
                            action = LiveCallAction.USER_JOIN,
                            ext = LiveCallExt.CHATTING
                        )
                        if (remoteView != null) {
                            AgodaUtils.setupRemoteVideo(
                                VideoCanvas(
                                    remoteView,
                                    VideoCanvas.RENDER_MODE_HIDDEN,
                                    uid
                                )
                            )
                        }
                        EventBus.post(AgodaCallEvent.UserJoined(uid, elapsed))
                    }

                    override fun onUserOffline(uid: Int, reason: Int) {
                        Log.d("AgodaUtils", "远端用户离开: uid=$uid, reason=$reason")
                        remoteUid = 0
                        currentChannel = null
                        EventBus.post(AgodaCallEvent.UserOffline(uid, reason))
                    }

                    override fun onUserMuteVideo(uid: Int, muted: Boolean) {
                        Log.d("AgodaUtils", "远端用户视频静音状态改变: uid=$uid, muted=$muted")
                        EventBus.post(AgodaCallEvent.UserMuteVideo(uid, muted))
                    }

                    override fun onUserMuteAudio(uid: Int, muted: Boolean) {
                        Log.d("AgodaUtils", "远端用户音频静音状态改变: uid=$uid, muted=$muted")
                        EventBus.post(AgodaCallEvent.UserMuteAudio(uid, muted))
                    }

                    override fun onConnectionStateChanged(state: Int, reason: Int) {
                        Log.d("AgodaUtils", "连接状态改变: state=$state, reason=$reason")
                        EventBus.post(AgodaCallEvent.ConnectionStateChanged(state, reason))
                    }

                    override fun onNetworkQuality(uid: Int, txQuality: Int, rxQuality: Int) {
//                        Timber.tag("AgodaUtils").d("网络质量变化: uid=$uid, txQuality=$txQuality, rxQuality=$rxQuality")
                        
                        // 发送原始网络质量事件
                        EventBus.post(AgodaCallEvent.NetworkQuality(uid, txQuality, rxQuality))
                        
                        // 根据网络质量调整配置
                        adjustConfigBasedOnNetworkQuality(txQuality, rxQuality)
                    }
                })
            // 设置默认的编码配置（新版SDK移除MIRROR_MODE_TYPE参数）
            rtcEngine?.setVideoEncoderConfiguration(
                VideoEncoderConfiguration(
                    VideoEncoderConfiguration.VD_1280x720,
                    VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_30,
                    VideoEncoderConfiguration.STANDARD_BITRATE,
                    VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_ADAPTIVE
                )
            )

            // 设置频道配置 - 必须设置为通信场景，避免3分钟挂断问题
            rtcEngine?.setChannelProfile(Constants.CHANNEL_PROFILE_COMMUNICATION)


            // 视频默认禁用，你需要调用 enableVideo 开始视频流。
            enableVideo()
            
            // 启用本地视频
            enableLocalVideo(true)
            
            // 根据网络类型预调整配置
            adjustConfigByNetworkType()
            
            isInitialized = true
            Log.d("AgodaUtils", "Agora初始化成功，已设置为通信场景")
        } catch (e: Exception) {
            Log.e("AgodaUtils", "Agora初始化失败", e)
        }
    }

    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean {
        return isInitialized && rtcEngine != null
    }

    fun joinChannel(token: String?, channelName: String, optionalInfo: String?, optionalUid: Int) {
        rtcEngine?.joinChannel(token, channelName, optionalInfo, optionalUid)
    }

    /**
     * 使用用户账号加入频道（推荐使用，避免3分钟挂断问题）
     */
    fun joinChannelWithUserAccount(token: String?, channelName: String, userAccount: String) {
        if (currentChannel == channelName) {
            return
        }
        Log.d("AgodaUtils", "joinChannelWithUserAccount: channelName: ${channelName}")
        try {
            rtcEngine?.joinChannelWithUserAccount(
                token,
                channelName,
                UserInfoManager.myUserInfo?.userId ?: userAccount
            )
        } catch (e : Exception) {

        }
    }

    fun leaveChannel() {
        rtcEngine?.leaveChannel()
    }

    fun destroy() {
        rtcEngine?.setupLocalVideo(null)
        rtcEngine?.setupRemoteVideo(null)
    }

    /**
     * 彻底释放RtcEngine资源
     */
    fun release() {
        try {
            rtcEngine?.leaveChannel()
            rtcEngine?.stopPreview()
            RtcEngine.destroy()
        } catch (e: Exception) {
            Log.e("AgodaUtils", "release error", e)
        } finally {
            rtcEngine = null
            isInitialized = false
            remoteUid = 0
            currentChannel = null
        }
    }

    fun getRtcEngine(): RtcEngine? = rtcEngine

    // 视频相关API
    fun setupLocalVideo(videoCanvas: VideoCanvas) {
        rtcEngine?.setupLocalVideo(videoCanvas)
    }

    fun setupRemoteVideo(videoCanvas: VideoCanvas) {
        rtcEngine?.setupRemoteVideo(videoCanvas)
    }

    fun startPreview() {
        rtcEngine?.startPreview()
    }

    fun stopPreview() {
        rtcEngine?.stopPreview()
    }

    fun enableVideo() {
        rtcEngine?.enableVideo()
    }

    fun disableVideo() {
        rtcEngine?.disableVideo()
    }

    fun enableLocalVideo(enabled: Boolean) {
        rtcEngine?.enableLocalVideo(enabled)
    }

    fun muteLocalVideoStream(muted: Boolean) {
        rtcEngine?.muteLocalVideoStream(muted)
    }

    fun muteRemoteVideoStream(uid: Int, muted: Boolean) {
        rtcEngine?.muteRemoteVideoStream(uid, muted)
    }

    fun muteAllRemoteVideoStreams(muted: Boolean) {
        rtcEngine?.muteAllRemoteVideoStreams(muted)
    }

    // 音频相关API
    fun enableAudio() {
        rtcEngine?.enableAudio()
    }

    fun disableAudio() {
        rtcEngine?.disableAudio()
    }

    fun enableLocalAudio(enabled: Boolean) {
        rtcEngine?.enableLocalAudio(enabled)
    }

    fun muteLocalAudioStream(muted: Boolean) {
        rtcEngine?.muteLocalAudioStream(muted)
    }

    fun muteRemoteAudioStream(uid: Int, muted: Boolean) {
        rtcEngine?.muteRemoteAudioStream(uid, muted)
    }

    fun muteAllRemoteAudioStreams(muted: Boolean) {
        rtcEngine?.muteAllRemoteAudioStreams(muted)
    }

    fun setChannelProfile(profile: Int) {
        rtcEngine?.setChannelProfile(profile)
    }

    fun enableDualStreamMode(enabled: Boolean) {
        rtcEngine?.enableDualStreamMode(enabled)
    }

    // 摄像头控制
    fun switchCamera() {
        rtcEngine?.switchCamera()
    }

    fun setCameraAutoFocusFaceModeEnabled(enabled: Boolean) {
        rtcEngine?.setCameraAutoFocusFaceModeEnabled(enabled)
    }

    // 音量控制
    fun adjustRecordingSignalVolume(volume: Int) {
        rtcEngine?.adjustRecordingSignalVolume(volume)
    }

    fun adjustPlaybackSignalVolume(volume: Int) {
        rtcEngine?.adjustPlaybackSignalVolume(volume)
    }

    // 错误处理
    fun getErrorDescription(error: Int): String {
        return RtcEngine.getErrorDescription(error)
    }

    fun observeError(owner: LifecycleOwner, onEvent: (AgodaCallEvent.Error) -> Unit) {
        EventBus.observe(owner, AgodaCallEvent.Error::class.java, onEvent = onEvent)
    }

    fun observeJoinChannelSuccess(
        owner: LifecycleOwner,
        onEvent: (AgodaCallEvent.JoinChannelSuccess) -> Unit
    ) {
        EventBus.observe(owner, AgodaCallEvent.JoinChannelSuccess::class.java, onEvent = onEvent)
    }

    fun observeUserJoined(owner: LifecycleOwner, onEvent: (AgodaCallEvent.UserJoined) -> Unit) {
        EventBus.observe(owner, AgodaCallEvent.UserJoined::class.java, onEvent = onEvent)
    }

    fun observeUserOffline(owner: LifecycleOwner, onEvent: (AgodaCallEvent.UserOffline) -> Unit) {
        EventBus.observe(owner, AgodaCallEvent.UserOffline::class.java, onEvent = onEvent)
    }

    fun observeUserMuteVideo(
        owner: LifecycleOwner,
        onEvent: (AgodaCallEvent.UserMuteVideo) -> Unit
    ) {
        EventBus.observe(owner, AgodaCallEvent.UserMuteVideo::class.java, onEvent = onEvent)
    }

    fun observeUserMuteAudio(
        owner: LifecycleOwner,
        onEvent: (AgodaCallEvent.UserMuteAudio) -> Unit
    ) {
        EventBus.observe(owner, AgodaCallEvent.UserMuteAudio::class.java, onEvent = onEvent)
    }

    fun observeConnectionStateChanged(
        owner: LifecycleOwner,
        onEvent: (AgodaCallEvent.ConnectionStateChanged) -> Unit
    ) {
        EventBus.observe(
            owner,
            AgodaCallEvent.ConnectionStateChanged::class.java,
            onEvent = onEvent
        )
    }

    fun observeNetworkQuality(
        owner: LifecycleOwner,
        onEvent: (AgodaCallEvent.NetworkQuality) -> Unit
    ) {
        EventBus.observe(owner, AgodaCallEvent.NetworkQuality::class.java, onEvent = onEvent)
    }

    fun observeNetworkQualityChanged(
        owner: LifecycleOwner,
        onEvent: (AgodaCallEvent.NetworkQualityChanged) -> Unit
    ) {
        EventBus.observe(owner, AgodaCallEvent.NetworkQualityChanged::class.java, onEvent = onEvent)
    }

    /**
     * 根据网络质量调整声网配置
     */
    private fun adjustConfigBasedOnNetworkQuality(txQuality: Int, rxQuality: Int) {
        val currentTime = System.currentTimeMillis()
        
        // 防止频繁调整配置
        if (currentTime - lastQualityChangeTime < qualityChangeThreshold) {
            return
        }
        
        val newQuality = evaluateNetworkQuality(txQuality, rxQuality)
        
        // 如果网络质量没有变化，不需要调整
        if (newQuality == currentNetworkQuality) {
            return
        }
        Timber.tag("AgodaUtils").i("网络质量从 ${currentNetworkQuality} 变化到 ${newQuality}")
        
        // 应用新的配置
        val newConfig = configStrategies[newQuality]
        if (newConfig != null) {
            applyAgoraConfig(newConfig)
            currentNetworkQuality = newQuality
            lastQualityChangeTime = currentTime

            // 发送配置变化事件
            EventBus.post(AgodaCallEvent.NetworkQualityChanged(newQuality, newConfig))

            Timber.tag("AgodaUtils").i("已应用新配置: ${newConfig.description}")
        }
    }

    /**
     * 评估网络质量等级
     */
    private fun evaluateNetworkQuality(txQuality: Int, rxQuality: Int): NetworkQualityLevel {
        // 取发送和接收质量的平均值
        val avgQuality = (txQuality + rxQuality) / 2
        
        return when {
            avgQuality <= 1 -> NetworkQualityLevel.EXCELLENT
            avgQuality == 2 -> NetworkQualityLevel.GOOD
            avgQuality in 3..4 -> NetworkQualityLevel.POOR
            avgQuality >= 5 -> NetworkQualityLevel.BAD
            else -> NetworkQualityLevel.UNKNOWN
        }
    }

    /**
     * 应用声网配置
     */
    private fun applyAgoraConfig(config: AgoraConfig) {
        try {
            rtcEngine?.let { engine ->
                // 设置视频编码配置
                engine.setVideoEncoderConfiguration(
                    VideoEncoderConfiguration(
                        config.videoResolution,
                        config.frameRate,
                        config.bitrate,
                        config.orientationMode
                    )
                )
                
                // 设置双流模式
                engine.enableDualStreamMode(config.enableDualStream)
                
                Timber.tag("AgodaUtils").d("声网配置已更新: ${config.description}")
            }
        } catch (e: Exception) {
        }
    }

    /**
     * 获取当前网络质量等级
     */
    fun getCurrentNetworkQuality(): NetworkQualityLevel {
        return currentNetworkQuality
    }

    /**
     * 获取当前配置策略
     */
    fun getCurrentConfig(): AgoraConfig? {
        return configStrategies[currentNetworkQuality]
    }

    /**
     * 手动设置网络质量等级（用于测试或特殊场景）
     */
    fun setNetworkQualityLevel(quality: NetworkQualityLevel) {
        val config = configStrategies[quality]
        if (config != null) {
            applyAgoraConfig(config)
            currentNetworkQuality = quality
            lastQualityChangeTime = System.currentTimeMillis()
            
            EventBus.post(AgodaCallEvent.NetworkQualityChanged(quality, config))
            Timber.tag("AgodaUtils").i("手动设置网络质量: $quality, 配置: ${config.description}")
        }
    }

    /**
     * 根据网络类型预调整配置
     */
    fun adjustConfigByNetworkType() {
        val networkType = NetworkUtils.getNetworkType()
        val quality = when (networkType) {
            NetworkUtils.NETWORK_TYPE_WIFI -> NetworkQualityLevel.EXCELLENT
            NetworkUtils.NETWORK_TYPE_MOBILE -> {
                // 根据移动网络类型进一步判断
                val detailedType = NetworkUtils.getNetWorkType()
                when (detailedType) {
                    NetworkUtils.NETWORK_TYPE_3G -> NetworkQualityLevel.GOOD
                    NetworkUtils.NETWORK_TYPE_2G -> NetworkQualityLevel.POOR
                    else -> NetworkQualityLevel.GOOD
                }
            }
            NetworkUtils.NETWORK_TYPE_NONE -> NetworkQualityLevel.BAD
            else -> NetworkQualityLevel.UNKNOWN
        }
        
        setNetworkQualityLevel(quality)
    }

} 