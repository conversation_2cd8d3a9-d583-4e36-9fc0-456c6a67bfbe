package com.score.callmetest.util

import android.app.Activity
import android.app.Dialog
import android.view.ViewGroup
import android.view.Window
import com.opensource.svgaplayer.SVGADrawable
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser

object LoadingUtils {
    private var dialog: Dialog? = null
    fun showLoading(activity: Activity, cancelable: Boolean = false) {
        if (dialog?.isShowing == true) return
        dialog = Dialog(activity)
        dialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        val svgaView = SVGAImageView(activity)
        svgaView.setBackgroundColor(android.graphics.Color.TRANSPARENT)
        dialog?.setContentView(
            svgaView,
            ViewGroup.LayoutParams(
                DisplayUtils.dp2px(70f),
                DisplayUtils.dp2px(70f)
            )
        )
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(cancelable)
        dialog?.show()
        // 播放loading.svga
        CustomUtils.playSvga(svgaView, "loading.svga")
    }

    fun dismissLoading() {
        DialogUtils.safeDismissOnMainThread(dialog)
        dialog = null
    }
} 