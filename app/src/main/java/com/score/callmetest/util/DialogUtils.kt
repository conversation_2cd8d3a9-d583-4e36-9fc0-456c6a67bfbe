package com.score.callmetest.util

import android.app.Dialog
import android.os.Handler
import android.os.Looper
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import timber.log.Timber

/**
 * Dialog工具类，提供安全的Dialog操作
 * 解决Dialog.dismiss()时出现"not attach to window"崩溃问题
 */
object DialogUtils {
    
    private const val TAG = "DialogUtils"
    
    /**
     * 安全地关闭Dialog
     * 检查Dialog状态，避免"not attach to window"崩溃
     */
    fun safeDismiss(dialog: Dialog?) {
        if (dialog == null) {
            Timber.tag(TAG).d("Dialog is null, skip dismiss")
            return
        }
        
        try {
            // 检查Dialog是否正在显示
            if (dialog.isShowing) {
                // 检查Dialog是否有window
                if (dialog.window != null) {
                    dialog.dismiss()
                } else {
                    Timber.tag(TAG).w("Dialog window is null, skip dismiss")
                }
            } else {
                Timber.tag(TAG).d("Dialog is not showing, skip dismiss")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to dismiss dialog: ${e.message}")
        }
    }
    
    /**
     * 强制关闭Dialog（备用方法）
     * 当safeDismiss不工作时使用
     */
    fun forceDismiss(dialog: Dialog?) {
        if (dialog == null) {
            return
        }
        
        try {
            Timber.tag(TAG).d("Force dismissing dialog: ${dialog.javaClass.simpleName}")
            dialog.dismiss()
            Timber.tag(TAG).d("Dialog force dismissed successfully")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to force dismiss dialog: ${e.message}")
        }
    }
    
    /**
     * 安全地关闭DialogFragment
     * 检查Fragment状态，避免状态异常崩溃
     */
    fun safeDismissDialogFragment(dialogFragment: DialogFragment?) {
        if (dialogFragment == null) {
            Timber.tag(TAG).d("DialogFragment is null, skip dismiss")
            return
        }
        
        try {
            // 检查Fragment是否已添加且未分离
            if (dialogFragment.isAdded && !dialogFragment.isDetached) {
                // 检查Fragment是否正在显示
                if (dialogFragment.dialog?.isShowing == true) {
                    dialogFragment.dismissAllowingStateLoss()
                    Timber.tag(TAG).d("DialogFragment dismissed successfully")
                } else {
                    Timber.tag(TAG).d("DialogFragment dialog is not showing, skip dismiss")
                }
            } else {
                Timber.tag(TAG).d("DialogFragment is not added or detached, skip dismiss")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to dismiss DialogFragment: ${e.message}")
        }
    }
    
    /**
     * 在主线程中安全地关闭Dialog
     * 如果当前不在主线程，会切换到主线程执行
     */
    fun safeDismissOnMainThread(dialog: Dialog?) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            // 已在主线程，直接执行
            safeDismiss(dialog)
        } else {
            // 不在主线程，切换到主线程执行
            Handler(Looper.getMainLooper()).post {
                safeDismiss(dialog)
            }
        }
    }
    
    /**
     * 在主线程中安全地关闭DialogFragment
     * 如果当前不在主线程，会切换到主线程执行
     */
    fun safeDismissDialogFragmentOnMainThread(dialogFragment: DialogFragment?) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            // 已在主线程，直接执行
            safeDismissDialogFragment(dialogFragment)
        } else {
            // 不在主线程，切换到主线程执行
            Handler(Looper.getMainLooper()).post {
                safeDismissDialogFragment(dialogFragment)
            }
        }
    }
    
    /**
     * 查找并安全关闭指定tag的DialogFragment
     */
    fun safeDismissDialogFragmentByTag(fragmentManager: FragmentManager?, tag: String) {
        if (fragmentManager == null) {
            Timber.tag(TAG).w("FragmentManager is null, cannot dismiss dialog by tag: $tag")
            return
        }
        
        try {
            val fragment = fragmentManager.findFragmentByTag(tag)
            if (fragment is DialogFragment) {
                safeDismissDialogFragment(fragment)
            } else {
                Timber.tag(TAG).d("No DialogFragment found with tag: $tag")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to dismiss DialogFragment by tag: $tag")
        }
    }
    
    /**
     * 检查Dialog是否可以安全关闭
     */
    fun canDismissSafely(dialog: Dialog?): Boolean {
        if (dialog == null) return false
        
        return try {
            dialog.isShowing && dialog.window != null
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error checking dialog state: ${e.message}")
            false
        }
    }
    
    /**
     * 检查DialogFragment是否可以安全关闭
     */
    fun canDismissDialogFragmentSafely(dialogFragment: DialogFragment?): Boolean {
        if (dialogFragment == null) return false
        
        return try {
            dialogFragment.isAdded && 
            !dialogFragment.isDetached && 
            dialogFragment.dialog?.isShowing == true
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Error checking DialogFragment state: ${e.message}")
            false
        }
    }
}
