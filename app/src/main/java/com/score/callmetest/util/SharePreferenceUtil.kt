package com.score.callmetest.util

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import com.score.callmetest.CallmeApplication
import timber.log.Timber

object SharePreferenceUtil {
    fun getPrefs(name: String = "default_prefs"): SharedPreferences {
        return CallmeApplication.Companion.context.getSharedPreferences(name, Context.MODE_PRIVATE)
    }

    fun putString(key: String, value: String, name: String = "default_prefs") {
        getPrefs(name).edit { putString(key, value) }
    }

    fun getString(key: String, defValue: String? = null, name: String = "default_prefs"): String? {
        return getPrefs(name).getString(key, defValue)
    }

    fun putBoolean(key: String, value: Boolean, name: String = "default_prefs") {
        getPrefs(name).edit { putBoolean(key, value) }
    }

    fun getBoolean(key: String, defValue: Boolean = false, name: String = "default_prefs"): Boolean {
        return getPrefs(name).getBoolean(key, defValue)
    }

    fun putInt(key: String, value: Int, name: String = "default_prefs") {
        getPrefs(name).edit { putInt(key, value) }
    }

    fun getInt(key: String, defValue: Int = 0, name: String = "default_prefs"): Int {
        return getPrefs(name).getInt(key, defValue)
    }

    fun putLong(key: String, value: Long, name: String = "default_prefs") {
        getPrefs(name).edit { putLong(key, value) }
    }

    fun getLong(key: String, defValue: Long = 0L, name: String = "default_prefs"): Long {
        return getPrefs(name).getLong(key, defValue)
    }

    fun putFloat(key: String, value: Float, name: String = "default_prefs") {
        getPrefs(name).edit { putFloat(key, value) }
    }

    fun getFloat(key: String, defValue: Float = 0f, name: String = "default_prefs"): Float {
        return getPrefs(name).getFloat(key, defValue)
    }

    fun contains(key: String, name: String = "default_prefs"): Boolean {
        return getPrefs(name).contains(key)
    }

    fun remove(key: String, name: String = "default_prefs") {
        getPrefs(name).edit { remove(key) }
    }

    fun clear(name: String = "default_prefs") {
        getPrefs(name).edit { clear() }
    }


    // ------------------------------------------------------------------------------------------------

    const val KEY_KEYBOARD_HEIGHT: String = "KEY_BROADCAST_HEIGHT"
    var TEMP_KEYBOARD_HEIGHT: Int = -1
    var TEMP_KEYBOARD_ORIENTATION: Int = -1

    /**
     * 获取保存的键盘高度
     * @param orientation 屏幕方向
     * @return 保存的键盘高度
     */
    fun getSaveKeyBoardHeight(orientation: Int): Int {
        return if (TEMP_KEYBOARD_HEIGHT == -1 || orientation != TEMP_KEYBOARD_ORIENTATION) {
            val height = getInt(getKeyboardHeightKey(orientation), -1)
            TEMP_KEYBOARD_HEIGHT = height
            TEMP_KEYBOARD_ORIENTATION = orientation
            if (height == -1) 0 else height
        } else {
            TEMP_KEYBOARD_HEIGHT
        }
    }

    /**
     * 保存键盘高度
     * @param orientation 屏幕方向
     * @param height 键盘高度
     */
    fun saveKeyboardHeight(orientation: Int, height: Int) {
        if (TEMP_KEYBOARD_HEIGHT != height || orientation != TEMP_KEYBOARD_ORIENTATION) {
            TEMP_KEYBOARD_HEIGHT = height
            TEMP_KEYBOARD_ORIENTATION = orientation
            putInt(getKeyboardHeightKey(orientation), height, )
        }
        Timber.d("保存键盘高度: orientation=$orientation, height=$height")
    }

    private fun getKeyboardHeightKey(orientation: Int): String {
        return KEY_KEYBOARD_HEIGHT + "_" + orientation
    }
} 