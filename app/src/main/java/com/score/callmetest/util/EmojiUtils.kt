package com.score.callmetest.util

import androidx.annotation.DrawableRes
import com.score.callmetest.R
import timber.log.Timber

/**
 * 管理StayDialog中emoji图片循环显示的工具类
 * 以及控制每次支付，只显示2次挽留弹窗的逻辑
 */
object EmojiUtils {
    
    private const val KEY_EMOJI_COUNTER = "stay_dialog_emoji_counter"
    private const val PREFS_NAME = "stay_dialog_prefs"
    /** StayDialog显示次数控制 */
    private const val KEY_STAY_DIALOG_COUNT = "stay_dialog_show_count"
    private const val MAX_STAY_DIALOG_SHOW_COUNT = 2
    
    // 定义4个emoji图片资源
    private val emojiArray = arrayOf(
        R.drawable.pic1,
        R.drawable.pic2,
        R.drawable.pic3,
        R.drawable.pic4
    )
    
    /**
     * 获取下一个要显示的emoji资源ID
     * @return 下一个emoji的资源ID
     */
    @DrawableRes
    fun getNextEmoji(): Int {
        // 获取当前计数器
        val currentCounter = SharePreferenceUtil.getInt(KEY_EMOJI_COUNTER, 0, PREFS_NAME)
        
        // 根据计数器获取对应的emoji（使用模运算实现循环）
        val emojiResId = emojiArray[currentCounter % emojiArray.size]
        
        // 更新计数器
        val nextCounter = currentCounter + 1
        SharePreferenceUtil.putInt(KEY_EMOJI_COUNTER, nextCounter, PREFS_NAME)
        
        return emojiResId
    }
    
    /**
     * 获取当前计数器值（用于调试）
     * @return 当前计数器值
     */
    fun getCurrentCounter(): Int {
        return SharePreferenceUtil.getInt(KEY_EMOJI_COUNTER, 0, PREFS_NAME)
    }
    
    /**
     * 重置计数器（用于测试或特殊需求）
     */
    fun resetCounter() {
        SharePreferenceUtil.putInt(KEY_EMOJI_COUNTER, 0, PREFS_NAME)
    }
    
    /**
     * 获取当前应该显示的emoji（不更新计数器）
     * @return 当前emoji的资源ID
     */
    @DrawableRes
    fun getCurrentEmoji(): Int {
        val currentCounter = SharePreferenceUtil.getInt(KEY_EMOJI_COUNTER, 0, PREFS_NAME)
        return emojiArray[currentCounter % emojiArray.size]
    }


    /**
     * 检查是否可以显示StayDialog
     * @return true表示可以显示，false表示已达到最大显示次数
     */
     fun canShowStayDialog(): Boolean {
        val currentCount = SharePreferenceUtil.getInt(KEY_STAY_DIALOG_COUNT, 0)
        return currentCount < MAX_STAY_DIALOG_SHOW_COUNT
    }

    /**
     * 增加StayDialog显示次数
     */
     fun incrementStayDialogCount() {
        val currentCount = SharePreferenceUtil.getInt(KEY_STAY_DIALOG_COUNT, 0)
        SharePreferenceUtil.putInt(KEY_STAY_DIALOG_COUNT, currentCount + 1)
        Timber.d("StayDialog显示次数增加，当前次数: ${currentCount + 1}")
    }

    /**
     * 重置StayDialog次数
     */
    fun resetStayDialogCount() {
        SharePreferenceUtil.putInt(KEY_STAY_DIALOG_COUNT, 0)
        Timber.d("StayDialog显示次数重置为0")
    }
}
