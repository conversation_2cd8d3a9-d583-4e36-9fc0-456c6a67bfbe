package com.score.callmetest.entity

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 用户信息业务实体类
 * 用于存储用户的基本信息，包括头像、昵称等
 * 
 * @property userId 用户ID，唯一标识
 * @property avatarThumbUrl 头像缩略图URL，用于列表显示等场景，文件较小，加载速度快
 * @property avatarMiddleThumbUrl 头像中等尺寸缩略图URL，用于详情页等需要中等尺寸头像的场景
 * @property avatar 头像原图URL，用于查看大图等场景，文件较大，清晰度高
 * @property nickName 用户昵称
 * @property createdTime 创建时间戳（毫秒）
 * @property updatedTime 更新时间戳（毫秒）
 */
@Parcelize
data class UserInfoEntity(
    val userId: String,
    val avatarThumbUrl: String = "",
    val avatarMiddleThumbUrl: String = "",
    val avatar: String = "",
    val nickName: String = "",
    val createdTime: Long = System.currentTimeMillis(),
    val updatedTime: Long = System.currentTimeMillis()
) : Parcelable {
    
    /**
     * 检查用户信息是否有效
     * @return true 如果用户ID不为空，false 否则
     */
    fun isValid(): Boolean {
        return userId.isNotBlank()
    }
    
    /**
     * 获取最适合的头像URL
     * 优先级：avatar > avatarMiddleThumbUrl > avatarThumbUrl
     * @return 头像URL，如果都为空则返回空字符串
     */
    fun getBestAvatar(): String {
        return when {
            avatar.isNotBlank() -> avatar
            avatarMiddleThumbUrl.isNotBlank() -> avatarMiddleThumbUrl
            avatarThumbUrl.isNotBlank() -> avatarThumbUrl
            else -> ""
        }
    }
    
    /**
     * 获取缩略图头像URL
     * 优先级：avatarThumbUrl > avatarMiddleThumbUrl > avatar
     * @return 缩略图头像URL，如果都为空则返回空字符串
     */
    fun getThumbAvatar(): String {
        return when {
            avatarThumbUrl.isNotBlank() -> avatarThumbUrl
            avatarMiddleThumbUrl.isNotBlank() -> avatarMiddleThumbUrl
            avatar.isNotBlank() -> avatar
            else -> ""
        }
    }
    
    /**
     * 获取显示名称
     * 如果昵称为空，则返回用户ID
     * @return 显示名称
     */
    fun getDisplayName(): String {
        return if (nickName.isNotBlank()) nickName else userId
    }
    
    /**
     * 创建一个更新时间戳的副本
     * @return 更新了时间戳的新实体
     */
    fun updateTimestamp(): UserInfoEntity {
        return this.copy(updatedTime = System.currentTimeMillis())
    }
    
    companion object {
        /**
         * 创建一个新的用户信息实体
         * @param userId 用户ID
         * @param avatarThumbUrl 头像缩略图URL
         * @param avatarMiddleThumbUrl 头像中等尺寸缩略图URL
         * @param avatar 头像原图URL
         * @param nickName 用户昵称
         * @return 新创建的用户信息实体
         */
        fun create(
            userId: String,
            avatarThumbUrl: String = "",
            avatarMiddleThumbUrl: String = "",
            avatar: String = "",
            nickName: String = ""
        ): UserInfoEntity {
            val currentTime = System.currentTimeMillis()
            return UserInfoEntity(
                userId = userId,
                avatarThumbUrl = avatarThumbUrl,
                avatarMiddleThumbUrl = avatarMiddleThumbUrl,
                avatar = avatar,
                nickName = nickName,
                createdTime = currentTime,
                updatedTime = currentTime
            )
        }
        
        /**
         * 创建一个空的用户信息实体
         * @param userId 用户ID
         * @return 只有用户ID的空实体
         */
        fun empty(userId: String): UserInfoEntity {
            return create(userId = userId)
        }
    }
}
