package com.score.callmetest.entity

import android.net.Uri
import com.score.callmetest.util.AgodaCallEvent


/**
 * 自定义事件-- 不知道分类在哪的都放这里
 * <AUTHOR>
 * @date 2025/08/01
 * @constructor 创建[CustomEvents]
 */
sealed class CustomEvents {


    /**
     * 选择底部选项卡
     * <AUTHOR>
     * @date 2025/08/01
     * @constructor 创建[BottomTabSelected]
     * @param [index]
     */
    data class BottomTabSelected(val index: Int) : CustomEvents()

    /**
     * 审核模式下呼出
     * <AUTHOR>
     * @date 2025/08/04
     * @constructor 创建[ReviewCallEvent]
     * @param [userId] 用户id
     */
    data class ReviewCallEvent(val userId: String) : CustomEvents()

    /**
     * 支付流程返回事件
     * 用于处理从外部浏览器返回应用时的轮询策略调整
     * <AUTHOR>
     * @date 2025/08/14
     * @constructor 创建[PaymentReturnEvent]
     */
    object PaymentReturnEvent : CustomEvents()

    /**
     * 完成充值--更新界面
     */
    object CheckInDialogUpdate : CustomEvents()

    /**
     * 签到是否可用
     */
    data class CheckInAvailable(val isAvailable: Boolean) : CustomEvents()

    /**
     * 新状态事件<userId,[com.score.callmetest.CallStatus]>
     */
    data class NewStatusEvent(val statusMap: Map<String, String>): CustomEvents()


    /**
     * 选择主播墙事件
     * @param [tab1Name] tab1名字
     * @param [tab2Name] tab2名字
     */
    data class SelectWallEvent(val tab1Name: String, val tab2Name: String): CustomEvents()

    /**
     * 消息列表滚动停止事件
     * 用于在滚动停止500ms后触发状态更新
     * @param [fragmentTag] Fragment标识，用于区分不同的Fragment
     */
    data class MessageListScrollEvent(val fragmentTag: String): CustomEvents()

}

/**
 * EventBus-Message相关
 */
sealed class MessageEvents {

    /**
     * 音频下载完成
     * @param [msgId] msg-id
     * @param [localUri] 本地uri
     */
    data class AudioDownloadOk(val msgId: String, val localUri: Uri) : MessageEvents()

    /**
     * 新通话消息
     */
    data class NewCallMessage(val chatMessage: ChatMessageEntity) : MessageEvents()

    /**
     * 自动关注消息事件（用于模拟主播发送首次关注消息）
     */
    data class AutoFollowMessage(val chatMessage: ChatMessageEntity) : MessageEvents()

}