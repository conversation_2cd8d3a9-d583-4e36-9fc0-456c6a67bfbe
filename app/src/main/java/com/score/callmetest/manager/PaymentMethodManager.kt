package com.score.callmetest.manager

import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.PayChannelExtra
import com.score.callmetest.network.PayChannelItem
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.StrategyConfig
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.util.CustomUtils
import timber.log.Timber

/**
 * 支付方式管理器
 * 负责管理支付渠道、用户选择、优惠信息等
 */
object PaymentMethodManager {

    // 支付渠道常量
    const val PAY_CHANNEL_GP = "GP"           // Google Play
    // SharedPreferences 键
    private const val KEY_LAST_PAYMENT_METHOD = "last_payment_method"
    private const val KEY_IS_FIRST_RECHARGE = "is_first_recharge"
    private const val KEY_PAYMENT_METHOD_RED_DOT = "payment_method_red_dot"

    // 当前可用的支付渠道
    private var availablePayChannels = mutableListOf<String>()
    
    // 支付渠道优惠信息
    private val channelDiscounts = mutableMapOf<String, Int>()


    // 支付渠道extra
    private var payChannelExtras: PayChannelExtra? = null
    // 支付渠道缓存
    private var cachedPayChannelList: List<PayChannelItem>? = null

    fun cachePayChannelList(channelList: List<PayChannelItem>?) {
        cachedPayChannelList = channelList
    }

    fun getCachedPayChannelList(): List<PayChannelItem>? {
        return cachedPayChannelList
    }
    
    /**
     * 初始化支付方式管理器
     */
    fun init(strategyConfig: StrategyConfig?) {
        strategyConfig?.payChannels?.let { channels ->
            availablePayChannels.addAll(channels)
        }
        
        // 设置本地支付优惠
        strategyConfig?.lpDiscount?.let { discount ->
        }
        
        // 设置促销商品优惠
        strategyConfig?.lpPromotionDiscount?.let { discount ->
            // 促销商品额外优惠逻辑
        }
    }

    /**
     * 查询支付渠道列表
     */
    private suspend fun fetchPayChannelList(): com.score.callmetest.network.BaseResponse<com.score.callmetest.network.PayChannelResponse>? {
        return try {
            val response = RetrofitUtils.dataRepository.getChannelList()
            Timber.tag("dsc--Recharge").d("查询支付渠道成功: $response")
            if(response is NetworkResult.Success) response.originResp else null
        } catch (e: Exception) {
            Timber.tag("dsc--Recharge").e(e, "查询支付渠道失败")
            null
        }
    }

    /**
     * 确保支付渠道列表已加载，优先返回缓存，无缓存时自动请求并缓存
     */
    suspend fun ensurePayChannelListLoaded(): List<PayChannelItem>? {
        if (cachedPayChannelList != null) return cachedPayChannelList
        val payChannelResponse = fetchPayChannelList()
        payChannelExtras = payChannelResponse?.data?.extra
        val list = payChannelResponse?.data?.channelList
        availablePayChannels.clear()
        list?.forEach { item ->
            item.payChannel?.let { channel ->
                availablePayChannels.add(channel)
                channelDiscounts[channel] = item.presentCoinRatio ?: 0
            }
        }
        cachePayChannelList(list)
        return list
    }

    /**
     * 主动预加载支付渠道并缓存，供登录成功后调用，内部处理异常，完成后回调
     */
    fun preloadPayChannelList(onFinish: (() -> Unit)? = null) {
        ThreadUtils.runOnIO {
            try {
                ensurePayChannelListLoaded()
            } catch (e: Exception) {
                Timber.tag("dsc--Recharge").e(e, "拉取支付渠道失败")
            } finally {
                onFinish?.invoke()
            }
        }
    }

    /**
     * 获取可用的支付渠道
     */
    fun getAvailablePayChannels(): List<String> = availablePayChannels

    
    /**
     * 获取用户上次使用的支付方式
     */
    fun getLastUsedPaymentMethod(): String? {
        return SharePreferenceUtil.getString(KEY_LAST_PAYMENT_METHOD, PAY_CHANNEL_GP, "payment")
    }
    
    /**
     * 保存用户选择的支付方式
     */
    fun saveLastUsedPaymentMethod(payChannel: String) {
        SharePreferenceUtil.putString(KEY_LAST_PAYMENT_METHOD, payChannel, "payment")
    }

    /**
     * 需要跳转外部浏览器
     *
     * @param [payChannel] 支付通道
     * @return [Boolean]
     */
    fun isNeedJumpExternalWeb(payChannel: String): Boolean{
        cachedPayChannelList?.forEach { item ->
            if (item.payChannel == payChannel) {
                return item.jumpType == 1
            }
        }
        return false
    }

    /**
     * 获取默认支付方式
     */
    fun getDefaultPaymentMethod(): String? {
        val lastUsed = getLastUsedPaymentMethod()
        if (lastUsed != null && availablePayChannels.contains(lastUsed)) {
            return lastUsed
        }
        return payChannelExtras?.chooseChannel ?: availablePayChannels.firstOrNull()
    }
    
    /**
     * 检查是否需要显示支付方式选择入口
     * 付费用户且多个支付渠道时显示
     */
    fun shouldShowPaymentMethodSelector(): Boolean {
        return availablePayChannels.size > 1
    }

    /**
     * 获取支付渠道的优惠比例
     */
    fun getChannelDiscount(payChannel: String): Int {
        return channelDiscounts[payChannel] ?: 0
    }
    
    /**
     * 检查支付方式选择入口是否需要显示红点
     */
    fun shouldShowPaymentMethodRedDot(): Boolean {
        return SharePreferenceUtil.getBoolean(KEY_PAYMENT_METHOD_RED_DOT, true, "payment")
    }
    
    /**
     * 隐藏支付方式选择入口红点
     */
    fun hidePaymentMethodRedDot() {
        SharePreferenceUtil.putBoolean(KEY_PAYMENT_METHOD_RED_DOT, false, "payment")
    }

    /**
     * 获取支付渠道显示名称
     */
    fun getPaymentChannelDisplayName(payChannel: String): String {

        cachedPayChannelList?.forEach { item ->
            if (item.payChannel == payChannel) {
                return item.title ?: payChannel
            }
        }

        return when (payChannel) {
            PAY_CHANNEL_GP -> "Google Play"
            else -> payChannel
        }
    }
    
    /**
     * 获取支付渠道图标资源
     */
    fun getPaymentChannelIcon(payChannel: String): Int {
        return when (payChannel) {
            PAY_CHANNEL_GP -> com.score.callmetest.R.drawable.ic_google
            else -> com.score.callmetest.R.drawable.coin
        }
    }

    /**
     * 清空支付相关本地状态和内存数据
     */
    fun clear() {
        payChannelExtras = null
        cachedPayChannelList = null
        availablePayChannels.clear()
        channelDiscounts.clear()
    }
} 