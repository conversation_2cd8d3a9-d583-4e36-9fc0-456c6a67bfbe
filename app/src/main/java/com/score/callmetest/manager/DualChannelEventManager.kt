package com.score.callmetest.manager

import android.util.Log
import androidx.lifecycle.LifecycleOwner
import com.score.callmetest.util.EventBus
import java.util.concurrent.ConcurrentHashMap

/**
 * 双通道事件去重管理器
 *
 * 功能：
 * 1. 在EventBus层面进行事件去重，避免重复处理
 * 2. 基于事件内容和时间戳进行去重判断
 * 3. 确保同一个事件只处理一次，提供双通道可靠性
 * 4. 支持生命周期感知的观察者模式
 *
 * 推荐使用方式：
 * - 使用 DualChannelEventManager.observeXxx() 替代 SocketManager.instance.observeXxx()
 * - 使用 DualChannelEventManager.observeXxx() 替代 RongCloudCommandManager.instance.observeXxx()
 * - 这样可以确保双通道事件的自动去重，避免重复处理
 */
object DualChannelEventManager {

    private const val TAG = "DualChannelEvent"
    private const val DUPLICATE_TIME_THRESHOLD = 2000L // 2秒内的相同事件认为是重复的

    // 事件去重缓存：key为事件标识，value为时间戳
    private val eventCache = ConcurrentHashMap<String, Long>()

    // 清理过期事件的间隔（5分钟）
    private val cleanupInterval = 5 * 60 * 1000L
    private var lastCleanupTime = System.currentTimeMillis()

    /**
     * 初始化双通道事件管理器
     */
    fun initialize() {
        // 初始化时清理一次过期事件
        cleanupExpiredEvents()
        Log.d(TAG, "双通道事件管理器初始化完成")
    }

    /**
     * 检查并记录事件，如果是重复事件则返回false
     * 这个方法供SocketManager和RongCloudCommandManager调用
     */
    fun shouldProcessEvent(eventKey: String): Boolean {
        synchronized(eventCache) {
            val currentTime = System.currentTimeMillis()
            val lastEventTime = eventCache[eventKey]

            // 如果是新事件或者距离上次处理超过阈值，则处理
            if (lastEventTime == null || (currentTime - lastEventTime) > DUPLICATE_TIME_THRESHOLD) {
                eventCache[eventKey] = currentTime

                // 定期清理过期事件
                if (currentTime - lastCleanupTime > cleanupInterval) {
                    cleanupExpiredEvents()
                    lastCleanupTime = currentTime
                }

                Log.d(TAG, "允许处理事件: $eventKey")
                return true
            }

            Log.d(TAG, "跳过重复事件: $eventKey")
            return false
        }
    }

    /**
     * 监听onCall事件（双通道自动去重）
     * 替代 SocketManager.instance.observeOnCall() 和 RongCloudCommandManager.instance.observeOnCall()
     *
     * @param owner 生命周期拥有者
     * @param onEvent 事件回调，只会被调用一次（即使Socket和融云都收到相同事件）
     */
    fun observeOnCall(owner: LifecycleOwner, onEvent: (OnCallMessage) -> Unit) {
        EventBus.observe(owner, OnCallMessage::class.java, onEvent = onEvent)
    }

    /**
     * 监听onHangUp事件（双通道自动去重）
     * 替代 SocketManager.instance.observeOnHangUp() 和 RongCloudCommandManager.instance.observeOnHangUp()
     *
     * @param owner 生命周期拥有者
     * @param onEvent 事件回调，只会被调用一次（即使Socket和融云都收到相同事件）
     */
    fun observeOnHangUp(owner: LifecycleOwner, onEvent: (OnHangUpMessage) -> Unit) {
        EventBus.observe(owner, OnHangUpMessage::class.java, onEvent = onEvent)
    }

    /**
     * 监听onPickUp事件（双通道自动去重）
     * 替代 SocketManager.instance.observeOnPickUp() 和 RongCloudCommandManager.instance.observeOnPickUp()
     *
     * @param owner 生命周期拥有者
     * @param onEvent 事件回调，只会被调用一次（即使Socket和融云都收到相同事件）
     */
    fun observeOnPickUp(owner: LifecycleOwner, onEvent: (OnPickUpMessage) -> Unit) {
        EventBus.observe(owner, OnPickUpMessage::class.java, onEvent = onEvent)
    }

    /**
     * 监听estimatedHangUpTime事件（双通道自动去重）
     * 替代 SocketManager.instance.observeEstimatedHangUpTime() 和 RongCloudCommandManager.instance.observeEstimatedHangUpTime()
     *
     * @param owner 生命周期拥有者
     * @param onEvent 事件回调，只会被调用一次（即使Socket和融云都收到相同事件）
     */
    fun observeEstimatedHangUpTime(owner: LifecycleOwner, onEvent: (EstimatedHangUpTimeMessage) -> Unit) {
        EventBus.observeDebounced(owner, EstimatedHangUpTimeMessage::class.java, onEvent = onEvent)
    }

    fun observeFlashChatFreeTimes(owner: LifecycleOwner, onEvent: (FlashChatFreeTimesMessage) -> Unit) {
        EventBus.observe(owner, FlashChatFreeTimesMessage::class.java, onEvent = onEvent)
    }

    fun observeFreeCallDuration(owner: LifecycleOwner, onEvent: (FreeCallDurationMessage) -> Unit) {
        EventBus.observe(owner, FreeCallDurationMessage::class.java, onEvent = onEvent)
    }

    /**
     * 监听availableCoins事件（双通道自动去重）
     * 替代 SocketManager.instance.observeAvailableCoins() 和 RongCloudCommandManager.instance.observeAvailableCoins()
     *
     * @param owner 生命周期拥有者
     * @param onEvent 事件回调，只会被调用一次（即使Socket和融云都收到相同事件）
     */
    fun observeAvailableCoins(owner: LifecycleOwner, onEvent: (AvailableCoinsMessage) -> Unit) {
        EventBus.observe(owner, AvailableCoinsMessage::class.java, onEvent = onEvent)
    }

    /**
     * 监听onChat事件（双通道自动去重）
     * 替代 SocketManager.instance.observeOnChat() 和 RongCloudCommandManager.instance.observeOnChat()
     *
     * 注意：聊天消息已经在DualChannelMessageManager中处理去重，这里提供统一接口
     *
     * @param owner 生命周期拥有者
     * @param onEvent 事件回调，只会被调用一次（即使Socket和融云都收到相同事件）
     */
    fun observeOnChat(owner: LifecycleOwner, onEvent: (OnChatMessage) -> Unit) {
        EventBus.observe(owner, OnChatMessage::class.java, onEvent = onEvent)
    }

    /**
     * 监听onGiftAsk事件（融云专用，自动去重）
     * 替代 RongCloudCommandManager.instance.observeOnGiftAsk()
     *
     * @param owner 生命周期拥有者
     * @param onEvent 事件回调，礼物索要事件仅通过融云发送
     */
    fun observeOnGiftAsk(owner: LifecycleOwner, onEvent: (OnGiftAskMessage) -> Unit) {
        EventBus.observe(owner, OnGiftAskMessage::class.java, onEvent = onEvent)
    }

    /**
     * 监听充值订单状态 (只有socket发送)
     *
     * @param owner 生命周期拥有者
     * @param onEvent 事件回调，充值订单状态件仅通过socket发送
     */
    fun observeRechargeOrderStatus(owner: LifecycleOwner, onEvent: (RechargeOrderStatusMessage) -> Unit) {
        EventBus.observe(owner, RechargeOrderStatusMessage::class.java, onEvent = onEvent)
    }

    /**
     * 清理过期的事件缓存（线程安全）
     */
    private fun cleanupExpiredEvents() {
        synchronized(eventCache) {
            val currentTime = System.currentTimeMillis()
            val expireTime = currentTime - cleanupInterval
            val iterator = eventCache.entries.iterator()
            var cleanedCount = 0

            while (iterator.hasNext()) {
                val entry = iterator.next()
                if (entry.value < expireTime) {
                    iterator.remove()
                    cleanedCount++
                }
            }

            if (cleanedCount > 0) {
                Log.d(TAG, "清理了 $cleanedCount 个过期事件")
            }
        }
    }
    
    /**
     * 清理所有事件缓存
     */
    fun cleanup() {
        synchronized(eventCache) {
            eventCache.clear()
            Log.d(TAG, "双通道事件管理器已清理")
        }
    }
}
