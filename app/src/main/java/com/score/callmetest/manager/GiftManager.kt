package com.score.callmetest.manager

import com.score.callmetest.network.GetGiftListRequest
import com.score.callmetest.network.GiftInfo
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import timber.log.Timber

object GiftManager {
    var giftCache: List<GiftInfo>? = null

    suspend fun getGiftList(forceRefresh: Boolean = false): List<GiftInfo> = withContext(Dispatchers.IO) {
        if (!forceRefresh && giftCache != null) return@withContext giftCache!!
        val types = listOf(1)
        val resp = RetrofitUtils.dataRepository.getGiftListPostV2(GetGiftListRequest(types))
        val list = if(resp is NetworkResult.Success){
            (resp.data ?: emptyList()).sortedBy{ it.coinPrice?:0.0}
        }else{
            if(resp is NetworkResult.Error){
                Timber.e("getGiftList error: $resp")
            }
            emptyList()
        }
        giftCache = list
        list
    }

    fun getGiftList(
        scope: CoroutineScope,
        forceRefresh: Boolean = false,
        onSuccess: (List<GiftInfo>) -> Unit,
        onError: (Throwable) -> Unit = {}
    ) {
        scope.launch(Dispatchers.IO) {
            try {
                val list = getGiftList(forceRefresh)
                ThreadUtils.runOnMain { onSuccess(list) }
            } catch (e: Exception) {
                ThreadUtils.runOnMain { onError(e) }
            }
        }
    }

    suspend fun loadAllGift() {
        if (giftCache.isNullOrEmpty()) {
            try {
                getGiftList(true)
            } catch (e: Exception) {
            }
        }
    }

    fun loadAllGift(
        scope: CoroutineScope,
        onSuccess: (List<GiftInfo>) -> Unit,
        onError: (Throwable) -> Unit = {}
    ) {
        getGiftList(scope, true, onSuccess, onError)
    }

    fun clearCache() {
        giftCache = null
    }
} 