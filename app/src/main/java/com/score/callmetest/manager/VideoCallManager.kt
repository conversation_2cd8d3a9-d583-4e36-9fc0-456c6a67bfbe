package com.score.callmetest.manager

import androidx.fragment.app.FragmentActivity
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.network.CreateChannelRequest
import com.score.callmetest.network.CreateChannelResponse
import com.score.callmetest.network.HangUpRequest
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.PickUpRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.ui.videocall.CallEndEvaluateFragmentDialog
import com.score.callmetest.ui.videocall.CallEndFreeFragmentDialog
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.DialogUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.util.CustomUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.UUID


enum class VideoCallState {
    OUTGOING,   // 主动呼叫
    INCOMING,   // 被呼叫
    ONGOING,    // 通话中
    ENDED       // 通话结束
}

enum class HangUpReason(val value: Int) {
    NORMAL(1),           // 常规
    NO_COINS(2),         // 金币不足
    CL_EXCEPTION(3),     // 客户端异常
    CL_CALL_TIMEOUT(4),  // 呼叫超时
    CL_REMOTE_USER_LEFT(5), // 对方退出
    SER_EXCEPTION(6),    // 服务端错误
    NET_EXCEPTION(7)     // 网络错误
}

// 通话类型 1-私人电话 2-匹配电话
enum class CallType(val value: Int) {
    PRIVATE(1), // 私人电话
    MATCH(2)    // 匹配电话
}

// callSource字典值
enum class CallSource(val value: Int) {
    CONVERSATION(1),              // 会话界面
    JSAPI(2),                     // 页端调用
    MATCH_RESULT(3),              // 匹配结果页
    POPULAR_WALL(4),              // 主播/用户墙
    ANOTHER_ANCHOR(5),            // 呼叫失败时推荐另一批
    DETAIL_PAGE(6),               // 主播主页调用
    DETAIL_VIDEO_PAGE(7),         // 主播视频详情页
    MISS_CALL_PAGE(8),            // miss call 弹框回拨
    CONNECTION_ERROR_CALL_BACK(9),// 用户网络不好弹框回拨
    MEDIA_WALL(10),               // 视频流
    CALLS_PAGE(11),               // 通话记录
    FAST_MATCH(12),               // 快速匹配
    AUTO_CALL(13),                // auto call
    MULTIPLE(14),                 // 多人连线
    RECOMMEND_GODDESS(15)         // 女神推荐
}

object VideoCallManager {
    fun createChannel(
        scope: CoroutineScope,
        toUserId: String,
        callType: CallType,
        callSource: CallSource,
        onSuccess: (CreateChannelResponse?) -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        val request = CreateChannelRequest(
            toUserId = toUserId,
            clientSessionId = UUID.randomUUID().toString(),
            callType = callType.value,
            callSource = callSource.value,
            supportVideoSdks = listOf(1)
        )
        scope.launch {
            try {
                val resp = RetrofitUtils.dataRepository.createChannel(request)
                if (resp is NetworkResult.Success) {
                    onSuccess.invoke(resp.data)
                } else {
                    onError.invoke(CustomUtils.getString(R.string.call_fail))
                }
            } catch (e: Exception) {
                onError.invoke(CustomUtils.getString(R.string.network_error))
            }
        }
    }

    /**
     * 接听
     */
    fun pickUp(
        scope: CoroutineScope,
        channelName: String,
        onSuccess: () -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        val request = PickUpRequest(channelName = channelName)
        scope.launch {
            try {
                val resp = RetrofitUtils.dataRepository.pickUp(request)
                if (resp is NetworkResult.Success && resp.data == true) {
                    onSuccess.invoke()
                } else {
                    Timber.e("Pickup fail--$resp")
                    onError.invoke(CustomUtils.getString(R.string.pickup_fail))
                }
            } catch (e: Exception) {
                onError.invoke("${CustomUtils.getString(R.string.network_error)}: ${e.message}")
            }
        }
    }

    /**
     * 挂断
     */
    fun hangUp(
        scope: CoroutineScope = CoroutineScope(Dispatchers.IO),
        channelName: String,
        hangUpReason: HangUpReason,
        oppositeUserId: String,
        remark: String?,
        onSuccess: () -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        val request = HangUpRequest(
            channelName = channelName,
            handUpReason = hangUpReason.value,
            requestNo = oppositeUserId,
            remark = remark,
        )
        scope.launch(Dispatchers.IO) {
            try {
                val resp = RetrofitUtils.dataRepository.hangUp(request)
                // 刷新对方状态
                UserInfoManager.loadOnlineStatus(
                    scope = this,
                    userId = oppositeUserId,
                    callback = { msg, throwable ->

                    }
                )
                scope.launch(Dispatchers.Main) {
                    if (resp is NetworkResult.Success && resp.data == true) {
                        onSuccess.invoke()
                    } else {
                        Timber.e("Hangup fail..$resp")
                        onError.invoke(CustomUtils.getString(R.string.hangup_fail))
                    }
                }
            } catch (e: Exception) {
                scope.launch(Dispatchers.Main) {
                    onError.invoke("${CustomUtils.getString(R.string.network_error)}: ${e.message}")
                }
            }
        }
    }

    /**
     * 使用 createChannel 返回的数据加入频道（推荐最优实现）
     */
    fun joinChannelWithResponseData(data: CreateChannelResponse) {
        val rtcToken = data.rtcToken
        val channelName = data.channelName
        val fromUserId = data.fromUserId?.toString() ?: UserInfoManager.myUserInfo?.userId ?: ""
        if (rtcToken.isNullOrEmpty() || channelName.isNullOrEmpty()) {
            return
        }
        AgodaUtils.joinChannelWithUserAccount(rtcToken, channelName, fromUserId)
    }

    private var lastEndDialog: BottomSheetDialogFragment? = null
    fun showEndDialog(
        type: Int = 0,
        userId: String,
        channelName: String,
    ) {
        DialogUtils.safeDismissDialogFragmentOnMainThread(lastEndDialog)
        ThreadUtils.runOnMainDelayed(1000) {
            UserInfoManager.getUserInfo(userId) { userInfo ->
                if (userInfo != null) {
                    val activity = ActivityUtils.getTopActivity()
                    if (activity is FragmentActivity) {
                        if (type == 0) {
                            // 金币大于主播每分钟价格展示评价
                            lastEndDialog = CallEndEvaluateFragmentDialog(
                                userInfo = userInfo,
                                channelName = channelName,
                                onDismiss = {
                                    lastEndDialog = null
                                }
                            ).apply {
                                show(activity.supportFragmentManager, "call_end")
                            }
                        } else {
                            // 其他情况展示金币弹框
                            lastEndDialog = CallEndFreeFragmentDialog(userInfo = userInfo,
                                onDismiss = {
                                    lastEndDialog = null
                                }).apply {
                                show(activity.supportFragmentManager, "call_end")
                            }
                        }
                    }
                }
            }
        }
    }
}