package com.score.callmetest.manager

import com.score.callmetest.network.DataRepository
import com.score.callmetest.network.GetVisitorListReq
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.VisitorRecord
import com.score.callmetest.util.ThreadUtils
import timber.log.Timber

/**
 * 来访者数据管理器
 * 负责管理来访者列表的数据加载和缓存
 */
object VisitorManager {
    
    private const val TAG = "VisitorManager"
    
    /**
     * 来访者列表加载回调接口
     */
    interface VisitorPageCallback {
        /**
         * 加载成功回调
         * @param list 来访者记录列表
         */
        fun onSuccess(list: List<VisitorRecord>)
        
        /**
         * 加载失败回调
         * @param errorMsg 错误信息
         */
        fun onError(errorMsg: String)
        
        /**
         * 加载状态变化回调
         * @param isLoading 是否正在加载
         */
        fun onLoading(isLoading: Boolean)
    }
    
    /**
     * 加载来访者列表
     * @param pageSize 每页大小
     * @param page 页码（从1开始）
     * @param callback 回调接口
     */
    fun loadVisitorList(pageSize: Int, page: Int, callback: VisitorPageCallback) {
        Timber.tag(TAG).d("加载来访者列表: pageSize=$pageSize, page=$page")
        
        callback.onLoading(true)
        
        // 在IO线程中执行网络请求
        ThreadUtils.runOnIO {
            try {
                val request = GetVisitorListReq(
                    size = pageSize,
                    page = page
                )
                
                val response = RetrofitUtils.dataRepository.getVisitorList(request)
                
                when (response) {
                    is NetworkResult.Success -> {
                        val visitorList = response.data ?: emptyList()
                        
                        Timber.tag(TAG).d("来访者列表加载成功: 获取到${visitorList.size}条记录")
                        
                        // 切换到主线程回调成功结果
                        ThreadUtils.runOnMain {
                            callback.onLoading(false)
                            callback.onSuccess(visitorList)
                        }
                    }
                    is NetworkResult.Error -> {
                        val errorMsg = response.message ?: "网络请求失败"
                        Timber.tag(TAG).e("来访者列表加载失败: $errorMsg")
                        
                        // 切换到主线程回调错误结果
                        ThreadUtils.runOnMain {
                            callback.onLoading(false)
                            callback.onError(errorMsg)
                        }
                    }
                }
            } catch (e: Exception) {
                val errorMsg = e.message ?: "未知错误"
                Timber.tag(TAG).e("来访者列表加载异常: $errorMsg", e)
                
                // 切换到主线程回调错误结果
                ThreadUtils.runOnMain {
                    callback.onLoading(false)
                    callback.onError(errorMsg)
                }
            }
        }
    }
}
