package com.score.callmetest.manager

import android.annotation.TargetApi
import android.app.Activity
import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.media.AudioAttributes
import android.media.AudioDeviceInfo
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.media.AudioManager.OnAudioFocusChangeListener
import android.media.MediaPlayer
import android.media.MediaPlayer.OnCompletionListener
import android.media.MediaPlayer.OnPreparedListener
import android.media.MediaPlayer.OnSeekCompleteListener
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import android.os.PowerManager.WakeLock
import android.util.Log
import android.view.WindowManager
import androidx.core.net.toUri
import com.score.callmetest.CallmeApplication
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.io.FileInputStream
import java.io.IOException


object AudioPlayManager {

    private const val TAG: String = "AudioPlayManager"

    private var mMediaPlayer: MediaPlayer? = null
    private var _playListener: IAudioPlayListener? = null
    private var mUriPlaying: Uri? = null
    private var mAudioManager: AudioManager? = null
    private var _wakeLock: WakeLock? = null
    private var afChangeListener: OnAudioFocusChangeListener? = null
    private var audioFocusRequest: AudioFocusRequest? = null
    private val handler: Handler = Handler(Looper.getMainLooper())
    private val mLock = Any()
    private var isLooping = false
    private var stopPlay = true

    fun startPlay(audioUri: Uri?, scope: CoroutineScope, playListener: IAudioPlayListener?, isLoop: Boolean = false) {
        if (audioUri == null) {
            Timber.tag(TAG).e("startPlay: audioUri is null")
            return
        }
        
        Timber.tag(TAG).d("startPlay: audioUri=$audioUri, isLoop=$isLoop")
        scope.launch(Dispatchers.IO) {
            synchronized(mLock) {
                // 停止当前播放并重置
                stopCurrentPlayback()

                // 设置播放参数
                setupPlaybackParameters(audioUri, playListener, isLoop)

                // 初始化音频管理
                initializeAudioManager()

                // 创建并配置 MediaPlayer
                if (!createAndConfigureMediaPlayer(audioUri, isLoop)) {
                    handlePlaybackError(audioUri, playListener)
                    return@launch
                }

                // 开始异步准备
                startAsyncPreparation()
            }
        }
    }

    private fun stopCurrentPlayback() {
        _playListener?.onStop(mUriPlaying)
        resetMediaPlayer()
    }

    private fun setupPlaybackParameters(audioUri: Uri, playListener: IAudioPlayListener?, isLoop: Boolean) {
        _playListener = playListener
        mUriPlaying = audioUri
        isLooping = isLoop
        stopPlay = false
    }

    private fun initializeAudioManager() {
        if (mAudioManager == null) {
            mAudioManager = CallmeApplication.context.getSystemService(Context.AUDIO_SERVICE) as AudioManager?
        }
        
        setupAudioFocusListener()
        requestAudioFocus()
        muteAudioFocus(mAudioManager, true)
    }

    private fun setupAudioFocusListener() {
        afChangeListener = OnAudioFocusChangeListener { focusChange ->
            handleAudioFocusChange(focusChange)
        }
    }

    private fun handleAudioFocusChange(focusChange: Int) {
        Timber.tag(TAG).d("Audio focus change: $focusChange")
        
        when (focusChange) {
            AudioManager.AUDIOFOCUS_LOSS -> {
                abandonAudioFocus()
                handler.post {
                    synchronized(mLock) {
                        _playListener?.onComplete(mUriPlaying)
                        _playListener = null
                        reset()
                    }
                }
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                mMediaPlayer?.pause()
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                mMediaPlayer?.setVolume(0.3f, 0.3f)
            }
            AudioManager.AUDIOFOCUS_GAIN -> {
                mMediaPlayer?.let { player ->
                    if (!player.isPlaying) {
                        player.start()
                    }
                    player.setVolume(1.0f, 1.0f)
                }
            }
        }
    }

    private fun createAndConfigureMediaPlayer(audioUri: Uri, isLoop: Boolean): Boolean {
        return try {
            mMediaPlayer = MediaPlayer()
            
            // 设置监听器
            setupMediaPlayerListeners()
            
            // 设置数据源
            setupDataSource(audioUri)
            
            // 配置音频属性
            configureAudioAttributes()
            
            true
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to create and configure MediaPlayer")
            false
        }
    }

    private fun setupMediaPlayerListeners() {
        mMediaPlayer?.let { player ->
            player.setOnCompletionListener { mp ->
                synchronized(mLock) {
                    _playListener?.onComplete(mUriPlaying)
                    _playListener = null
                    reset()
                }
            }
            
            player.setOnErrorListener { mp, what, extra ->
                Timber.tag(TAG).e("MediaPlayer error: what=$what, extra=$extra")
                synchronized(mLock) {
                    reset()
                }
                true
            }
        }
    }

    private fun setupDataSource(audioUri: Uri) {
        var fis: FileInputStream? = null
        try {
            fis = FileInputStream(audioUri.path)
            mMediaPlayer?.setDataSource(fis.fd)
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to set data source")
            throw e
        } finally {
            fis?.closeQuietly()
        }
    }

    private fun configureAudioAttributes() {
        mMediaPlayer?.let { player ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                val attributes = AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION_SIGNALLING)
                    .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                    .setLegacyStreamType(AudioManager.STREAM_VOICE_CALL)
                    .build()
                player.setAudioAttributes(attributes)
            } else {
                player.setAudioStreamType(AudioManager.STREAM_MUSIC)
            }
        }
    }

    private fun startAsyncPreparation() {
        mMediaPlayer?.setOnPreparedListener { mp ->
            if (stopPlay) {
                return@setOnPreparedListener
            }
            
            synchronized(mLock) {
                try {
                    mMediaPlayer?.let { player ->
                        player.isLooping = isLooping
                        player.start()
                        _playListener?.onStart(mUriPlaying)
                        Timber.tag(TAG).d("Playback started successfully")
                    }
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "Failed to start playback after preparation")
                    handlePlaybackError(mUriPlaying, _playListener)
                }
            }
        }
        
        mMediaPlayer?.prepareAsync()
    }

    private fun handlePlaybackError(audioUri: Uri?, playListener: IAudioPlayListener?) {
        Timber.tag(TAG).e("Playback error for uri: $audioUri")
        playListener?.onStop(audioUri)
        _playListener = null
        reset()
    }

    private fun FileInputStream.closeQuietly() {
        try {
            close()
        } catch (e: IOException) {
            Timber.tag(TAG).e(e, "Failed to close FileInputStream")
        }
    }

    fun setPlayListener(listener: IAudioPlayListener?) {
        synchronized(mLock) {
            this._playListener = listener
        }
    }

    fun stopPlay() {
        synchronized(mLock) {
            stopPlay = true
            _playListener?.onStop(mUriPlaying)
            reset()
        }
    }

    private fun reset() {
        stopPlay = true
        resetMediaPlayer()
        resetAudioPlayManager()
    }

    private fun resetAudioPlayManager() {
        if (mAudioManager != null) {
            mAudioManager!!.setMode(AudioManager.MODE_NORMAL)
            abandonAudioFocus()
            muteAudioFocus(mAudioManager, false)
        }
        mAudioManager = null
        _wakeLock = null
        mUriPlaying = null
        _playListener = null
    }

    @TargetApi(Build.VERSION_CODES.O)
    private fun requestAudioFocus() {
        if (mAudioManager == null || afChangeListener == null) return

        val result = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            audioFocusRequest =
                AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK)
                    .setAudioAttributes(
                        AudioAttributes.Builder()
                            .setUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION_SIGNALLING)
                            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                            .build()
                    )
                    .setOnAudioFocusChangeListener(afChangeListener!!)
                    .setAcceptsDelayedFocusGain(false)
                    .setWillPauseWhenDucked(false)
                    .build()
            mAudioManager!!.requestAudioFocus(audioFocusRequest!!)
        } else {
            mAudioManager!!.requestAudioFocus(
                afChangeListener,
                AudioManager.STREAM_VOICE_CALL,
                AudioManager.AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK
            )
        }
        Timber.tag(TAG).d("Audio focus request result: $result")
    }

    private fun abandonAudioFocus() {
        if (mAudioManager == null) return

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && audioFocusRequest != null) {
            mAudioManager!!.abandonAudioFocusRequest(audioFocusRequest!!)
            audioFocusRequest = null
        } else if (afChangeListener != null) {
            mAudioManager!!.abandonAudioFocus(afChangeListener)
        }
        afChangeListener = null
        Timber.tag(TAG).d("Audio focus abandoned")
    }

    private fun resetMediaPlayer() {
        synchronized(mLock) {
            mMediaPlayer?.let { player ->
                try {
                    if (player.isPlaying) {
                        player.stop()
                    }
                    player.reset()
                    player.release()
                    Timber.tag(TAG).d("MediaPlayer reset successfully")
                } catch (e: IllegalStateException) {
                    Timber.tag(TAG).e(e, "Error stopping MediaPlayer")
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "Unexpected error resetting MediaPlayer")
                } finally {
                    mMediaPlayer = null
                    isLooping = false
                }
            }
        }
    }

    fun getPlayingUri(): Uri? {
        synchronized(mLock) {
            return mUriPlaying ?: Uri.EMPTY
        }
    }

    @TargetApi(Build.VERSION_CODES.FROYO)
    private fun muteAudioFocus(audioManager: AudioManager?, bMute: Boolean) {
        if (audioManager == null || afChangeListener == null) return
        
        synchronized(mLock) {
            try {
                if (bMute) {
                    val result = audioManager.requestAudioFocus(
                        afChangeListener,
                        AudioManager.STREAM_MUSIC,
                        AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
                    )
                    if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                        Timber.tag(TAG).d("Audio focus for muting granted")
                    } else {
                        Timber.tag(TAG).w("Audio focus for muting denied")
                    }
                } else {
                    audioManager.abandonAudioFocus(afChangeListener)
                    Timber.tag(TAG).d("Audio focus for muting abandoned")
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Error in muteAudioFocus: bMute=$bMute")
            }
        }
    }

    /**
     * 检查AudioPlayManager是否处于通道正常的状态。
     *
     * @param context 上下文
     * @return 是否处于通道正常的状态
     */
    fun isInNormalMode(context: Context): Boolean {
        if (context == null) {
            Timber.tag(TAG).w("isInNormalMode: context is null")
            return false
        }
        
        synchronized(mLock) {
            try {
                if (mAudioManager == null) {
                    mAudioManager = context.applicationContext
                        .getSystemService(Context.AUDIO_SERVICE) as AudioManager?
                }
                
                return mAudioManager?.mode == AudioManager.MODE_NORMAL
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Error checking audio mode")
                return false
            }
        }
    }

    private var isVOIPMode = false

    fun isInVOIPMode(context: Context?): Boolean {
        return isVOIPMode
    }

    fun setInVoipMode(isVOIPMode: Boolean) {
        this.isVOIPMode = isVOIPMode
    }

    fun playVideoRingtone(context: Context,scope: CoroutineScope, assetFileName: String = "ring.mp3") {
        val cacheFile = copyAssetToCache(context, assetFileName)
        if (cacheFile == null || !cacheFile.exists()) {
            Timber.tag(TAG).e("无法创建或找到缓存文件: $assetFileName")
            return
        }
        startPlay(cacheFile.toUri(), scope,null, true)
    }

    private fun copyAssetToCache(context: Context, assetFileName: String): File? {
        return try {
            val inputStream = context.assets.open(assetFileName)
            val cacheFile = File(context.cacheDir, assetFileName)

            // 如果文件已存在且大小不为0，直接返回
            if (cacheFile.exists() && cacheFile.length() > 0) {
                Timber.tag(TAG)
                    .d("缓存文件已存在: ${cacheFile.absolutePath}, 大小: ${cacheFile.length()} bytes")
                inputStream.close()
                return cacheFile
            }

            // 复制文件到 cache 目录
            cacheFile.outputStream().use { outputStream ->
                inputStream.copyTo(outputStream)
            }
            inputStream.close()

            Timber.tag(TAG)
                .d("文件复制成功: ${cacheFile.absolutePath}, 大小: ${cacheFile.length()} bytes")
            cacheFile
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "复制 assets 文件到 cache 失败: $assetFileName")
            null
        }
    }

    fun isPlaying(): Boolean {
        synchronized(mLock) {
            return mMediaPlayer != null && mMediaPlayer!!.isPlaying()
        }
    }

    interface IAudioPlayListener {
        fun onStart(uri: Uri?)

        fun onStop(uri: Uri?)

        fun onComplete(uri: Uri?)
    }

}