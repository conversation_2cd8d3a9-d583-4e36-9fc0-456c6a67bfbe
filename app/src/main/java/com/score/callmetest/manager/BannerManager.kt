package com.score.callmetest.manager

import android.content.Context
import android.net.Uri
import android.os.Bundle
import androidx.core.net.toUri
import com.score.callmetest.R
import com.score.callmetest.network.BannerInfoResponse
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.ui.web.WebViewActivity
import com.score.callmetest.util.ActivityUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.atomic.AtomicBoolean

object BannerManager {

    const val VIP_BANNER = "vip_banner"
    // 缓存的Banner数据
    private var bannerList: List<BannerInfoResponse>? = null

    // 请求状态管理：防止重复请求
    private var isLoading = AtomicBoolean(false)

    // 等待回调的队列：当正在请求时，新的请求会加入队列等待结果
    private val pendingCallbacks = CopyOnWriteArrayList<(List<BannerInfoResponse>?) -> Unit>()

    /**
     * 初始化Banner管理器，预加载Banner数据
     */
    fun init() {
        Timber.d("BannerManager init() called")
        loadBannerInfo()
    }

    /**
     * 加载Banner信息（内部使用）
     */
    private fun loadBannerInfo() {
        getBannerInfo(
            scope = CoroutineScope(Dispatchers.IO),
            callback = { data ->
                Timber.d("BannerManager loadBannerInfo completed, data size: ${data?.size}")
            }
        )
    }

    /**
     * 获取VIP Banner信息
     * 根据用户VIP状态决定是否返回Banner
     * 
     * @return VIP Banner信息，如果用户已是VIP则返回null
     */
    fun getVipBanner(): BannerInfoResponse? {
        // 检查是否为审核包
        if (StrategyManager.isReviewPkg()) {
            return null
        }

        return BannerInfoResponse(
            type = 3,
            bizType = "3",
            isFirstLottery = false,
            jumpUrl = VIP_BANNER,
            pic = "android.resource://${R::class.java.`package`.name}/${R.drawable.vip_banner}"
        )
    }

    /**
     * 获取Banner信息
     * 优化后的版本：
     * 1. 防止重复请求
     * 2. 请求队列管理
     * 3. 支持强制刷新
     *
     * @param scope 协程作用域
     * @param callback 结果回调
     * @param forceRefresh 是否强制刷新缓存，默认false
     */
    fun getBannerInfo(
        scope: CoroutineScope,
        callback: (List<BannerInfoResponse>?) -> Unit,
        forceRefresh: Boolean = false
    ) {
        if (StrategyManager.isReviewPkg()) {
            callback(emptyList())
            return
        }

        // 检查缓存是否有效（除非强制刷新）
        if (bannerList != null && !forceRefresh) {
            Timber.d("BannerManager returning cached data")
            callback(bannerList)
            return
        }

        // 如果正在加载，将回调加入队列
        if (isLoading.get()) {
            Timber.d("BannerManager request in progress, adding callback to queue")
            pendingCallbacks.add(callback)
            return
        }

        // 开始新的请求
        isLoading.set(true)
        pendingCallbacks.add(callback)

        Timber.d("BannerManager starting new request, forceRefresh: $forceRefresh")

        scope.launch(Dispatchers.IO) {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.getBannerInfo()
                }
                val data = if (resp is NetworkResult.Success) {
                    resp.data
                } else {
                    Timber.i("BannerManager request failed: $resp")
                    null
                }

                // 更新缓存
                bannerList = data

                // 添加VIP Banner（如果用户不是VIP且有可用商品）
                val vipBanner = getVipBanner()
                if (vipBanner != null) {
                    (bannerList as MutableList).add(0, vipBanner)
                }


                // 通知所有等待的回调
                scope.launch(Dispatchers.Main) {
                    val callbacks = pendingCallbacks.toList()
                    pendingCallbacks.clear()

                    Timber.d("BannerManager request completed, notifying ${callbacks.size} callbacks")
                    callbacks.forEach { it.invoke(data) }
                }
            } catch (e: Exception) {
                Timber.e(e, "BannerManager request exception")

                // 通知所有等待的回调
                scope.launch(Dispatchers.Main) {
                    val callbacks = pendingCallbacks.toList()
                    pendingCallbacks.clear()

                    callbacks.forEach { it.invoke(null) }
                }
            } finally {
                isLoading.set(false)
            }
        }
    }

    /**
     * 清除缓存，强制下次请求重新加载
     */
    fun clearCache() {
        Timber.d("BannerManager cache cleared")
        bannerList = null
    }

    /**
     * 处理Banner点击跳转逻辑
     *
     * @param context 上下文
     * @param bannerInfoResponse Banner信息
     */
    fun handleBannerClick(context: Context, bannerInfoResponse: BannerInfoResponse) {
        Timber.d("handleBannerClick: $bannerInfoResponse")

        when(bannerInfoResponse.type) {
            1 -> {
                // 类型1：内部WebView跳转
                ActivityUtils.startActivity(
                    context,
                    WebViewActivity::class.java,
                    Bundle().apply {
                        putString("url", bannerInfoResponse.jumpUrl)
                    },
                )
            }
            3 -> {
                if (bannerInfoResponse.jumpUrl == VIP_BANNER) {
                    // 类型3：VIP Banner - 显示VIP对话框
                    VipManager.showVipDialog()
                }
            }
            4, 5 -> {
                // 类型4、5：外部浏览器跳转
                if (!bannerInfoResponse.jumpUrl.isNullOrEmpty()) {
                    var uri = bannerInfoResponse.jumpUrl.toUri()
                    /*if (bannerInfoResponse.type == 5) {
                        // 类型5需要添加sign参数
                        uri = uri.buildUpon().apply {
                            appendQueryParameter("sign", "")
                        }.build()
                    }*/
                    ActivityUtils.openExternalWeb(context, uri)
                }
            }
            else -> {
            }
        }
    }
}