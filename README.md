

文档库：https://nx8jquavdy0.feishu.cn/drive/shared/

Callme需求：https://nx8jquavdy0.feishu.cn/wiki/Li3YwDIaAibX6wkkHx5cRMaonch

马甲方舟接口文档：https://z5lovly9tf.feishu.cn/docs/doccnI2cvRvcxdDKRVbjtpUF2kf
接口对接文档：https://test-app.iohubonline.club/swagger-ui.html#!/255092147521069325126530647video45call/autoCallV2HangUpUsingPOST

风控方案：https://pcnfc9wstzar.feishu.cn/docx/LHBFdoZxPovG8dxuxPAcpY7KnIg

蓝湖：https://lanhuapp.com/web/#/item/project/stage?pid=5a36b955-fe56-4dae-8f5b-f2dac60005f1&image_id=b6ad75ad-9d01-4fc0-a5cd-628dcebbc5dd&tid=5ef4bacd-9d38-44f9-a924-8445f825eb80

后台测试数据修改：https://test-page.iohubonline.club/search-new/

通话挂断原因汇总: https://pcnfc9wstzar.feishu.cn/wiki/K791wMDOaijDAsk4TvGcuLwHnrg

一期用例安卓：https://pcnfc9wstzar.feishu.cn/sheets/A9U0sJvrqhZ4XxtjKQAcmOP3nFc?sheet=10fHKE

==============================
Adjust信息：
应用名称: Callme
包名: com.score.callme
App token: 3m37ne0t4mps
下单 token: av7eda
注册 token: 2jp2y3
登录 token: um5wb3
购买 token: rykn2x
==============================



